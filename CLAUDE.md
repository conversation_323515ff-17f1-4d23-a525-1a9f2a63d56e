# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🏗️ 專案架構

這是一個基於 Turborepo 的 Monorepo 結構的 HorizAI SaaS 平台：

```
apps/
├── backend/          # NestJS API 後端 (TypeScript)
└── frontend/         # Vue 3 前端應用 (TypeScript + Vite)
packages/
├── @auth/           # 認證相關共享套件
└── permissions/     # 權限管理套件
```

### 技術棧
- **後端**: NestJS + TypeScript + Prisma ORM + PostgreSQL + JWT認證 + Socket.IO + Swagger
- **前端**: Vue 3 (Composition API) + TypeScript + Vite + Pinia + Tailwind CSS + VeeValidate + Zod
- **認證**: HTTP-only Cookie + JWT + 多租戶權限系統
- **AI整合**: OpenAI + Anthropic + Google Gemini
- **即時通訊**: Socket.IO WebSocket

## 🚀 常用開發指令

### 整體專案管理
```bash
# 啟動前後端開發環境
pnpm dev

# 僅啟動後端
pnpm backend:dev

# 構建全專案
pnpm build

# 執行測試
pnpm test

# 型別檢查
pnpm typecheck

# 程式碼檢查
pnpm lint
```

### 資料庫管理
```bash
# 資料庫遷移
pnpm db:migrate

# 生成 Prisma 客戶端
pnpm db:generate

# 開啟資料庫管理界面
pnpm db:studio

# 權限同步到資料庫
cd apps/backend && pnpm db:sync-perms

# 完整資料庫重置（含種子資料）
cd apps/backend && pnpm prisma migrate reset

# 執行統一遷移工具（推薦）
cd apps/backend && pnpm migration:full
```

### 後端開發
```bash
cd apps/backend

# 開發模式啟動
pnpm start:dev

# 構建
pnpm build

# 生成 API 文件
pnpm docs:generate

# 資料庫相關
pnpm db:status          # 檢查資料庫狀態
pnpm db:seed           # 執行種子資料
pnpm migration:health  # 資料庫健康檢查

# 權限管理
pnpm db:scan-perms     # 掃描程式碼中的權限
pnpm db:sync-perms     # 同步權限到資料庫
pnpm db:perms-stats    # 權限統計報告
```

### 前端開發
```bash
cd apps/frontend

# 開發伺服器
pnpm dev

# 構建
pnpm build

# 預覽構建結果
pnpm preview

# 程式碼格式化
pnpm format
```

## 🔐 認證與權限架構

### 認證層級
1. **系統層**: 系統管理員 (存於 `system_users` 表)
2. **租戶層**: 租戶管理員與使用者 (存於 `tenant_users` 表)  
3. **工作區層**: 工作區內權限管理

### 權限系統核心概念
- **CASL**: 基於 CASL 的細粒度權限控制
- **權限範圍**: `SYSTEM`, `TENANT`, `WORKSPACE`
- **自動同步**: 程式碼中的權限宣告自動同步到資料庫
- **權限模板**: `apps/backend/prisma/templates/permissions.template.ts`
- **角色權限映射**: `apps/backend/prisma/templates/role-permissions.template.ts`

### 認證流程
- 使用 HTTP-only Cookie 存儲 JWT
- 前端透過 `@horizai/auth` 套件統一認證
- 後端使用 `JwtAuthGuard` + `PoliciesGuard` 雙重守衛

## 📐 開發規範

### 命名慣例
- 資料夾: `kebab-case`
- Vue元件: `PascalCase.vue`
- 服務檔案: `xxx.service.ts`
- 狀態檔案: `xxx.store.ts`
- 模型檔案: `xxx.model.ts`
- 變數函式: `camelCase`
- 介面: `I` 前綴，如 `IPhoto`

### 前端開發重點
- 使用 `<script setup lang="ts">` 語法
- 表單驗證: VeeValidate + Zod
- 狀態管理: Pinia
- UI組件: Tailwind CSS + Shadcn/ui (Vue版)
- 路由守衛: 基於權限的路由保護
- **重要**: 認證初始化在 `main.ts` 中統一處理，組件內禁止重複執行認證邏輯

### 後端開發重點
- 所有API需要 JWT + 權限驗證
- 使用 `@UseGuards(JwtAuthGuard, PoliciesGuard)`
- Controller 需要 `@ApiTags()` Swagger註解
- 實作 tenant-aware 查詢邏輯
- **重要**: 
  - 系統管理員使用 `this.prisma.system_users`
  - 租戶使用者使用 `this.prisma.tenant_users`
  - 避免使用已移除的 `this.prisma.users`

### 資料庫變更流程
1. 修改 `prisma/schema.prisma`
2. 執行 `pnpm db:migrate` 生成遷移
3. 如有權限變更，更新權限模板檔案
4. 執行 `pnpm db:sync-perms` 同步權限

## 🔄 AI整合架構

- **多模型支援**: OpenAI、Anthropic、Google Gemini
- **金鑰管理**: 分租戶的AI金鑰配置
- **用量控制**: 基於令牌的配額管理
- **工作流程**: AI工作流程設計器
- **影像分析**: 支援圖片AI分析功能

## 📊 資料庫架構重點

### 多租戶分離
- **系統用戶**: `system_users` (系統管理員)
- **租戶用戶**: `tenant_users` (租戶內使用者)
- **工作區**: `workspaces` (租戶下的工作空間)
- **專案**: tenant-scoped 專案管理

### 權限系統表
- `permissions`: 權限定義
- `roles`: 角色定義  
- `role_permissions`: 角色權限映射
- `system_user_roles`: 系統用戶角色
- `tenant_user_roles`: 租戶用戶角色

## 💡 常見開發任務

### 新增API端點
1. 在對應模組建立 Controller
2. 實作 Service 業務邏輯
3. 添加權限檢查裝飾器
4. 更新權限模板（如需要）
5. 執行權限同步

### 新增前端頁面
1. 在 `views/` 建立頁面組件
2. 在 `router/` 配置路由
3. 添加路由守衛（如需要）
4. 實作對應的 API 服務
5. 建立狀態管理（如需要）

### 權限變更
1. 修改 `permissions.template.ts`
2. 更新 `role-permissions.template.ts`
3. 執行 `pnpm db:sync-perms`
4. 測試前後端權限檢查

## 🔧 環境設定

### 必要環境變數 (backend/.env)
```
DATABASE_URL="postgresql://user:password@localhost:5432/db"
JWT_ACCESS_SECRET="your-jwt-secret"
ALLOWED_ORIGINS="http://localhost:5173"
```

### 預設服務端點
- 後端: http://localhost:4000
- 前端: http://localhost:5173  
- API文件: http://localhost:4000/docs
- 資料庫管理: `pnpm db:studio`

### 預設登入帳號
- 超級管理員: <EMAIL> / Admin@123
- 系統管理員: <EMAIL> / Password@123  
- 租戶管理員: <EMAIL> / Password@123

## 📝 重要提醒

- 所有commit需遵循 Conventional Commits 規範
- 使用繁體中文進行溝通
- 安全性是首要考量，特別是認證和資料庫操作
- 複雜邏輯必須有註解說明
- 參考 `README.md` 和相關文件了解詳細架構
- 權限系統變更需要執行同步腳本
- 前端禁止在組件內重複執行認證初始化