# 命名一致性修正總結

## 修正時間
**日期**: 2024年12月19日  
**修正目標**: 統一使用 `snake_case` 命名規範以符合 Prisma 模型定義

## 修正檔案列表

### ✅ 完全修正的檔案

#### 1. `apps/backend/src/modules/admin/line/services/line-bot.service.ts`
- **問題**: 混用 `camelCase` 和 `snake_case` 屬性名稱
- **修正**:
  - `UserContext` interface: `tenantId` → `tenant_id`
  - Prisma 查詢: `where.tenantId` → `where.tenant_id`
  - Prisma 查詢: `select: { workspaceId: true }` → `select: { workspace_id: true }`
  - LINE Bot SDK 屬性: `event.source.group_id` → `event.source.groupId`
  - LINE Bot SDK 屬性: `event.source.user_id` → `event.source.userId`
  - DTO 解構: `tenantId: dtoTenantId` → `tenant_id: dtoTenantId`

#### 2. `apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts`
- **問題**: `AuthenticatedUser` 介面和服務調用的屬性名稱不一致
- **修正**:
  - 服務調用參數: `tenantId: queryTenantId` → `tenant_id: queryTenantId`
  - 用戶上下文: `tenantId: user.tenantId` → `tenant_id: user.tenant_id`
  - 系統日誌: `tenantId: user.tenant_id` → `tenant_id: user.tenant_id`

#### 3. `apps/backend/src/modules/admin/line/controllers/message-log.controller.ts`
- **問題**: 參數傳遞時的屬性名稱不一致
- **修正**:
  - 服務調用參數: `tenantId,` → `tenant_id,`

### 🔧 部分修正的檔案

#### 4. `apps/backend/prisma/init-db.ts` (部分修正)
- **修正**:
  - Prisma 模型名稱: `client.plan` → `client.plans`
  - Prisma 模型名稱: `client.tenant` → `client.tenants`
  - Prisma 模型名稱: `client.photo` → `client.photos`
  - Prisma 模型名稱: `client.project` → `client.projects`
- **備註**: 此檔案使用舊的 Schema 設計，可能需要完全重寫以符合新的分離用戶架構

#### 5. `apps/backend/prisma/seed.ts` (部分修正)
- **修正**:
  - 角色屬性: `displayName` → `display_name`
  - 租戶屬性: `adminEmail` → `admin_email`, `adminName` → `admin_name`
  - 角色屬性: `isSystem` → `is_system`
  - 時間屬性: `updatedAt` → `updated_at`
  - 租戶屬性: `maxUsers` → `max_users`, `paymentStatus` → `payment_status`

## 預防措施

### 1. 命名檢查腳本
- **位置**: `apps/backend/scripts/check-naming-consistency.js`
- **功能**: 自動檢測 `camelCase` 和 `snake_case` 命名不一致問題
- **使用方式**:
  ```bash
  npm run check:naming
  # 或
  npm run lint:naming
  ```

### 2. 新增 package.json 腳本
```json
{
  "scripts": {
    "check:naming": "node scripts/check-naming-consistency.js",
    "lint:naming": "npm run check:naming"
  }
}
```

## 命名規範標準

### Prisma 模型與欄位
- **模型名稱**: `snake_case` 複數形式 (例如: `system_users`, `tenant_users`)
- **欄位名稱**: `snake_case` (例如: `tenant_id`, `created_at`, `is_enabled`)

### TypeScript 程式碼
- **變數與函式**: `camelCase` (例如: `userName`, `fetchUserData`)
- **介面與類型**: `PascalCase` (例如: `UserProfile`, `AuthenticatedUser`)
- **常數**: `UPPER_SNAKE_CASE` (例如: `MAX_USERS`, `API_TIMEOUT`)

### 與 Prisma 互動
- **資料庫操作**: 嚴格使用 `snake_case` 符合 Prisma schema
- **DTO 轉換**: 必要時使用 `@Transform` 裝飾器處理命名轉換
- **查詢欄位**: 在 `where`, `select`, `include` 中使用 `snake_case`

## 剩餘待修正問題

### 高優先級
1. **完成 `seed.ts` 修正**: 修正剩餘的 `tenantId` 和其他 `camelCase` 屬性
2. **重寫 `init-db.ts`**: 更新以符合新的分離用戶架構
3. **檢查其他核心檔案**: 如認證服務、用戶管理等關鍵模組

### 中優先級
1. **Service 層修正**: 檢查所有服務類別的屬性名稱一致性
2. **DTO 類別修正**: 確保所有 DTO 使用正確的屬性名稱
3. **Controller 層修正**: 檢查所有控制器的參數和回應格式

### 低優先級
1. **測試檔案修正**: 更新測試檔案以符合新的命名規範
2. **文件更新**: 更新 API 文件和開發文件
3. **型別定義完善**: 建立更嚴格的型別定義防止命名錯誤

## 建議的開發流程

1. **開發前**: 運行 `npm run check:naming` 檢查現有問題
2. **開發中**: 嚴格遵循命名規範，特別是與 Prisma 互動的部分
3. **提交前**: 再次運行 `npm run check:naming` 確保無新問題
4. **程式碼審查**: 重點檢查命名一致性，特別是 Prisma 相關程式碼

## 工具和資源

- **命名檢查腳本**: `scripts/check-naming-consistency.js`
- **Prisma 規範文件**: `.cursor/rules/prisma.mdc`
- **應用程式架構指南**: `.cursor/rules/ApplicationArchitectureGuide.mdc`
- **TypeScript 編譯檢查**: `npx tsc --noEmit`

---

**重要提醒**: 這個修正是持續進行的過程。所有新的程式碼都應該嚴格遵循 `snake_case` 命名規範以確保與 Prisma schema 的一致性。 