<script setup lang="ts">
import { RouterView, useRouter } from 'vue-router'
import { Toaster } from '@/components/ui/toast'
import FlashMessage from '@/components/common/FlashMessage.vue'
import { onMounted, watch, onUnmounted } from 'vue'
import { useAuthStore } from '@horizai/auth'
import { useSiteInfo } from '@/composables/shared/useSiteInfo'
import { WebSocketService } from '@/services/websocket.service'
import WebSocketManager from '@/services/websocket-manager'

const router = useRouter()
const { loadSiteInfo } = useSiteInfo()
const authStore = useAuthStore()

// WebSocket 服務實例
let wsService: WebSocketService | null = null

// 監聽認證狀態變化
watch(() => authStore.isLoggedIn, async (authenticated) => {
  if (authenticated && authStore.currentUser) {
    // 用戶已認證，暫時跳過 WebSocket 連接（待後端 WebSocket 修復）
    console.log('用戶已認證，WebSocket 連接暫時禁用')
    // TODO: 修復後端 WebSocket 配置後重新啟用
    /*
    try {
      if (!wsService) {
        wsService = new WebSocketService()
        await wsService.connect() // 手動調用連接方法
        WebSocketManager.setInstance(wsService) // 設置到管理器
        console.log('WebSocket 服務已啟動')
      }
    } catch (error) {
      console.warn('WebSocket: Failed to connect:', error)
    }
    */
  } else {
    // 用戶未認證或已登出，斷開 WebSocket 連接
    if (wsService) {
      wsService.disconnect()
      wsService = null
      WebSocketManager.setInstance(null) // 清除管理器中的實例
      console.log('WebSocket 服務已斷開')
    }
  }
}) // 移除 immediate: true，避免在認證初始化期間觸發

onMounted(async () => {
  // 載入網站基本資訊
  loadSiteInfo()
  
  // 等待路由就緒
  await router.isReady()
  
  // 延遲檢查認證狀態，給認證系統時間完成初始化
  setTimeout(() => {
    if (authStore.isLoggedIn && authStore.currentUser) {
      // 認證完成，暫時跳過 WebSocket 初始化（待後端修復）
      console.log('認證完成，WebSocket 初始化暫時禁用')
      // TODO: 修復後端 WebSocket 配置後重新啟用
      /*
      if (!wsService) {
        wsService = new WebSocketService()
        wsService.connect().then(() => {
          WebSocketManager.setInstance(wsService)
          console.log('WebSocket 服務已在 onMounted 中啟動')
        }).catch(error => {
          console.error('WebSocket 初始化失敗:', error)
        })
      }
      */
    }
  }, 1000) // 延遲 1 秒，確保認證系統完全初始化
})

onUnmounted(() => {
  // 組件卸載時確保 WebSocket 連接被斷開
  if (wsService) {
    wsService.disconnect()
    wsService = null
    WebSocketManager.setInstance(null)
  }
})
</script>

<template>
  <div class="min-h-screen bg-background">
    <RouterView />
    <Toaster />
    <FlashMessage />
  </div>
</template>
