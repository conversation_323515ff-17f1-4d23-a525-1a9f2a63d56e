import type { User } from '@/types/models/user.model'

// 這裡重新導出User類型，避免與其他User類型衝突
export type { User };

export interface LoginDto {
  email: string
  password: string
  /** 對應後端的 remember_me 欄位 */
  remember_me?: boolean
}

export interface RegisterDto {
  email: string
  password: string
  name: string
}

export interface AuthResponse {
  user: {
    id: string
    email: string
    name: string | null
    role: string
    status: string
    tenantId: string | null
    lastLoginAt: Date | null
    avatar: string | null
  }
  accessToken: string
  refreshToken?: string
}

export interface LoginResponse extends AuthResponse {}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  isLoading: boolean
  error: string | null
}

export interface UpdateMeDto {
  name?: string
  email?: string
  currentPassword?: string
  newPassword?: string
}

export interface TokenPayload {
  sub: string
  email: string
  role: string
  tenantId?: string
  exp: number
  iat: number
} 
