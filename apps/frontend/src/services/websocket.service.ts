import { io, Socket } from 'socket.io-client'
import { useAuthStore } from '@horizai/auth'
import { useNotificationStore } from '@/stores/notification.store'
import { getWebSocketConfig, getWebSocketURL, WEBSOCKET_CONFIG } from '@/config/websocket.config'
import type {
    FileEvent,
    CommentEvent,
    TaskEvent,
    ProjectEvent,
    UserMentionEvent,
    SystemMessageEvent
} from '@/types/models/websocket.model'

// 通知回調類型
type NotificationCallback = (notification: {
    type: 'file' | 'comment' | 'task' | 'project' | 'mention' | 'system'
    title: string
    message: string
    priority: 'low' | 'normal' | 'high' | 'urgent'
    actionUrl?: string
}) => void

export class WebSocketService {
    private socket: Socket | null = null
    private reconnectAttempts = 0
    private maxReconnectAttempts = 5
    private reconnectDelay = 1000
    private isConnecting = false
    private notificationCallbacks: Set<NotificationCallback> = new Set()

    constructor() {
        // 不在構造函數中初始化連接，而是等待外部調用
    }

    // 公開的連接方法
    public async connect() {
        await this.initializeConnection()
    }

    // 添加通知回調
    public addNotificationCallback(callback: NotificationCallback) {
        this.notificationCallbacks.add(callback)
    }

    // 移除通知回調
    public removeNotificationCallback(callback: NotificationCallback) {
        this.notificationCallbacks.delete(callback)
    }

    // 觸發通知回調
    private triggerNotificationCallbacks(notification: Parameters<NotificationCallback>[0]) {
        this.notificationCallbacks.forEach(callback => {
            try {
                callback(notification)
            } catch (error) {
                console.error('Notification callback error:', error)
            }
        })
    }

    private async initializeConnection() {
        if (this.isConnecting || this.socket?.connected) {
            console.log('WebSocket: Already connecting or connected, skipping initialization')
            return
        }

        this.isConnecting = true
        console.log('WebSocket: Starting connection initialization...')

        // 延遲獲取 store，確保 Pinia 已經初始化
        let authStore: any
        try {
            authStore = useAuthStore()
            console.log('WebSocket: Auth store accessed successfully')
            console.log('WebSocket: Auth store state:', {
                isLoggedIn: authStore.isLoggedIn,
                hasUser: !!authStore.currentUser
            })

            // 對於 HttpOnly Cookie 認證，前端可能沒有完整的認證狀態
            // 我們仍然嘗試連線，讓後端進行認證
            if (!authStore.isLoggedIn) {
                console.warn('WebSocket: No frontend auth state, but attempting connection with HttpOnly Cookie')
            }

            // 由於使用 HttpOnly Cookie，前端不直接管理 access token
            // 直接嘗試連接，讓後端驗證 Cookie
            console.log('WebSocket: Using HttpOnly Cookie authentication')
        } catch (error) {
            console.error('WebSocket: Failed to access auth store, Pinia may not be initialized:', error)
            // 即使 AuthStore 失敗，我們仍然嘗試連線，依賴 HttpOnly Cookie
            console.log('WebSocket: Attempting connection without AuthStore')
        }

        // 使用配置檔案獲取 WebSocket URL
        const baseURL = getWebSocketURL()
        const config = getWebSocketConfig()

        console.log('WebSocket: WebSocket Base URL:', baseURL)
        console.log('WebSocket: Config:', config)

        // 由於使用 HttpOnly Cookie，前端不需要管理 token
        // WebSocket 連接會自動發送 Cookie，後端會驗證 HttpOnly Cookie
        console.log('WebSocket: Using HttpOnly Cookie for authentication')
        console.log('WebSocket: No frontend token management needed')
        console.log('WebSocket: User type:', authStore?.user?.userType || authStore?.currentUser?.userType || 'unknown')

        const socketOptions = {
            path: config.path,
            transports: config.transports,
            timeout: config.timeout,
            forceNew: config.forceNew,
            withCredentials: config.withCredentials,
            autoConnect: config.autoConnect,
            reconnection: config.reconnection,
            reconnectionAttempts: config.reconnectionAttempts,
            reconnectionDelay: config.reconnectionDelay,
            reconnectionDelayMax: config.reconnectionDelayMax,
            maxReconnectionAttempts: config.maxReconnectionAttempts,
            tryAllTransports: config.tryAllTransports,
            auth: {
                user_type: authStore?.user?.userType || authStore?.currentUser?.userType || WEBSOCKET_CONFIG.AUTH.defaultUserType
            },
        }

        console.log('WebSocket: Connecting to:', baseURL)
        console.log('WebSocket: Full Socket.IO URL:', `${baseURL}${socketOptions.path}`)
        console.log('WebSocket: Connection options:', socketOptions)

        this.socket = io(baseURL, socketOptions)

        this.setupEventListeners()

        // 手動觸發連線（因為 autoConnect: false）
        console.log('WebSocket: Manually connecting socket...')
        this.socket.connect()

        this.isConnecting = false
    }

    private setupEventListeners() {
        if (!this.socket) return

        // 延遲獲取 notification store
        let notificationStore: ReturnType<typeof useNotificationStore>
        try {
            notificationStore = useNotificationStore()
        } catch (error) {
            console.error('WebSocket: Failed to access notification store:', error)
            return
        }

        // Connection events
        this.socket.on('connect', () => {
            console.log('✅ WebSocket connected successfully! Socket ID:', this.socket?.id)
            this.reconnectAttempts = 0

            // Join user's personal room
            try {
                const authStore = useAuthStore()
                if (authStore.currentUser?.id) {
                    console.log('WebSocket: Joining user room:', authStore.currentUser.id)
                    this.socket?.emit('join:room', {
                        roomType: 'user',
                        roomId: authStore.currentUser.id
                    })
                } else {
                    console.warn('WebSocket: No current user ID found for room joining')
                }
            } catch (error) {
                console.error('WebSocket: Failed to access auth store in connect handler:', error)
            }
        })

        this.socket.on('disconnect', (reason) => {
            console.log('❌ WebSocket disconnected. Reason:', reason)
            if (reason === 'io server disconnect') {
                // Server initiated disconnect, try to reconnect
                console.log('WebSocket: Server initiated disconnect, attempting reconnect...')
                this.handleReconnect()
            }
        })

        this.socket.on('connect_error', (error) => {
            console.error('❌ WebSocket connection error:', error)
            console.error('WebSocket: Error details:', {
                message: error.message,
                description: (error as any).description,
                context: (error as any).context,
                type: (error as any).type,
                data: (error as any).data
            })

            // 檢查具體的錯誤類型
            if (error.message) {
                if (error.message.includes('jwt') || error.message.includes('Unauthorized') || error.message.includes('Authentication')) {
                    console.log('WebSocket: Authentication error detected, attempting token refresh')
                    this.handleAuthError()
                } else if (error.message.includes('CORS') || error.message.includes('cors')) {
                    console.error('WebSocket: CORS error - check server CORS configuration')
                } else if (error.message.includes('timeout') || error.message.includes('Timeout')) {
                    console.error('WebSocket: Connection timeout - check server availability')
                } else {
                    console.log('WebSocket: Non-auth error, attempting reconnect')
                    this.handleReconnect()
                }
            } else {
                console.log('WebSocket: Unknown error, attempting reconnect')
                this.handleReconnect()
            }
        })

        // 監聽服務器端的認證錯誤
        this.socket.on('error', (error) => {
            console.error('WebSocket server error:', error)
            if (error.message && (error.message.includes('Unauthorized') || error.message.includes('jwt'))) {
                console.log('WebSocket: Server authentication error, attempting token refresh')
                this.handleAuthError()
            }
        })

        // File events
        this.socket.on('file:uploaded', (data: FileEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '檔案上傳',
                message: `${data.fileName} 已成功上傳`,
                duration: 3000
            }
            notificationStore.notify(notification)

            // 觸發通知中心回調
            this.triggerNotificationCallbacks({
                type: 'file',
                title: '檔案上傳完成',
                message: `檔案 "${data.fileName}" 已成功上傳`,
                priority: 'normal'
            })
        })

        this.socket.on('file:updated', (data: FileEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '檔案更新',
                message: `${data.fileName} 已更新`,
                duration: 3000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'file',
                title: '檔案更新',
                message: `檔案 "${data.fileName}" 已更新`,
                priority: 'normal'
            })
        })

        this.socket.on('file:shared', (data: FileEvent) => {
            const notification = {
                type: 'success' as const,
                mode: 'toast' as const,
                title: '檔案分享',
                message: `${data.fileName} 已與您分享`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'file',
                title: '檔案分享',
                message: `檔案 "${data.fileName}" 已與您分享`,
                priority: 'normal'
            })
        })

        this.socket.on('file:deleted', (data: FileEvent) => {
            const notification = {
                type: 'warning' as const,
                mode: 'toast' as const,
                title: '檔案刪除',
                message: `${data.fileName} 已被刪除`,
                duration: 3000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'file',
                title: '檔案刪除',
                message: `檔案 "${data.fileName}" 已被刪除`,
                priority: 'normal'
            })
        })

        // Test message events
        this.socket.on('test:message:response', (data: any) => {
            console.log('✅ WebSocket: 收到測試訊息回應:', data)
            const notification = {
                type: 'success' as const,
                mode: 'toast' as const,
                title: '測試訊息回應',
                message: `伺服器回應: ${data.message || '測試成功'}`,
                duration: 3000
            }
            notificationStore.notify(notification)
        })

        this.socket.on('test:echo', (data: any) => {
            console.log('🔄 WebSocket: 收到回音測試:', data)
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '回音測試',
                message: `回音: ${data.message || data}`,
                duration: 3000
            }
            notificationStore.notify(notification)
        })

        // Comment events
        this.socket.on('comment:created', (data: CommentEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '新留言',
                message: `${data.authorName} 在 ${data.targetType} 中新增了留言`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'comment',
                title: '新留言',
                message: `${data.authorName} 在 ${data.targetType} 中新增了留言`,
                priority: 'normal'
            })
        })

        this.socket.on('comment:updated', (data: CommentEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '留言更新',
                message: `${data.authorName} 更新了留言`,
                duration: 3000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'comment',
                title: '留言更新',
                message: `${data.authorName} 更新了留言`,
                priority: 'normal'
            })
        })

        this.socket.on('comment:deleted', (data: CommentEvent) => {
            const notification = {
                type: 'warning' as const,
                mode: 'toast' as const,
                title: '留言刪除',
                message: `留言已被刪除`,
                duration: 3000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'comment',
                title: '留言刪除',
                message: '留言已被刪除',
                priority: 'normal'
            })
        })

        // Task events
        this.socket.on('task:created', (data: TaskEvent) => {
            const notification = {
                type: 'success' as const,
                mode: 'toast' as const,
                title: '新任務',
                message: `任務「${data.taskTitle}」已建立`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'task',
                title: '新任務建立',
                message: `任務「${data.taskTitle}」已建立`,
                priority: 'normal'
            })
        })

        this.socket.on('task:status-changed', (data: TaskEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '任務狀態更新',
                message: `任務「${data.taskTitle}」狀態已更改為 ${data.newStatus}`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'task',
                title: '任務狀態更新',
                message: `任務「${data.taskTitle}」狀態已更改為 ${data.newStatus}`,
                priority: 'normal'
            })
        })

        this.socket.on('task:assigned', (data: TaskEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '任務指派',
                message: `您被指派了新任務：${data.taskTitle}`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'task',
                title: '任務指派',
                message: `您被指派了新任務：${data.taskTitle}`,
                priority: 'high'
            })
        })

        // Project events
        this.socket.on('project:created', (data: ProjectEvent) => {
            const notification = {
                type: 'success' as const,
                mode: 'toast' as const,
                title: '新專案',
                message: `專案「${data.projectName}」已建立`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'project',
                title: '新專案建立',
                message: `專案「${data.projectName}」已建立`,
                priority: 'normal'
            })
        })

        this.socket.on('project:status-changed', (data: ProjectEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '專案狀態更新',
                message: `專案「${data.projectName}」狀態已更改`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'project',
                title: '專案狀態更新',
                message: `專案「${data.projectName}」狀態已更改`,
                priority: 'normal'
            })
        })

        this.socket.on('project:member-added', (data: ProjectEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '專案成員',
                message: `您已被加入專案「${data.projectName}」`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'project',
                title: '專案成員',
                message: `您已被加入專案「${data.projectName}」`,
                priority: 'normal'
            })
        })

        // User mention events
        this.socket.on('user:mentioned', (data: UserMentionEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '提及通知',
                message: `${data.mentionedBy} 在 ${data.context} 中提及了您`,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'mention',
                title: '提及通知',
                message: `${data.mentionedBy} 在 ${data.context} 中提及了您`,
                priority: 'high'
            })
        })

        // System message events
        this.socket.on('system:message', (data: SystemMessageEvent) => {
            const notification = {
                type: 'info' as const,
                mode: 'toast' as const,
                title: '系統通知',
                message: data.message,
                duration: 5000
            }
            notificationStore.notify(notification)

            this.triggerNotificationCallbacks({
                type: 'system',
                title: '系統通知',
                message: data.message,
                priority: data.priority === 'high' ? 'high' : 'normal'
            })
        })

        // Error handling
        this.socket.on('error', (error) => {
            console.error('WebSocket error:', error)
            notificationStore.notify({
                type: 'error',
                mode: 'toast' as const,
                title: '連線錯誤',
                message: '即時通知連線發生問題',
                duration: 5000
            })
        })
    }

    private async handleAuthError() {
        console.log('WebSocket: Handling authentication error')

        try {
            const authStore = useAuthStore()

            // 嘗試刷新 token
            await authStore.refreshToken()
            console.log('WebSocket: Token refreshed, attempting reconnection')

            // 重新連接
            this.disconnect()
            await this.initializeConnection()
        } catch (error) {
            console.error('WebSocket: Token refresh failed:', error)

            // 如果 token 刷新失敗，可能需要重新登入
            // 這裡可以觸發登出流程或顯示錯誤訊息
            console.warn('WebSocket: Authentication failed, user may need to re-login')
        }
    }

    private handleReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.error('WebSocket: Max reconnection attempts reached')
            return
        }

        this.reconnectAttempts++
        const delay = this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1)

        console.log(`WebSocket: Attempting to reconnect in ${delay}ms (attempt ${this.reconnectAttempts})`)

        setTimeout(async () => {
            this.disconnect()
            await this.initializeConnection()
        }, delay)
    }

    public joinWorkspaceRoom(workspaceId: string) {
        if (this.socket?.connected) {
            this.socket.emit('join:room', {
                roomType: 'workspace',
                roomId: workspaceId
            })
            console.log(`Joined workspace room: ${workspaceId}`)
        }
    }

    public leaveWorkspaceRoom(workspaceId: string) {
        if (this.socket?.connected) {
            this.socket.emit('leave:room', {
                roomType: 'workspace',
                roomId: workspaceId
            })
            console.log(`Left workspace room: ${workspaceId}`)
        }
    }

    public joinProjectRoom(projectId: string) {
        if (this.socket?.connected) {
            this.socket.emit('join:room', {
                roomType: 'project',
                roomId: projectId
            })
            console.log(`Joined project room: ${projectId}`)
        }
    }

    public leaveProjectRoom(projectId: string) {
        if (this.socket?.connected) {
            this.socket.emit('leave:room', {
                roomType: 'project',
                roomId: projectId
            })
            console.log(`Left project room: ${projectId}`)
        }
    }

    public sendTestMessage(message: string = '測試訊息') {
        if (!this.socket?.connected) {
            console.warn('WebSocket: 無法發送測試訊息，連線未建立')
            return false
        }

        try {
            this.socket.emit('test:message', {
                message,
                timestamp: new Date().toISOString(),
                clientId: this.socket.id
            })
            console.log(`WebSocket: 已發送測試訊息: ${message}`)
            return true
        } catch (error) {
            console.error('WebSocket: 發送測試訊息失敗:', error)
            return false
        }
    }

    public sendChatMessage(roomId: string, message: string) {
        if (!this.socket?.connected) {
            console.warn('WebSocket: 無法發送聊天訊息，連線未建立')
            return false
        }

        try {
            this.socket.emit('chat:message', {
                roomId,
                message,
                timestamp: new Date().toISOString()
            })
            console.log(`WebSocket: 已發送聊天訊息到房間 ${roomId}: ${message}`)
            return true
        } catch (error) {
            console.error('WebSocket: 發送聊天訊息失敗:', error)
            return false
        }
    }

    public disconnect() {
        if (this.socket) {
            this.socket.disconnect()
            this.socket = null
        }
    }

    public async reconnect() {
        this.disconnect()
        this.reconnectAttempts = 0
        await this.initializeConnection()
    }

    public get isConnected(): boolean {
        return this.socket?.connected || false
    }

    public get connectionId(): string | undefined {
        return this.socket?.id
    }

    // 添加自定義事件監聽器
    public on(event: string, callback: (...args: any[]) => void) {
        if (this.socket) {
            this.socket.on(event, callback)
        } else {
            console.warn(`WebSocket: Cannot register event listener for '${event}', socket not connected`)
        }
    }

    // 移除事件監聽器
    public off(event: string, callback?: (...args: any[]) => void) {
        if (this.socket) {
            this.socket.off(event, callback)
        }
    }

    // 發送事件
    public emit(event: string, ...args: any[]) {
        if (this.socket?.connected) {
            this.socket.emit(event, ...args)
            return true
        } else {
            console.warn(`WebSocket: Cannot emit event '${event}', socket not connected`)
            return false
        }
    }
}

// 不再導出單例實例，而是讓 App.vue 來管理實例 