import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'

export interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  metaKey?: boolean
  description: string
  action: () => void
  scope?: string
}

export function useKeyboardShortcuts(shortcuts: KeyboardShortcut[] = []) {
  const router = useRouter()

  // 預設快捷鍵
  const defaultShortcuts: KeyboardShortcut[] = [
    {
      key: 'h',
      altKey: true,
      description: '顯示快捷鍵說明',
      action: () => showShortcutHelp(),
      scope: 'global'
    },
    {
      key: 'd',
      altKey: true,
      description: '前往 AI 儀表板',
      action: () => router.push('/admin/ai-dashboard'),
      scope: 'ai'
    },
    {
      key: 's',
      altKey: true,
      description: '前往 AI 設定',
      action: () => router.push('/admin/ai-settings'),
      scope: 'ai'
    },
    {
      key: 'w',
      altKey: true,
      description: '前往工作流程管理',
      action: () => router.push('/admin/ai-workflows'),
      scope: 'ai'
    },
    {
      key: 'c',
      altKey: true,
      description: '前往 AI 創作室',
      action: () => router.push('/admin/ai-creator-studio'),
      scope: 'ai'
    },
    {
      key: 't',
      altKey: true,
      description: '前往測試中心',
      action: () => router.push('/admin/ai-testing'),
      scope: 'ai'
    }
  ]

  // 合併快捷鍵
  const allShortcuts = [...defaultShortcuts, ...shortcuts]

  // 快捷鍵說明對話框狀態
  const showHelp = ref(false)

  const showShortcutHelp = () => {
    showHelp.value = true
  }

  const hideShortcutHelp = () => {
    showHelp.value = false
  }

  // 處理鍵盤事件
  const handleKeyDown = (event: KeyboardEvent) => {
    // 如果用戶在輸入框中，則忽略快捷鍵
    const target = event.target as HTMLElement
    if (target.tagName === 'INPUT' || target.tagName === 'TEXTAREA' || target.contentEditable === 'true') {
      return
    }

    for (const shortcut of allShortcuts) {
      const {
        key,
        ctrlKey = false,
        altKey = false,
        shiftKey = false,
        metaKey = false,
        action
      } = shortcut

      if (
        event.key.toLowerCase() === key.toLowerCase() &&
        event.ctrlKey === ctrlKey &&
        event.altKey === altKey &&
        event.shiftKey === shiftKey &&
        event.metaKey === metaKey
      ) {
        event.preventDefault()
        action()
        break
      }
    }
  }

  // 根據範圍過濾快捷鍵
  const getShortcutsByScope = (scope?: string) => {
    if (!scope) return allShortcuts
    return allShortcuts.filter(s => !s.scope || s.scope === scope || s.scope === 'global')
  }

  onMounted(() => {
    window.addEventListener('keydown', handleKeyDown)
  })

  onUnmounted(() => {
    window.removeEventListener('keydown', handleKeyDown)
  })

  return {
    showHelp,
    showShortcutHelp,
    hideShortcutHelp,
    getShortcutsByScope,
    allShortcuts
  }
}

// 快捷鍵說明組件
export const ShortcutHelp = {
  template: `
    <div v-if="show" class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center" @click.self="$emit('close')">
      <div class="bg-white rounded-lg p-6 max-w-lg mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">快捷鍵說明</h3>
          <button @click="$emit('close')" class="text-gray-400 hover:text-gray-600">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
            </svg>
          </button>
        </div>
        
        <div class="space-y-3">
          <div v-for="shortcut in shortcuts" :key="shortcut.key + shortcut.description" class="flex items-center justify-between">
            <span class="text-sm text-gray-600">{{ shortcut.description }}</span>
            <div class="flex items-center space-x-1">
              <kbd v-if="shortcut.ctrlKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Ctrl</kbd>
              <kbd v-if="shortcut.altKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Alt</kbd>
              <kbd v-if="shortcut.shiftKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Shift</kbd>
              <kbd v-if="shortcut.metaKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Cmd</kbd>
              <kbd class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">{{ shortcut.key.toUpperCase() }}</kbd>
            </div>
          </div>
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-500">
          提示：在輸入框中時快捷鍵會被停用
        </div>
      </div>
    </div>
  `,
  props: {
    show: Boolean,
    shortcuts: Array
  },
  emits: ['close']
}