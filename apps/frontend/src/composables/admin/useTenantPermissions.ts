import { ref, computed, onMounted } from "vue";
import { usePermission } from "@/composables/admin/usePermission";
import { USER_ROLES } from "@/constants/user.constants";
import { useAuth } from "@horizai/auth";
import { ErrorService } from "@/services/error.service";
import { Actions, Subjects } from "@horizai/permissions";

// 引入 Action 型別
type Action = string;

export function useTenantPermissions() {
  const { currentUser, can } = usePermission();
  const auth = useAuth();

  // 使用本地狀態儲存使用者角色
  const userRole = ref("");
  const isLoaded = ref(false);

  // 在組件掛載時讀取使用者角色
  onMounted(async () => {
    try {
      // 嘗試讀取使用者信息 - 修正：使用 .value 訪問響應式狀態
      if (auth.isAuthenticated.value) {
        const user = await auth.getUser();
        userRole.value = user?.role || "";
        console.log("[useTenantPermissions] 載入使用者成功:", user);
        console.log("[useTenantPermissions] 本地 userRole:", userRole.value);
      }
    } catch (error) {
      ErrorService.notify(error);
    } finally {
      isLoaded.value = true;
    }
  });

  // 統一的權限檢查函數
  const checkPermission = (permission: Action, subject = "Tenant"): boolean => {
    console.log(`[useTenantPermissions] 檢查權限 ${permission}:${subject}`);

    // 確保當前使用者已載入
    if (!isLoaded.value) {
      console.warn("[useTenantPermissions] 權限尚未載入完成，檢查失敗");
      return false;
    }

    // 超級管理員擁有所有權限
    if (userRole.value === USER_ROLES.SUPER_ADMIN) {
      console.log("[useTenantPermissions] 超級管理員，自動授權");
      return true;
    }

    // 使用 auth.getAbility() 進行檢查
    const ability = auth.getAbility();
    const result = ability ? ability.can(permission, subject) : false;
    console.log(`[useTenantPermissions] 權限檢查結果: ${result}`);
    return result;
  };

  // 檢查使用者是否擁有指定角色
  const hasRole = (roles: string | string[]) => {
    // 使用來自 currentUser 的角色或本地儲存的角色
    const role = currentUser.value?.role || userRole.value;

    if (!role) {
      console.log("[useTenantPermissions] 無使用者角色，權限檢查失敗");
      return false;
    }

    if (typeof roles === "string") {
      const result = role === roles;
      console.log(
        `[useTenantPermissions] 角色檢查 ${role} === ${roles} 結果:`,
        result
      );
      return result;
    }

    const result = roles.includes(role);
    console.log(
      `[useTenantPermissions] 角色列表檢查 ${roles.join(",")} 包含 ${role} 結果:`,
      result
    );
    return result;
  };

  // 直接使用 CASL 能力檢查
  const canManage = computed(() => {
    const ability = auth.getAbility();
    const result = ability
      ? ability.can(Actions.MANAGE, Subjects.TENANT)
      : false;
    console.log(
      "[useTenantPermissions] canManage 計算:",
      result,
      "使用 ability:",
      ability
    );
    return result;
  });

  const canRead = computed(() => {
    const ability = auth.getAbility();
    const result = ability ? ability.can(Actions.READ, Subjects.TENANT) : false;
    console.log(
      "[useTenantPermissions] canRead 計算:",
      result,
      "使用 ability:",
      ability
    );
    return result;
  });

  // 直接接檢查使用者是否為超級管理員
  const isSuperAdmin = computed(() => {
    const role = currentUser.value?.role || userRole.value;
    const result = role === USER_ROLES.SUPER_ADMIN;
    return result;
  });

  // 租戶管理權限
  const tenantPermissions = computed(() => {
    console.log(
      "[useTenantPermissions] 計算租戶權限，當前使用者角色:",
      userRole.value
    );
    console.log(
      "[useTenantPermissions] CASL 權限狀態 - canManage:",
      canManage.value,
      "canRead:",
      canRead.value,
      "isSuperAdmin:",
      isSuperAdmin.value
    );

    // 優先使用 CASL 能力檢查或直接的角色檢查
    const canViewResult =
      canManage.value ||
      canRead.value ||
      isSuperAdmin.value ||
      hasRole([
        USER_ROLES.SUPER_ADMIN,
        USER_ROLES.SYSTEM_ADMIN,
        USER_ROLES.TENANT_ADMIN,
      ]);

    console.log("[useTenantPermissions] canView 計算結果:", canViewResult);

    const result = {
      // 基本操作權限
      canView: canViewResult,
      canCreate:
        canManage.value ||
        hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.SYSTEM_ADMIN]),
      canEdit:
        canManage.value ||
        hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.SYSTEM_ADMIN]),
      canDelete: canManage.value || hasRole([USER_ROLES.SUPER_ADMIN]),

      // 租戶使用者管理權限
      canManageUsers:
        canManage.value ||
        hasRole([
          USER_ROLES.SUPER_ADMIN,
          USER_ROLES.SYSTEM_ADMIN,
          USER_ROLES.TENANT_ADMIN,
        ]),
      canAddUsersDirectly:
        canManage.value ||
        hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.SYSTEM_ADMIN]),
      canSendInvitations:
        canManage.value ||
        hasRole([
          USER_ROLES.SUPER_ADMIN,
          USER_ROLES.SYSTEM_ADMIN,
          USER_ROLES.TENANT_ADMIN,
        ]),

      // 租戶特定功能權限
      canManagePlans:
        canManage.value ||
        hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.SYSTEM_ADMIN]),
      canViewBilling:
        canManage.value ||
        hasRole([
          USER_ROLES.SUPER_ADMIN,
          USER_ROLES.SYSTEM_ADMIN,
          USER_ROLES.TENANT_ADMIN,
        ]),

      // 是否為系統管理員
      isSystemAdmin: hasRole([USER_ROLES.SUPER_ADMIN, USER_ROLES.SYSTEM_ADMIN]),

      // 是否為租戶管理員
      isTenantAdmin: hasRole([USER_ROLES.TENANT_ADMIN]),

      // 是否為目前租戶的管理員
      isCurrentTenantAdmin: (tenantId: string) => {
        return (
          hasRole(USER_ROLES.TENANT_ADMIN) &&
          (currentUser.value?.tenantId === tenantId || false)
        );
      },
    };

    console.log("[useTenantPermissions] 計算的權限結果:", result);

    return result;
  });

  return {
    tenantPermissions,
    canManage,
    canRead,
    isSuperAdmin,
    userRole,
    isLoaded,
    checkPermission,
  };
}
