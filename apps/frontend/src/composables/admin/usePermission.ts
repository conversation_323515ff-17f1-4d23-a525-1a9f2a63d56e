import { computed } from "vue";
import { useAuthStore } from "@horizai/auth";
import { Actions, Subjects } from "@horizai/permissions";
import type { AppAbility } from "@/types/models/ability.model";

/**
 * 權限檢查 Composable
 * 提供統一、響應式的權限檢查邏輯，其唯一真實來源為 useAuthStore 中的 CASL ability。
 */
export function usePermission() {
  const authStore = useAuthStore();

  const currentUser = computed(() => authStore.currentUser);
  const isAuthenticated = computed(() => authStore.isLoggedIn);
  const ability = computed(() => authStore.getAbility());

  /**
   * 檢查當前使用者是否具有特定權限。
   * @param action The action (e.g., 'manage', 'create', 'read').
   * @param subject The subject (e.g., 'Article', 'User', or a specific object).
   * @param field Optional. The field of the subject to check.
   * @returns True if the user has the permission, false otherwise.
   */
  const can = (action: string, subject: any, field?: string): boolean => {
    const abilityObj = ability.value;
    if (!abilityObj || typeof abilityObj.can !== 'function') {
      return false;
    }
    return abilityObj.can(action, subject, field);
  };

  /**
   * 檢查當前使用者是否不具有特定權限。
   * @param action The action.
   * @param subject The subject.
   * @param field Optional. The field.
   * @returns True if the user does NOT have the permission.
   */
  const cannot = (action: string, subject: any, field?: string): boolean => {
    return !can(action, subject, field);
  };

  /**
   * 檢查使用者是否為超級管理員
   */
  const isSuperAdmin = computed(() => can(Actions.MANAGE, "all"));

  /**
   * 檢查使用者是否為系統管理員
   */
  const isSystemAdmin = computed(() =>
    can(Actions.MANAGE, Subjects.SYSTEM_SETTINGS),
  );

  /**
   * 檢查使用者是否為租戶管理員
   */
  const isTenantAdmin = computed(() => can(Actions.MANAGE, Subjects.TENANT));

  /**
   * 檢查使用者是否具有任一管理員角色
   */
  const hasAdminRole = computed(
    () => isSuperAdmin.value || isSystemAdmin.value || isTenantAdmin.value,
  );

  return {
    currentUser,
    isAuthenticated,
    ability,
    can,
    cannot,
    isSuperAdmin,
    isSystemAdmin,
    isTenantAdmin,
    hasAdminRole,
  };
}
