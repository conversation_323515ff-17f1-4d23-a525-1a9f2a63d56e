import { ref, watch, nextTick } from "vue";
import type { Ref } from "vue";
import { marked } from "marked";
import DOMPurify from "dompurify";
import { AiBotsService } from "@/services/admin/ai/ai-bots.service";
import type { AiBot } from "@/types/models/ai.model";
import type { useNotification } from "@/composables/shared/useNotification";

interface ApiResponseTypes {
  content: string;
  usage?: {
    inputTokens?: number;
    outputTokens?: number;
  };
}

export function useAIBotChat(
  selectedBot: Ref<AiBot | null>,
  editData: Ref<AiBot>,
  notification: ReturnType<typeof useNotification>
) {
  const testMessage = ref("");
  const isSending = ref(false);
  const chatMessages = ref<{ role: "user" | "assistant"; content: string }[]>([]);
  const aiTyping = ref(false);
  const typingText = ref("");
  const typingIndex = ref(0);
  const typingInterval = ref<number | null>(null);
  const chatEndRef = ref<HTMLElement | null>(null);

  marked.use({
    gfm: true,
    breaks: true,
  });

  const renderMarkdown = (text: string) => {
    try {
      const cleanedText = text.replace(/\n\s*\n/g, "\n").trim();
      const parsedContent = marked.parse(cleanedText) as string;
      return DOMPurify.sanitize(parsedContent);
    } catch (error) {
      return text;
    }
  };

  watch(
    () => chatMessages.value.length,
    async () => {
      await nextTick();
      chatEndRef.value?.scrollIntoView({ behavior: "smooth" });
    },
    { deep: true }
  );

  watch(
    [
      () => editData.value.model_id,
      () => editData.value.key_id,
      () => editData.value.scene,
      () => editData.value.response_format,
      () => editData.value.system_prompt,
      () => editData.value.temperature,
    ],
    () => {
      if (chatMessages.value.length > 0) {
        clearChat();
      }
    },
    { deep: true }
  );

  const finishTyping = (fullText: string) => {
    if (typingInterval.value) {
      clearInterval(typingInterval.value);
      typingInterval.value = null;
    }

    chatMessages.value.push({
      role: "assistant",
      content: fullText,
    });

    aiTyping.value = false;
    typingText.value = "";
  };

  const startTypingEffect = (text: string) => {
    if (typingInterval.value) {
      clearInterval(typingInterval.value);
    }

    typingText.value = "";
    typingIndex.value = 0;
    aiTyping.value = true;

    typingInterval.value = window.setInterval(() => {
      const isPunctuation = [".", ",", "!", "?", ";", ":", "\n"].includes(
        text[typingIndex.value]
      );
      const shouldPause = isPunctuation && Math.random() > 0.3;

      if (!shouldPause && Math.random() > 0.15) {
        if (typingIndex.value < text.length) {
          typingText.value += text[typingIndex.value];
          typingIndex.value++;

          if (text[typingIndex.value - 1] === "\n") {
            clearInterval(typingInterval.value as number);
            setTimeout(() => {
              typingInterval.value = window.setInterval(() => {
                const shouldContinue = Math.random() > 0.15;
                if (shouldContinue && typingIndex.value < text.length) {
                  typingText.value += text[typingIndex.value];
                  typingIndex.value++;
                } else if (typingIndex.value >= text.length) {
                  finishTyping(text);
                }
              }, 15);
            }, 300);
          }
        } else {
          finishTyping(text);
        }
      }
    }, 15);
  };

  const startThinkingAnimation = () => {
    aiTyping.value = true;
    typingText.value = "";
    return window.setInterval(() => {}, 500);
  };

  const sendMessage = async () => {
    if (!testMessage.value.trim()) {
      notification.toast.warning("請輸入訊息內容");
      return;
    }

    if (!selectedBot.value?.id) {
      notification.toast.error("請先選擇或建立一個 AI 助手");
      return;
    }

    if (!editData.value.model_id) {
      notification.toast.error("請先選擇 AI 模型");
      return;
    }

    if (!editData.value.key_id) {
      notification.toast.error("請先設定 API 金鑰");
      return;
    }

    chatMessages.value.push({
      role: "user",
      content: testMessage.value.trim(),
    });

    const messageToSend = testMessage.value.trim();
    testMessage.value = "";

    isSending.value = true;
    const thinkingInterval = startThinkingAnimation();

    try {
      const validMessages = chatMessages.value
        .filter(
          (msg) =>
            msg.role &&
            typeof msg.content === "string" &&
            msg.content.trim().length > 0
        )
        .map((msg) => ({ role: msg.role, content: msg.content }));

      // 確保有有效的訊息可以發送
      if (validMessages.length === 0) {
        throw new Error("沒有有效的訊息可以發送");
      }

      const apiResponse = await AiBotsService.execute(selectedBot.value.id, {
        messages: validMessages,
        temperature: editData.value.temperature,
      });

      const typedResponse = apiResponse as unknown as ApiResponseTypes;

      if (!typedResponse) {
        throw new Error("API 回應格式不符預期");
      }

      const responseContent = typedResponse.content || "";

      if (!responseContent.trim()) {
        throw new Error("API 回應內容為空");
      }

      clearInterval(thinkingInterval);

      startTypingEffect(responseContent);
    } catch (error) {
      clearInterval(thinkingInterval);
      aiTyping.value = false;

      let errorMessage = "與 AI 助手通信失敗";

      if (error instanceof Error) {
        errorMessage = `錯誤: ${error.message}`;
      } else if (typeof error === "string") {
        errorMessage = `錯誤: ${error}`;
      } else if (error && typeof error === "object" && "message" in error) {
        errorMessage = `錯誤: ${error.message || JSON.stringify(error)}`;
      }

      chatMessages.value.push({
        role: "assistant",
        content: `⚠️ ${errorMessage}`,
      });

      notification.toast.error("與 AI 助手通信失敗");
    } finally {
      isSending.value = false;
    }
  };

  const clearChat = () => {
    chatMessages.value = [];
    if (typingInterval.value) {
      clearInterval(typingInterval.value);
      typingInterval.value = null;
    }
    aiTyping.value = false;
    typingText.value = "";
  };

  return {
    testMessage,
    isSending,
    chatMessages,
    aiTyping,
    typingText,
    chatEndRef,
    sendMessage,
    clearChat,
    renderMarkdown,
  };
}
