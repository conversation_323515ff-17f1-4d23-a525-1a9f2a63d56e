import { ref, computed, watch } from 'vue'
import { useForm } from 'vee-validate'
import { useNotification } from '@/composables/shared/useNotification'
import { type DateValue, parseDate, getLocalTimeZone, today } from '@internationalized/date'
import type { Plan } from '@/types/models/plan.model'
import type { Order } from '@/types/models/order.model'
import { formatters } from '@/utils/formatting.utils'
import { calculateEndDate as calculateOrderEndDate } from '@/utils/date.utils'
import { calculateOrderAmount as calculateAmount } from '../../utils/order.utils'
import { ErrorService } from '@/services/error.service'

// 類型定義
export type PlanOption = Pick<Plan, 'id' | 'name' | 'price' | 'maxUsers'> & {
  limits?: {
    maxUsers?: number;
    maxProjects?: number;
    maxStorage?: number;
  }
};

// 定義表單值的型別
export type OrderFormValues = {
  tenantId: string;
  planId: string;
  startDate: string;
  period: number;
  numberOfSubscribers: number;
  remarks?: string;
  amount?: number;
  paymentMethod: string;
  paymentStatus: 'pending' | 'paid' | 'failed';
  contactName: string;
  contactEmail: string;
}

// 期間選項
export const periodOptions = [
  { label: '1 個月', value: 1 },
  { label: '3 個月', value: 3 },
  { label: '6 個月', value: 6 },
  { label: '12 個月', value: 12 }
]

// 初始值
const getInitialValues = (): OrderFormValues => ({
  tenantId: '',
  planId: '',
  startDate: today(getLocalTimeZone()).toString(),
  period: 1,
  numberOfSubscribers: 1,
  remarks: '',
  amount: 0,
  paymentMethod: '銀行轉帳',
  paymentStatus: 'pending',
  contactName: '',
  contactEmail: ''
})

export interface UseOrderFormOptions {
  onSubmit?: (values: OrderFormValues & { endDate: string; discount: number }) => void;
  planOptions: PlanOption[];
  tenantOptions?: { id: string; name: string }[];
  tenantSubscribers?: Record<string, number>;
  selectedOrder?: Order | null;
  isEditMode?: boolean;
}

export function useOrderForm(options: UseOrderFormOptions) {
  const {
    onSubmit: handleSubmit,
    planOptions: initialPlanOptions,
    tenantOptions = [],
    tenantSubscribers = {},
    selectedOrder = null,
    isEditMode = false
  } = options;
  
  const notification = useNotification()
  
  // 內部副本，用於更新選項
  const internalOptions = ref({
    isEditMode,
    selectedOrder,
    tenantOptions,
    planOptions: initialPlanOptions,
    tenantSubscribers
  });
  
  // 狀態
  const loading = ref(false)
  const selectedPlanPrice = ref(0)
  const selectedPeriod = ref(1)
  const selectedSubscribers = ref(1)
  const customDiscount = ref(100)
  const calculatedAmount = ref(0)
  const currentPlanOptions = ref<PlanOption[]>(initialPlanOptions)
  const currentTenantSubscribers = ref(0)

  // 使用 vee-validate 的驗證方式，包含必填及格式驗證
  const form = useForm<OrderFormValues>({
    initialValues: getInitialValues(),
    validateOnMount: false,
    validationSchema: {
      tenantId: (value: string) => value ? true : '請選擇租戶',
      planId: (value: string) => value ? true : '請選擇方案',
      startDate: (value: string) => value ? true : '請選擇開始日期',
      contactEmail: (value: string) => {
        if (!value) return true; // 空值可跳過格式驗證
        // 簡單的電子郵件格式驗證
        const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
        return emailRegex.test(value) ? true : '聯絡信箱格式不正確';
      }
    }
  })

  // 可用方案列表
  const availablePlans = computed(() => {
    return currentPlanOptions.value.filter(plan => {
      if (!plan.maxUsers) return true
      const totalSubscribers = currentTenantSubscribers.value + selectedSubscribers.value
      return totalSubscribers <= plan.maxUsers
    })
  })

  // 更新租戶已訂閱數
  const updateTenantSubscribers = (count: number) => {
    currentTenantSubscribers.value = count
  }

  // 更新方案列表
  const updatePlanOptions = (newOptions: PlanOption[]) => {
    if (!Array.isArray(newOptions)) {
      console.error('更新方案列表錯誤：無效的方案列表格式', newOptions)
      return
    }

    const currentValue = JSON.stringify(currentPlanOptions.value)
    const newValue = JSON.stringify(newOptions)
    
    if (currentValue !== newValue) {
      currentPlanOptions.value = [...newOptions]
    }
  }

  // 格式化金額
  const formatAmount = (amount: number) => {
    return new Intl.NumberFormat('zh-TW', {
      style: 'decimal',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  // 計算基本金額（原價）
  const baseAmount = computed(() => {
    const planPrice = selectedPlanPrice.value;
    if (!planPrice) return 0;
    return planPrice;
  });

  // 計算小計金額（數量 × 期間）
  const subtotalAmount = computed(() => {
    const planPrice = selectedPlanPrice.value;
    const period = selectedPeriod.value;
    const subscribers = selectedSubscribers.value;
    
    if (!planPrice || !period || !subscribers) return 0;
    return planPrice * period * subscribers;
  });

  // 格式化金額顯示
  const formattedAmountDisplay = computed(() => {
    return formatAmount(calculatedAmount.value);
  });

  // 格式化基本金額顯示
  const formattedBaseAmount = computed(() => {
    return formatAmount(baseAmount.value);
  });

  // 格式化小計金額顯示
  const formattedSubtotalAmount = computed(() => {
    return formatAmount(subtotalAmount.value);
  });

  // 讀取當前選擇方案的最大可訂閱數
  const getCurrentPlanMaxUsers = (planId: string) => {
    if (!planId) return 999999
    
    const selectedPlan = currentPlanOptions.value.find(plan => plan.id === planId)
    if (!selectedPlan?.maxUsers) return 999999

    const remainingSlots = selectedPlan.maxUsers - currentTenantSubscribers.value
    return Math.max(1, remainingSlots)
  }

  // 讀取訂閱人數描述
  const getSubscriberLimitDescription = (planId: string) => {
    if (!planId) {
      const currentUsers = currentTenantSubscribers.value
      const totalRequestedUsers = currentUsers + selectedSubscribers.value
      const recommendedPlan = currentPlanOptions.value
        .filter(plan => !plan.maxUsers || plan.maxUsers > totalRequestedUsers)
        .sort((a, b) => {
          if (a.maxUsers && b.maxUsers) return a.maxUsers - b.maxUsers
          if (a.maxUsers) return -1
          if (b.maxUsers) return 1
          return a.price - b.price
        })[0]

      return `目前租戶已訂閱 ${currentUsers} 人，預計新增 ${selectedSubscribers.value} 人${
        recommendedPlan 
          ? `\n\n推薦方案：\n${recommendedPlan.name}${
              recommendedPlan.maxUsers 
                ? ` (上限 ${recommendedPlan.maxUsers} 人，可再新增 ${recommendedPlan.maxUsers - currentUsers} 人)` 
                : ' (無人數限制)'
            }`
          : ''
      }`
    }

    const selectedPlan = currentPlanOptions.value.find(plan => plan.id === planId)
    if (!selectedPlan) return '無法找到所選方案'

    const maxUsers = selectedPlan.maxUsers
    const currentUsers = currentTenantSubscribers.value
    const selectedUsers = selectedSubscribers.value

    if (!maxUsers || maxUsers >= 999) {
      return currentUsers > 0 
        ? `目前租戶已訂閱 ${currentUsers} 人，此方案無額外人數限制`
        : '此方案無使用者數量限制'
    }

    const remainingSlots = maxUsers - currentUsers
    const totalRequestedUsers = currentUsers + selectedUsers

    if (remainingSlots <= 0) {
      const upgradePlan = findUpgradePlan(totalRequestedUsers)
      return `已達到方案上限 (${maxUsers} 人)${
        upgradePlan
          ? `，建議升級至 ${upgradePlan.name}${
              upgradePlan.maxUsers 
                ? ` (上限 ${upgradePlan.maxUsers} 人)` 
                : ' (無人數限制)'
            }`
          : ''
      }`
    }

    if (selectedUsers > remainingSlots) {
      const upgradePlan = findUpgradePlan(totalRequestedUsers)
      return `超出可用名額！剩餘可用名額: ${remainingSlots} 人 (上限 ${maxUsers} 人)${
        upgradePlan
          ? `，建議升級至 ${upgradePlan.name}${
              upgradePlan.maxUsers 
                ? ` (上限 ${upgradePlan.maxUsers} 人)` 
                : ' (無人數限制)'
            }`
          : ''
      }`
    }

    return currentUsers > 0
      ? `租戶已訂閱 ${currentUsers} 人，尚可新增 ${remainingSlots} 人 (上限 ${maxUsers} 人)`
      : `可訂閱人數上限: ${maxUsers} 人`
  }

  // 尋找升級方案
  const findUpgradePlan = (totalRequestedUsers: number) => {
    return currentPlanOptions.value
      .filter(plan => !plan.maxUsers || plan.maxUsers >= totalRequestedUsers)
      .sort((a, b) => {
        if (a.maxUsers && b.maxUsers) return a.maxUsers - b.maxUsers
        if (a.maxUsers) return -1
        if (b.maxUsers) return 1
        return a.price - b.price
      })[0]
  }

  // 計算訂單金額
  const calculateOrderAmount = (
    planPrice: number, 
    period: number, 
    subscribers: number,
    discount: number = 100
  ) => {
    if (!planPrice || !period || !subscribers) return 0;
    
    const validSubscribers = Math.max(1, subscribers);
    const validPeriod = Math.max(1, period);
    const validDiscount = Math.min(Math.max(0, discount), 100);
    
    // 計算基本金額
    const baseAmount = planPrice * validPeriod * validSubscribers;
    
    // 應用折扣並四捨五入到整數
    return Math.round(baseAmount * (validDiscount / 100));
  }

  // 更新訂單金額
  const updateOrderAmount = () => {
    const amount = calculateOrderAmount(
      selectedPlanPrice.value,
      selectedPeriod.value,
      selectedSubscribers.value,
      customDiscount.value
    );
    
    calculatedAmount.value = amount;
    form.setFieldValue('amount', amount);
  }

  // 計算結束日期
  const calculateEndDate = (startDate: string, period: number) => {
    try {
      const date = new Date(startDate)
      if (isNaN(date.getTime())) return ''
      date.setMonth(date.getMonth() + period)
      return date.toISOString().split('T')[0]
    } catch (error) {
      console.error('計算結束日期時發生錯誤:', error)
      return ''
    }
  }

  // ======== 欄位變更處理 ========
  
  // 處理租戶選擇變更
  const handleTenantChange = (val: string | null) => {
    const tenantId = val ?? '';
    form.setFieldValue('tenantId', tenantId);
    
    const subscriberCount = tenantSubscribers[tenantId] || 0;
    updateTenantSubscribers(subscriberCount);
    
    // 檢查人數限制
    if (form.values.planId) {
      const maxUsers = getCurrentPlanMaxUsers(form.values.planId);
      if (selectedSubscribers.value > maxUsers) {
        handleSubscribersChange(maxUsers, form.values.planId);
        form.setFieldValue('numberOfSubscribers', maxUsers);
      }
    }
    
    updateOrderAmount();
  }
  
  // 處理方案選擇變更
  const handlePlanChange = (val: string | null) => {
    const planId = val ?? '';
    form.setFieldValue('planId', planId);
    
    const selectedPlan = currentPlanOptions.value.find(p => p.id === planId);
    if (selectedPlan) {
      selectedPlanPrice.value = selectedPlan.price || 0;
      
      // 檢查人數限制
      const maxUsers = getCurrentPlanMaxUsers(planId);
      if (selectedSubscribers.value > maxUsers) {
        handleSubscribersChange(maxUsers, planId);
        form.setFieldValue('numberOfSubscribers', maxUsers);
      }
    } else {
      selectedPlanPrice.value = 0;
    }
    
    updateOrderAmount();
  }
  
  // 處理開始日期變更
  const handleStartDateChange = (date: Date | undefined) => {
    if (!date) return;
    
    // 調整時區問題，確保日期準確
    const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
    const dateStr = localDate.toISOString().split('T')[0];
    
    form.setFieldValue('startDate', dateStr);
    updateOrderAmount();
  }

  // 處理期間選擇
  const handlePeriodSelect = (period: number) => {
    if (!isNaN(period) && period >= 1) {
      selectedPeriod.value = period;
      form.setFieldValue('period', period);
      
      // 自動套用期間折扣
      let discount = 100;
      if (period >= 12) {
        discount = 80;  // 12個月以上打8折
      } else if (period >= 6) {
        discount = 85;  // 6個月以上打85折
      } else if (period >= 3) {
        discount = 90;  // 3個月以上打9折
      }
      
      // 更新折扣值
      customDiscount.value = discount;
      
      // 更新訂單金額
      const amount = calculateOrderAmount(
        selectedPlanPrice.value,
        period,
        selectedSubscribers.value,
        discount
      );
      calculatedAmount.value = amount;
      form.setFieldValue('amount', amount);
    }
  }

  // 處理訂閱人數變更
  const handleSubscribersChange = (value: number, planId?: string) => {
    if (isNaN(value) || value < 1) {
      selectedSubscribers.value = 1;
      form.setFieldValue('numberOfSubscribers', 1);
      return;
    }

    const maxUsers = getCurrentPlanMaxUsers(planId || '');
    const validValue = Math.min(Math.max(1, value), maxUsers);
    
    if (validValue !== selectedSubscribers.value) {
      selectedSubscribers.value = validValue;
      form.setFieldValue('numberOfSubscribers', validValue);
      updateOrderAmount();
    }
  }

  // 處理折扣變更
  const handleDiscountChange = (value: number) => {
    if (!isNaN(value)) {
      customDiscount.value = Math.min(Math.max(0, value), 100);
      updateOrderAmount();
    }
  }

  // 套用期間折扣
  const applyPeriodDiscount = () => {
    const period = selectedPeriod.value;
    // 根據期間長度套用不同折扣
    let discount = 100;
    if (period >= 12) {
      discount = 80;  // 12個月以上打8折
    } else if (period >= 6) {
      discount = 85;  // 6個月以上打85折
    } else if (period >= 3) {
      discount = 90;  // 3個月以上打9折
    }
    
    customDiscount.value = discount;
    updateOrderAmount();
    
    // 更新表單金額
    const amount = calculateOrderAmount(
      selectedPlanPrice.value,
      selectedPeriod.value,
      selectedSubscribers.value,
      discount
    );
    form.setFieldValue('amount', amount);
  }

  // 更新選項
  const updateOptions = (newOptions: Partial<UseOrderFormOptions>) => {
    console.log('更新 orderForm 選項:', newOptions);
    
    if (newOptions.selectedOrder !== undefined) {
      internalOptions.value.selectedOrder = newOptions.selectedOrder;
    }
    
    if (newOptions.isEditMode !== undefined) {
      internalOptions.value.isEditMode = newOptions.isEditMode;
    }
    
    if (newOptions.tenantOptions && Array.isArray(newOptions.tenantOptions)) {
      internalOptions.value.tenantOptions = newOptions.tenantOptions;
    }
    
    if (newOptions.planOptions && Array.isArray(newOptions.planOptions)) {
      internalOptions.value.planOptions = newOptions.planOptions;
    }
    
    if (newOptions.tenantSubscribers) {
      internalOptions.value.tenantSubscribers = newOptions.tenantSubscribers;
    }
    
    console.log('更新後的選項:', internalOptions.value);
  }

  // 初始化表單數據
  const initializeForm = async () => {
    // 使用內部選項
    const { isEditMode, selectedOrder, tenantOptions, planOptions: internalPlanOptions, tenantSubscribers: internalTenantSubscribers } = internalOptions.value;
    
    console.log('initForm', { 
      isEditMode, 
      selectedOrder, 
      tenantOptions, 
      planOptions: internalPlanOptions,
      tenantSubscribers: internalTenantSubscribers
    });
    
    // 重置表單值
    const initialValues = {
      ...getInitialValues(),
      tenantId: tenantOptions.length > 0 ? tenantOptions[0].id : '',
      planId: currentPlanOptions.value.length > 0 ? currentPlanOptions.value[0].id : ''
    };

    if (isEditMode && selectedOrder) {
      // 找到對應的租戶和方案，只用名稱對應
      console.log('尋找租戶和方案:', { tenantName: selectedOrder.tenantName, planName: selectedOrder.planName });
      console.log('可用租戶選項:', tenantOptions);
      console.log('可用方案選項:', currentPlanOptions.value);
      
      // 還能使用 ID 查找，如果 API 返回的訂單包含 tenantId 和 planId
      let tenant = tenantOptions.find(t => t.id === (selectedOrder as any).tenantId);
      if (!tenant) {
        tenant = tenantOptions.find(t => t.name === selectedOrder.tenantName);
      }
      
      let plan = currentPlanOptions.value.find(p => p.id === (selectedOrder as any).planId);
      if (!plan) {
        plan = currentPlanOptions.value.find(p => p.name === selectedOrder.planName);
      }
      
      console.log('找到的租戶和方案:', { tenant, plan });
      
      if (tenant && plan) {
        // 更新表單欄位
        initialValues.tenantId = tenant.id;
        initialValues.planId = plan.id;
        initialValues.startDate = selectedOrder.startDate ? selectedOrder.startDate.split('T')[0] : today(getLocalTimeZone()).toString();
        initialValues.period = Number(selectedOrder.period) || 1;
        initialValues.numberOfSubscribers = Number(selectedOrder.numberOfSubscribers) || 1;
        initialValues.remarks = selectedOrder.remarks || '';
        initialValues.amount = Number(selectedOrder.amount) || 0;
        
        // 更新狀態
        selectedPlanPrice.value = plan.price || 0;
        selectedPeriod.value = Number(selectedOrder.period) || 1;
        selectedSubscribers.value = Number(selectedOrder.numberOfSubscribers) || 1;
        
        // 計算折扣
        if (selectedOrder.amount && plan.price > 0 && selectedOrder.period > 0 && selectedOrder.numberOfSubscribers > 0) {
          const baseAmount = plan.price * selectedOrder.period * selectedOrder.numberOfSubscribers;
          const calculatedDiscount = baseAmount > 0 ? Math.round((selectedOrder.amount / baseAmount) * 100) : 100;
          customDiscount.value = calculatedDiscount || 100;
        } else {
          customDiscount.value = 100;
        }
      } else {
        // 如果找不到精確匹配，嘗試部分匹配或使用默認值
        console.warn('無法精確匹配租戶或方案，嘗試備選方案');
        
        // 嘗試部分匹配，如果租戶名稱包含在options內的名稱中
        const partialTenantMatch = !tenant && selectedOrder.tenantName ? 
          tenantOptions.find(t => t.name.includes(selectedOrder.tenantName) || selectedOrder.tenantName.includes(t.name)) : null;
        
        const partialPlanMatch = !plan && selectedOrder.planName ? 
          currentPlanOptions.value.find(p => p.name.includes(selectedOrder.planName) || selectedOrder.planName.includes(p.name)) : null;
        
        console.log('部分匹配結果:', { partialTenantMatch, partialPlanMatch });
        
        // 使用匹配到的租戶或默認第一個
        if (partialTenantMatch) {
          initialValues.tenantId = partialTenantMatch.id;
        }
        
        // 使用匹配到的方案或默認第一個
        if (partialPlanMatch) {
          initialValues.planId = partialPlanMatch.id;
          selectedPlanPrice.value = partialPlanMatch.price || 0;
        }
        
        // 剩下的欄位仍然使用訂單值
        initialValues.startDate = selectedOrder.startDate ? selectedOrder.startDate.split('T')[0] : today(getLocalTimeZone()).toString();
        initialValues.period = Number(selectedOrder.period) || 1;
        initialValues.numberOfSubscribers = Number(selectedOrder.numberOfSubscribers) || 1;
        initialValues.remarks = selectedOrder.remarks || '';
        initialValues.amount = Number(selectedOrder.amount) || 0;
        
        // 更新狀態
        selectedPeriod.value = Number(selectedOrder.period) || 1;
        selectedSubscribers.value = Number(selectedOrder.numberOfSubscribers) || 1;
        customDiscount.value = 100;
      }
    } else {
      // 新增模式：使用第一個可用的租戶和方案
      const firstPlan = currentPlanOptions.value[0];
      if (firstPlan) {
        selectedPlanPrice.value = firstPlan.price || 0;
      }
      selectedPeriod.value = 1;
      selectedSubscribers.value = 1;
      customDiscount.value = 100;
    }
    
    // 防護檢查：確保 tenantId 和 planId 不為空
    if (!initialValues.tenantId && tenantOptions.length > 0) {
      console.warn('tenantId 為空，使用第一個選項', tenantOptions[0]);
      initialValues.tenantId = tenantOptions[0].id;
    }
    
    if (!initialValues.planId && currentPlanOptions.value.length > 0) {
      console.warn('planId 為空，使用第一個選項', currentPlanOptions.value[0]);
      initialValues.planId = currentPlanOptions.value[0].id;
      selectedPlanPrice.value = currentPlanOptions.value[0].price || 0;
    }
    
    // 更新租戶訂閱數
    const selectedTenantId = initialValues.tenantId;
    updateTenantSubscribers(selectedTenantId ? tenantSubscribers[selectedTenantId] || 0 : 0);
    
    // 重置表單
    await form.resetForm({ values: initialValues });
    
    // 更新訂單金額
    updateOrderAmount();
    
    console.log('Form initialized with values:', initialValues);
  };

  // 提交表單
  const submitForm = async () => {
    loading.value = true;
    try {
      // 執行前端欄位驗證
      const result = await form.validate();
      if (!result.valid) {
        // 顯示驗證錯誤提示，並中止提交
        notification.toast.error('表單驗證失敗，請檢查輸入內容', { title: '驗證錯誤' });
        return;
      }
      // 取得表單值
      const formValues = form.values;
      // 處理空字串欄位，避免送空字串
      if (formValues.contactEmail === '') formValues.contactEmail = undefined as any;
      if (formValues.contactName === '') formValues.contactName = undefined as any;
      if (formValues.remarks === '') formValues.remarks = undefined as any;
      if (formValues.paymentMethod === '') formValues.paymentMethod = '銀行轉帳';
      // 計算結束日期
      const endDate = calculateEndDate(formValues.startDate, formValues.period);
      if (!endDate) {
        notification.toast.error('無法計算結束日期', { title: '錯誤' });
        return;
      }
      // 計算最終金額
      const finalAmount = calculateOrderAmount(
        selectedPlanPrice.value,
        formValues.period,
        formValues.numberOfSubscribers,
        customDiscount.value
      );
      // 構建提交資料
      const submitData = {
        ...formValues,
        amount: finalAmount,
        endDate,
        discount: customDiscount.value,
        orderHistory: internalOptions.value.isEditMode ? [
          {
            type: 'info',
            date: new Date().toISOString(),
            status: '訂單已更新',
            description: '訂單資訊已被修改',
            by: 'System'
          },
          ...(internalOptions.value.selectedOrder?.orderHistory || [])
        ] : [
          {
            type: 'info',
            date: new Date().toISOString(),
            status: '訂單已建立',
            description: '訂單已成功建立，等待處理',
            by: null
          }
        ]
      };
      // 呼叫 onSubmit 回調
      if (handleSubmit) handleSubmit(submitData);
      return submitData;
    } catch (error) {
      console.error('submitForm 發生錯誤:', error);
      notification.toast.error('表單提交失敗，請稍後再試', { title: '錯誤' });
    } finally {
      loading.value = false;
    }
  };

  // 監聽方案列表變化
  watch(() => initialPlanOptions, (newPlanOptions) => {
    updatePlanOptions(newPlanOptions);
    
    // 如果沒有選擇方案，預設選擇第一個
    if (!form.values.planId && newPlanOptions.length > 0) {
      const firstPlan = newPlanOptions[0];
      form.setFieldValue('planId', firstPlan.id);
      selectedPlanPrice.value = firstPlan.price || 0;
      updateOrderAmount();
    }
  }, { deep: true, immediate: true });

  // 監聽方案變更
  watch(() => form.values.planId, (newPlanId, oldPlanId) => {
    if (!newPlanId || newPlanId === oldPlanId) return;
    
    const selectedPlan = availablePlans.value.find(p => p.id === newPlanId);
    if (!selectedPlan) {
      console.warn('找不到選擇的方案:', newPlanId);
      return;
    }
    
    selectedPlanPrice.value = selectedPlan.price || 0;
    
    // 檢查人數限制
    const maxUsers = getCurrentPlanMaxUsers(newPlanId);
    if (selectedSubscribers.value > maxUsers) {
      handleSubscribersChange(maxUsers, newPlanId);
      form.setFieldValue('numberOfSubscribers', maxUsers);
    }
    
    updateOrderAmount();
  });

  // 監聽租戶選擇變化
  watch(() => form.values.tenantId, (newTenantId) => {
    if (!newTenantId) {
      updateTenantSubscribers(0);
      return;
    }
    
    const subscriberCount = tenantSubscribers[newTenantId] || 0;
    updateTenantSubscribers(subscriberCount);
    
    // 檢查人數限制
    if (form.values.planId) {
      const maxUsers = getCurrentPlanMaxUsers(form.values.planId);
      if (selectedSubscribers.value > maxUsers) {
        selectedSubscribers.value = maxUsers;
        form.setFieldValue('numberOfSubscribers', maxUsers);
      }
    }
    
    updateOrderAmount();
  });

  // 初始化
  initializeForm();

  return {
    // 表單和狀態
    form,
    loading,
    selectedPlanPrice,
    selectedPeriod,
    selectedSubscribers,
    customDiscount,
    calculatedAmount,
    formattedAmountDisplay,
    baseAmount,
    formattedBaseAmount,
    subtotalAmount,
    formattedSubtotalAmount,
    periodOptions,
    currentPlanOptions,
    availablePlans,
    
    // 欄位處理函數
    handleTenantChange,
    handlePlanChange,
    handleStartDateChange,
    handlePeriodSelect,
    handleSubscribersChange,
    handleDiscountChange,
    applyPeriodDiscount,
    
    // 工具函數
    getCurrentPlanMaxUsers,
    getSubscriberLimitDescription,
    calculateEndDate,
    updatePlanOptions,
    updateTenantSubscribers,
    
    // 初始化和提交
    initializeForm,
    submitForm,
    updateOptions,
    
    // 工具
    formatters
  }
}
