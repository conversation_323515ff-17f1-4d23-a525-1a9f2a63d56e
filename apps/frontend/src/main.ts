import { createApp, reactive } from "vue";
import { create<PERSON><PERSON> } from "pinia";
import { Toaster } from "@/components/ui/toast";
import App from "./App.vue";
import { initAuth } from "@horizai/auth";
import { authConfig as appAuthConfig } from "@/config/auth.config";
import router from "./router";
import "@/assets/styles/main.css";
// HTTP 服務已在 auth 套件中初始化

// 過濾已知的 Radix-Vue aria-hidden 警告
const originalConsoleWarn = console.warn;
console.warn = (...args) => {
  try {
    // 安全地檢查參數中是否有字串類型且包含需要過濾的內容
    let shouldFilter = false;

    // 只檢查字串類型的參數
    for (const arg of args) {
      if (typeof arg === "string") {
        if (
          arg.includes("Blocked aria-hidden on an element") ||
          arg.includes(
            "focus must not be hidden from assistive technology users"
          )
        ) {
          shouldFilter = true;
          break;
        }
      }
    }

    // 過濾掉 Radix-Vue 的已知 aria-hidden 問題警告
    if (shouldFilter) {
      return; // 靜默處理這個警告
    }

    // 其他警告正常顯示
    originalConsoleWarn.apply(console, args);
  } catch (error) {
    // 如果過濾過程中出現任何錯誤，仍然顯示原始警告
    originalConsoleWarn.apply(console, args);
  }
};

async function bootstrap() {
  console.log("啟動應用程序...");
  console.log("API 基礎 URL:", appAuthConfig.baseURL);
  console.log("認證配置:", {
    loginEndpoint: appAuthConfig.loginEndpoint,
    logoutEndpoint: appAuthConfig.logoutEndpoint,
    refreshTokenEndpoint: appAuthConfig.refreshTokenEndpoint,
    meEndpoint: appAuthConfig.meEndpoint,
  });

  // 2. Initialize the @horizai/auth package.
  // It will now use its own internal HttpService for authentication operations.
  // We no longer pass httpServiceInstance to it.
  const app = createApp(App);

  // 註冊Pinia狀態管理
  const pinia = createPinia();
  app.use(pinia);

  // Wrap auth config in reactive so toRefs() receives a reactive object
  await initAuth(
    app,
    reactive({
      ...appAuthConfig,
      // tokenExpiryHandler will be set up by initAuth to call authStore.logout
    })
  );

  // 設置全域登出事件監聽器
  setupGlobalLogoutListeners();

  // 註冊全局組件
  app.component("Toaster", Toaster);

  // 認證初始化完成後再註冊 router
  app.use(router);

  // 掛載應用
  console.log("應用程序啟動成功，即將掛載主應用");
  app.mount("#app");

  // 應用程序啟動成功
}

/**
 * 設置全域登出事件監聽器
 */
function setupGlobalLogoutListeners() {
  // 監聽來自 HTTP service 的自動登出事件
  window.addEventListener('auth:logout', async (event: any) => {
    console.log('收到自動登出事件:', event.detail);

    try {
      const { useAuthStore } = await import('@horizai/auth');
      const authStore = useAuthStore();

      // 清除前端狀態
      authStore.resetState();

      // 清除本地儲存
      const { handleLogoutRedirect } = await import('@/utils/redirect.utils');
      handleLogoutRedirect();

      // 重導向到登入頁面
      const router = (await import('@/router')).default;
      const reason = event.detail?.reason || 'session_expired';

      await router.push({
        path: '/auth/login',
        query: { reason }
      });

      console.log('自動登出完成');
    } catch (error) {
      console.error('處理自動登出事件時發生錯誤:', error);
      // 備用方案：強制跳轉
      window.location.href = '/auth/login?reason=auto_logout_error';
    }
  });

  // 監聽登出完成事件
  window.addEventListener('auth:logout-complete', () => {
    console.log('登出完成事件已觸發');
  });
}

// 確保 DOM 加載完成後再啟動應用
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => bootstrap());
} else {
  bootstrap();
}
