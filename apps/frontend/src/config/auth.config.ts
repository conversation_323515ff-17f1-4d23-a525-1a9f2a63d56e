import { defineAbility } from "@casl/ability";
import type { AuthConfig } from "@horizai/auth";
import { useAuthStore } from "@horizai/auth";
import { Actions, Subjects } from "@horizai/permissions";

export const defaultAbility = defineAbility((can) => {
  can(Actions.READ, Subjects.ALL);
});

export const AUTH_CONSTANTS = {
  baseURL: import.meta.env?.VITE_API_BASE_URL || "/api",
  loginEndpoint: "auth/login",
  logoutEndpoint: "auth/logout",
  userEndpoint: "auth/me",
  refreshTokenEndpoint: "auth/refresh-token",
} as const;

// Token 過期處理器
let isRedirecting = false; // 防止重複重定向的標記

const tokenExpiryHandler = async () => {
  console.log("tokenExpiryHandler: Token 過期，開始處理...");

  // 防止重複重定向
  if (isRedirecting) {
    console.log("tokenExpiryHandler: 已在重定向中，跳過");
    return;
  }

  isRedirecting = true;

  try {
    const authStore = useAuthStore();

    // 嘗試登出，但不要因為登出失敗而阻止重定向
    try {
      await authStore.logout();
      console.log("tokenExpiryHandler: 已登出");
    } catch (logoutError) {
      console.warn(
        "tokenExpiryHandler: 登出失敗，但繼續清除本地狀態:",
        logoutError
      );
      // 即使登出 API 失敗，也要清除本地狀態
      // 注意：logout() 方法已經會清除本地狀態，這裡不需要額外操作
    }

    // 重定向到登入頁面
    const router = (await import("@/router")).default;
    const currentPath = router.currentRoute.value.fullPath;

    // 只有當前不在登入頁面時才重定向
    if (!currentPath.startsWith("/auth/login")) {
      console.log("tokenExpiryHandler: 重定向到登入頁面");
      await router.push({
        path: "/auth/login",
        query: {
          redirect: currentPath,
          reason: "session_expired",
        },
      });
    } else {
      console.log("tokenExpiryHandler: 已在登入頁面，不需要重定向");
    }
  } catch (error) {
    console.error("tokenExpiryHandler: 處理過程中發生錯誤:", error);
    // 即使發生錯誤，也嘗試重定向到登入頁面
    try {
      window.location.href = "/auth/login?reason=session_expired";
    } catch (redirectError) {
      console.error("tokenExpiryHandler: 備用重定向也失敗:", redirectError);
    }
  } finally {
    // 延遲重置標記，避免快速重複調用
    setTimeout(() => {
      isRedirecting = false;
    }, 1000);
  }
};

export const authConfig: AuthConfig = {
  baseURL: AUTH_CONSTANTS.baseURL,
  loginEndpoint: AUTH_CONSTANTS.loginEndpoint,
  logoutEndpoint: AUTH_CONSTANTS.logoutEndpoint,
  refreshTokenEndpoint: AUTH_CONSTANTS.refreshTokenEndpoint,
  userEndpoint: AUTH_CONSTANTS.userEndpoint,
  tokenExpiryHandler: tokenExpiryHandler,
};
