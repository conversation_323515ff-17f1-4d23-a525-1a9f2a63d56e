<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card'
import {
  CheckIcon, ArrowRightIcon, ChartBarIcon, CalendarIcon, CurrencyDollarIcon, FolderIcon,
  BeakerIcon, CpuChipIcon, LightBulbIcon, RocketLaunchIcon
} from '@heroicons/vue/24/outline'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@horizai/auth'
import { useWorkspaceStore } from '@/stores/workspace.store'
import { useNotification } from '@/composables/shared/useNotification'
import { useFlashMessage } from '@/composables/shared/useFlashMessage'
import Logo from '@/assets/images/logo.svg'
import { getDefaultRedirectPath } from '@/utils/redirect.utils'

const router = useRouter()
const workspaceStore = useWorkspaceStore()
const notification = useNotification()
const flash = useFlashMessage()
const authStore = useAuthStore()

// 載入狀態
const isLoading = ref(false)

// 新增 section refs
const featuresSection = ref<HTMLElement | null>(null)
const pricingSection = ref<HTMLElement | null>(null)
const testimonialsSection = ref<HTMLElement | null>(null)

// Hero 區域的標語輪播
const heroTexts = [
  {
    title: "智能專案管理的未來",
    subtitle: "AI 驅動的決策支援，讓每個專案都能精準執行",
    highlight: "未來"
  },
  {
    title: "數據驅動的敏捷轉型",
    subtitle: "即時分析與預測，為您的團隊帶來革命性突破",
    highlight: "敏捷"
  },
  {
    title: "打造高效能工作流",
    subtitle: "自動化工作流程，釋放團隊創新潛能",
    highlight: "高效"
  },
  {
    title: "全方位專案掌控",
    subtitle: "從構想到實現，每一步都在掌握之中",
    highlight: "掌控"
  }
]

const currentHeroIndex = ref(0)
const isTransitioning = ref(false)

const nextHero = () => {
  isTransitioning.value = true
  setTimeout(() => {
    currentHeroIndex.value = (currentHeroIndex.value + 1) % heroTexts.length
    isTransitioning.value = false
  }, 500)
}

const features = [
  {
    title: 'AI 智能分析',
    description: '運用先進的機器學習算法，預測專案風險並提供優化建議，提升決策準確度高達 85%。',
    icon: BeakerIcon,
    image: 'https://images.unsplash.com/photo-1555949963-aa79dcee981c?auto=format&fit=crop&q=80'
  },
  {
    title: '即時資源調配',
    description: '智能資源分配系統，自動平衡團隊工作負載，將資源使用效率提升 40%。',
    icon: CpuChipIcon,
    image: 'https://images.unsplash.com/photo-1551434678-e076c223a692?auto=format&fit=crop&q=80'
  },
  {
    title: '敏捷專案追蹤',
    description: '視覺化的敏捷管理工具，讓團隊協作更順暢，溝通效率提升 60%。',
    icon: RocketLaunchIcon,
    image: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80'
  },
  {
    title: '智能文件管理',
    description: 'AI 驅動的文件分類與版本控制，降低 75% 的文件搜尋時間。',
    icon: LightBulbIcon,
    image: 'https://images.unsplash.com/photo-1507925921958-8a62f3d1a50d?auto=format&fit=crop&q=80'
  }
]

const pricingPlans = [
  {
    name: '基礎版',
    price: 'NT$ 1,000',
    description: '適合小型團隊使用',
    features: [
      '最多 5 個專案',
      '基本甘特圖功能',
      '文件管理',
      'Email 支援'
    ],
    image: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?auto=format&fit=crop&q=80'
  },
  {
    name: '專業版',
    price: 'NT$ 3,000',
    description: '適合中型企業使用',
    features: [
      '無限專案數量',
      '進階甘特圖功能',
      '預算管理',
      '優先支援',
      'API 存取'
    ],
    popular: true,
    image: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?auto=format&fit=crop&q=80'
  },
  {
    name: '企業版',
    price: 'NT$ 8,000',
    description: '適合大型企業使用',
    features: [
      '所有專業版功能',
      '客製化整合',
      '專屬支援',
      '進階報表',
      '多語言支援'
    ],
    image: 'https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?auto=format&fit=crop&q=80'
  }
]

const testimonials = [
  {
    name: '張志明',
    role: '專案經理',
    company: '科技創新公司',
    content: 'HorizAI 徹底改變了我們的專案管理方式，AI 驅動的建議非常實用。',
    image: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?auto=format&fit=crop&q=80'
  },
  {
    name: '林美玲',
    role: '產品總監',
    company: '數位科技',
    content: '直觀的介面和強大的功能，讓團隊協作更加順暢。',
    image: 'https://images.unsplash.com/photo-1494790108377-be9c29b29330?auto=format&fit=crop&q=80'
  }
]

// 處理「進入儀表板」跳轉
const goToDashboard = async () => {
  if (isLoading.value) return
  isLoading.value = true
  try {
    // 確認認證與使用者資訊
    if (!authStore.isAuthenticated || !authStore.user) {
      notification.toast.error('請先登入')
      return router.push({ name: 'login', query: { redirect: '/admin/dashboard' } })
    }
    // 使用集中導航工具根據角色決定路徑
    const target = getDefaultRedirectPath(authStore.user)
    await router.replace(target)
  } catch (e: any) {
    console.error('HomeView: 導向儀表板失敗', e)
    notification.toast.error('導航失敗，請稍後再試')
  } finally {
    isLoading.value = false
  }
}

// 滾動函數
const scrollToSection = (section: HTMLElement | null) => {
  if (section) {
    section.scrollIntoView({ behavior: 'smooth' })
  }
}

// 錯誤處理函數
const errorHandler = (error: any, message: string) => {
  console.error(message, error)
  flash.error('連接發生錯誤，請稍後再試')
}

// 改進：完全重構組件掛載邏輯
onMounted(() => {
  console.log('HomeView: 元件掛載')
  
  // 設置載入狀態保護
  const loadingTimeout = setTimeout(() => {
    if (isLoading.value) {
      console.log('HomeView: 偵測到載入時間過長，強制取消載入狀態')
      isLoading.value = false
    }
  }, 3000)
  
  // 組件卸載時清理資源
  onUnmounted(() => {
    clearTimeout(loadingTimeout)
  })
})
</script>

<template>
  <div class="min-h-screen">
    <!-- Loading Overlay -->
    <div v-if="isLoading" class="fixed inset-0 z-50 bg-white/80 backdrop-blur-sm flex items-center justify-center">
      <div class="text-center">
        <div class="animate-spin rounded-full h-12 w-12 border-4 border-emerald-500 border-t-transparent"></div>
        <p class="mt-4 text-zinc-600">載入中...</p>
      </div>
    </div>
  
    <!-- Hero Section -->
    <section
      class="relative min-h-screen flex items-center overflow-hidden bg-gradient-to-b from-emerald-50 to-emerald-100/95">
      <!-- 頂部導航 -->
      <nav class="fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-white/50 border-b border-emerald-100">
        <div class="container mx-auto px-6">
          <div class="h-20 flex items-center justify-between">
            <!-- Logo -->
            <div class="flex items-center gap-2">
              <img :src="Logo" alt="HorizAI Logo"
                class="h-8 w-auto transition-transform duration-300 hover:scale-105" />
            </div>

            <!-- 導航連結 -->
            <div class="hidden md:flex items-center gap-8">
              <button
                class="text-base font-medium hover:text-emerald-600 transition-colors relative group"
                @click="scrollToSection(featuresSection)">
                產品特色
              </button>
              <button
                class="text-base font-medium hover:text-emerald-600 transition-colors relative group"
                @click="scrollToSection(pricingSection)">
                價格方案
              </button>
              <button
                class="text-base font-medium hover:text-emerald-600 transition-colors relative group"
                @click="scrollToSection(testimonialsSection)">
                客戶案例
              </button>
            </div>

            <!-- 登入/儀表板按鈕 -->
            <div class="flex items-center gap-4">
              <template v-if="!authStore.isAuthenticated">
                <button class="text-base font-medium hover:text-emerald-600 transition-colors"
                  @click="router.push('/auth/login')">
                  登入
                </button>
                <button
                  class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2.5 rounded-full text-base font-medium shadow-lg hover:shadow-emerald-600/20 transition-all duration-300 relative overflow-hidden group"
                  @click="router.push('/auth/register')">
                  <span class="relative z-10">免費試用</span>
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000">
                  </div>
                </button>
              </template>
              <template v-else>
                <button
                  class="bg-emerald-600 hover:bg-emerald-700 text-white px-6 py-2.5 rounded-full text-base font-medium shadow-lg hover:shadow-emerald-600/20 transition-all duration-300 relative overflow-hidden group"
                  @click="goToDashboard">
                  <span class="relative z-10">進入儀表板</span>
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000">
                  </div>
                </button>
              </template>
            </div>
          </div>
        </div>
      </nav>

      <!-- 背景效果 -->
      <div class="absolute inset-0 z-0">
        <!-- 動態網格背景 -->
        <div
          class="absolute inset-0 bg-[linear-gradient(to_right,rgba(16,185,129,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(16,185,129,0.05)_1px,transparent_1px)] bg-[size:4rem_4rem]">
        </div>
        <div
          class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.075),transparent_50%)] animate-pulse-slow">
        </div>

        <!-- 裝飾元素 -->
        <div
          class="absolute right-[5%] top-[20%] w-[25rem] h-[25rem] rounded-full bg-emerald-500/5 blur-3xl animate-float">
        </div>
        <div
          class="absolute left-[10%] bottom-[10%] w-[20rem] h-[20rem] rounded-full bg-emerald-500/5 blur-3xl animate-float-delay">
        </div>
      </div>

      <div class="container mx-auto px-6 relative z-10">
        <div class="max-w-[90rem] mx-auto">
          <!-- 主要內容 -->
          <div class="mt-32 mb-24 grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
            <!-- 左側文字區域 -->
            <div class="space-y-8">
              <div
                class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-200">
                <span class="text-xs font-medium text-emerald-600">全新發布</span>
                <span class="w-1 h-1 rounded-full bg-emerald-600"></span>
                <span class="text-xs font-medium text-gray-600">AI 驅動的專案管理平台</span>
              </div>

              <div class="relative">
                <h1 class="text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight leading-[1.1] animate-fade-in-up"
                  style="--delay: 0.2s">
                  <span class="text-gray-900 relative">打造</span>
                  <span
                    class="text-emerald-600 relative inline-block transform hover:scale-105 transition-transform">高效</span>
                  <span class="text-gray-900 relative">能工作流</span>
                </h1>
                <p class="mt-6 text-xl text-gray-600 leading-relaxed max-w-2xl animate-fade-in-up"
                  style="--delay: 0.4s">
                  自動化工作流程，釋放團隊創新潛能。運用 AI 技術，讓每個專案都能精準執行。
                </p>
              </div>

              <div class="flex flex-col sm:flex-row gap-4 pt-4 animate-fade-in-up" style="--delay: 0.6s">
                <button v-if="!authStore.isAuthenticated"
                  class="group bg-emerald-600 hover:bg-emerald-700 text-white rounded-full px-8 py-6 text-base font-medium relative overflow-hidden"
                  @click="router.push('/auth/register')">
                  <span class="relative z-10">開始 14 天免費試用</span>
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000">
                  </div>
                </button>
                <button v-else
                  class="group bg-emerald-600 hover:bg-emerald-700 text-white rounded-full px-8 py-6 text-base font-medium relative overflow-hidden"
                  @click="goToDashboard">
                  <span class="relative z-10">進入儀表板</span>
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000">
                  </div>
                </button>
                <button class="rounded-full px-8 py-6 text-base font-medium border-2 border-emerald-200 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-300">
                  觀看示範
                </button>
              </div>

              <!-- 統計數據 -->
              <div class="grid grid-cols-2 sm:grid-cols-4 gap-8 pt-12 border-t border-emerald-100">
                <div v-for="(stat, index) in [
                  { value: '85%', label: '決策準確度' },
                  { value: '40%', label: '資源利用提升' },
                  { value: '60%', label: '溝通效率提升' },
                  { value: '75%', label: '時間節省' }
                ]" :key="index" class="group">
                  <div
                    class="text-4xl font-bold bg-gradient-to-br from-emerald-600 to-emerald-500 bg-clip-text text-transparent">
                    {{ stat.value }}
                  </div>
                  <div class="mt-2 text-sm font-medium text-gray-500 group-hover:text-gray-900 transition-colors">
                    {{ stat.label }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 右側圖片區域 -->
            <div class="relative aspect-square w-full max-w-2xl mx-auto lg:mx-0">
              <div
                class="absolute inset-0 rounded-3xl overflow-hidden border border-emerald-200 bg-gradient-to-br from-emerald-50 to-transparent backdrop-blur-sm">
                <img src="https://images.unsplash.com/photo-1460925895917-afdab827c52f?auto=format&fit=crop&q=80"
                  alt="Hero Image" class="w-full h-full object-cover scale-105 animate-slow-zoom" />
                <div class="absolute inset-0 bg-gradient-to-br from-white/50 to-white/30 mix-blend-overlay"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section ref="featuresSection" class="py-32 relative overflow-hidden bg-white">
      <div class="absolute inset-0">
        <div
          class="absolute inset-0 bg-[linear-gradient(to_right,rgba(16,185,129,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(16,185,129,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]">
        </div>
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.03),transparent_50%)]">
        </div>
      </div>

      <div class="container mx-auto px-6 relative">
        <div class="text-center max-w-3xl mx-auto mb-20">
          <div
            class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-200 mb-6">
            <span class="text-xs font-medium text-emerald-600">產品功能</span>
            <span class="w-1 h-1 rounded-full bg-emerald-600"></span>
            <span class="text-xs font-medium text-gray-600">智能化專案管理</span>
          </div>
          <h2 class="text-4xl font-bold mb-6 text-gray-900">強大功能，簡單使用</h2>
          <p class="text-xl text-gray-600">
            整合 AI 技術的專案管理工具，讓您的團隊更有效率地完成專案
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div v-for="feature in features" :key="feature.title"
            class="group relative overflow-hidden rounded-3xl bg-white shadow-lg border border-emerald-100 hover:border-emerald-200 transition-all duration-500">
            <div class="absolute inset-0">
              <div
                class="absolute inset-0 bg-gradient-to-br from-emerald-50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500">
              </div>
            </div>
            <div class="relative p-8">
              <div class="flex items-center gap-4 mb-6">
                <div class="p-3 rounded-2xl bg-emerald-50 ring-1 ring-emerald-200">
                  <component :is="feature.icon" class="w-8 h-8 text-emerald-600" />
                </div>
                <h3 class="text-2xl font-semibold text-gray-900">{{ feature.title }}</h3>
              </div>
              <p class="text-gray-600 mb-6 leading-relaxed">{{ feature.description }}</p>
              <button class="group/btn text-emerald-600 hover:text-emerald-700">
                了解更多
                <ArrowRightIcon class="w-4 h-4 ml-2 group-hover/btn:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section ref="pricingSection" class="py-32 relative overflow-hidden bg-gradient-to-b from-emerald-50 to-white">
      <div class="absolute inset-0">
        <div
          class="absolute inset-0 bg-[linear-gradient(to_right,rgba(16,185,129,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(16,185,129,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]">
        </div>
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.03),transparent_50%)]">
        </div>
      </div>

      <div class="container mx-auto px-6 relative">
        <div class="text-center max-w-3xl mx-auto mb-20">
          <div
            class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-200 mb-6">
            <span class="text-xs font-medium text-emerald-600">價格方案</span>
            <span class="w-1 h-1 rounded-full bg-emerald-600"></span>
            <span class="text-xs font-medium text-gray-600">彈性選擇</span>
          </div>
          <h2 class="text-4xl font-bold mb-6 text-gray-900">選擇最適合您的方案</h2>
          <p class="text-xl text-gray-600">
            靈活的價格方案，滿足不同規模團隊的需求
          </p>
        </div>

        <div class="grid md:grid-cols-3 gap-8">
          <div v-for="plan in pricingPlans" :key="plan.name" class="relative">
            <!-- 最受歡迎標籤 -->
            <div v-if="plan.popular" class="absolute -top-4 left-1/2 -translate-x-1/2 z-10">
              <span class="bg-emerald-600 text-white px-6 py-2 rounded-full text-sm font-medium shadow-lg">
                最受歡迎
              </span>
            </div>

            <div :class="[
              'group relative overflow-hidden rounded-3xl transition-all duration-500 backdrop-blur-sm bg-white',
              plan.popular ? 'border-2 border-emerald-400 shadow-xl scale-105' : 'border border-emerald-200'
            ]">
              <div class="relative p-8">
                <div class="text-center mb-8">
                  <h3 class="text-2xl font-bold mb-2 text-gray-900">{{ plan.name }}</h3>
                  <div class="text-4xl font-bold text-emerald-600 mb-2">{{ plan.price }}</div>
                  <p class="text-gray-600">{{ plan.description }}</p>
                </div>

                <ul class="space-y-4 mb-8">
                  <li v-for="feature in plan.features" :key="feature"
                    class="flex items-center gap-3 text-gray-600 group-hover:text-gray-900 transition-colors">
                    <div class="p-1 rounded-full bg-emerald-100">
                      <CheckIcon class="w-4 h-4 text-emerald-600" />
                    </div>
                    <span>{{ feature }}</span>
                  </li>
                </ul>

                <button :class="[
                  'w-full rounded-full relative overflow-hidden group/btn',
                  plan.popular ? 'bg-emerald-600 hover:bg-emerald-700 text-white' : 'bg-emerald-100 hover:bg-emerald-200 text-emerald-700'
                ]" @click="router.push('/auth/register')">
                  <span class="relative z-10">免費試用</span>
                  <div
                    class="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 translate-x-[-200%] group-hover/btn:translate-x-[200%] transition-transform duration-1000">
                  </div>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Testimonials Section -->
    <section ref="testimonialsSection" class="py-32 relative overflow-hidden bg-white">
      <div class="absolute inset-0">
        <div
          class="absolute inset-0 bg-[linear-gradient(to_right,rgba(16,185,129,0.03)_1px,transparent_1px),linear-gradient(to_bottom,rgba(16,185,129,0.03)_1px,transparent_1px)] bg-[size:4rem_4rem]">
        </div>
        <div class="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(16,185,129,0.03),transparent_50%)]">
        </div>
      </div>

      <div class="container mx-auto px-6 relative">
        <div class="text-center max-w-3xl mx-auto mb-20">
          <div
            class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-emerald-50 border border-emerald-200 mb-6">
            <span class="text-xs font-medium text-emerald-600">客戶見證</span>
            <span class="w-1 h-1 rounded-full bg-emerald-600"></span>
            <span class="text-xs font-medium text-gray-600">真實體驗</span>
          </div>
          <h2 class="text-4xl font-bold mb-6 text-gray-900">客戶的使用心得</h2>
          <p class="text-xl text-gray-600">
            聽聽其他企業如何運用 HorizAI 提升專案管理效率
          </p>
        </div>

        <div class="grid md:grid-cols-2 gap-8">
          <div v-for="testimonial in testimonials" :key="testimonial.name"
            class="relative overflow-hidden group rounded-3xl bg-white shadow-lg border border-emerald-100 hover:border-emerald-200 transition-all duration-500">
            <div class="relative p-8">
              <div class="flex items-center gap-4 mb-6">
                <div class="relative">
                  <div class="absolute inset-0 bg-emerald-400/20 rounded-full blur-xl"></div>
                  <img :src="testimonial.image" :alt="testimonial.name"
                    class="w-16 h-16 rounded-full object-cover ring-2 ring-emerald-200 relative" />
                </div>
                <div>
                  <h4 class="font-semibold text-lg text-gray-900">{{ testimonial.name }}</h4>
                  <p class="text-sm text-gray-600">
                    {{ testimonial.role }} · {{ testimonial.company }}
                  </p>
                </div>
              </div>
              <div class="relative">
                <div class="absolute -top-4 -left-4 text-4xl text-emerald-200">"</div>
                <p class="text-lg leading-relaxed pl-4 text-gray-600">{{ testimonial.content }}</p>
                <div class="absolute -bottom-4 -right-4 text-4xl text-emerald-200 rotate-180">"</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="py-32 relative overflow-hidden">
      <div class="absolute inset-0">
        <div class="absolute inset-0 bg-gradient-to-br from-emerald-600 via-emerald-500 to-emerald-400"></div>
        <div
          class="absolute inset-0 bg-[linear-gradient(to_right,rgba(255,255,255,0.05)_1px,transparent_1px),linear-gradient(to_bottom,rgba(255,255,255,0.05)_1px,transparent_1px)] bg-[size:4rem_4rem]">
        </div>
        <img src="https://images.unsplash.com/photo-1554224155-8d04cb21cd6c?auto=format&fit=crop&q=80"
          alt="CTA Background" class="absolute inset-0 w-full h-full object-cover mix-blend-overlay opacity-20" />
      </div>

      <div class="container mx-auto px-6 relative">
        <div class="max-w-3xl mx-auto text-center text-white">
          <div class="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-white/10 border border-white/20 mb-8">
            <span class="text-xs font-medium">立即開始</span>
            <span class="w-1 h-1 rounded-full bg-white"></span>
            <span class="text-xs font-medium opacity-80">免費試用</span>
          </div>
          <h2 class="text-4xl font-bold mb-6">準備好開始了嗎？</h2>
          <p class="text-xl mb-8 opacity-90">
            立即註冊，體驗智能專案管理系統的強大功能
          </p>
          <div class="flex flex-col sm:flex-row gap-4 justify-center">
            <button size="lg" variant="secondary"
              class="rounded-full px-8 py-6 text-base font-medium relative overflow-hidden group bg-white text-emerald-600 hover:bg-white/90"
              @click="router.push('/auth/register')">
              <span class="relative z-10">開始 14 天免費試用</span>
              <div
                class="absolute inset-0 bg-gradient-to-r from-emerald-500/0 via-emerald-500/10 to-emerald-500/0 translate-x-[-200%] group-hover:translate-x-[200%] transition-transform duration-1000">
              </div>
            </button>
          </div>
        </div>
      </div>
    </section>
  </div>
</template>

<style scoped>
@keyframes float {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-20px);
  }
}

@keyframes float-delay {

  0%,
  100% {
    transform: translateY(0px);
  }

  50% {
    transform: translateY(-15px);
  }
}

.animate-float {
  animation: float 8s ease-in-out infinite;
}

.animate-float-delay {
  animation: float-delay 6s ease-in-out infinite;
  animation-delay: -3s;
}

@keyframes slow-zoom {

  0%,
  100% {
    transform: scale(1.05);
  }

  50% {
    transform: scale(1.1);
  }
}

.animate-slow-zoom {
  animation: slow-zoom 15s ease-in-out infinite;
}

@keyframes pulse-slow {

  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 0.6;
  }
}

.animate-pulse-slow {
  animation: pulse-slow 4s ease-in-out infinite;
}

@keyframes fade-in-up {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  opacity: 0;
  animation: fade-in-up 1s ease-out forwards;
  animation-delay: var(--delay, 0s);
}
</style>


