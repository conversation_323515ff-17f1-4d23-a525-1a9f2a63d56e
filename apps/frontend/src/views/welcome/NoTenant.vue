<script setup lang="ts">
import { useAuth, useAuthStore } from '@horizai/auth';
import { useRouter } from 'vue-router';
import { useNotification } from '@/composables/shared/useNotification';
import { handleLogoutRedirect } from '@/utils/redirect.utils';
import { computed } from 'vue';
import { Button } from '@/components/ui/button';
import { Building, User, LogOut } from 'lucide-vue-next';

const { logout } = useAuth();
const authStore = useAuthStore();
const router = useRouter();
const notification = useNotification();

// 處理公司設定
function handleCompanySetup() {
  router.push('/auth/company-setup');
}

// 處理個人模式
function handlePersonalMode() {
  router.push('/welcome/personal');
}

async function handleLogout() {
  try {
    // 執行登出邏輯
    await logout();
    notification.toast.success('已成功登出');
  } catch (error) {
    console.error('登出過程中發生錯誤:', error);
    notification.toast.warning('登出過程中發生錯誤，但已清除本地狀態');
  } finally {
    // 無論登出是否成功，都要執行重導向
    try {
      const target = handleLogoutRedirect();
      await router.push(target);
    } catch (routerError) {
      console.error('重導向失敗，使用強制跳轉:', routerError);
      window.location.href = '/auth/login';
    }
  }
}

// 使用者資訊
const userEmail = computed(() => authStore.currentUser?.email || '');
const userName = computed(() => authStore.currentUser?.name || '');
</script>

<template>
  <div class="flex min-h-screen flex-col items-center justify-center bg-background">
    <div class="mx-auto flex w-full flex-col justify-center space-y-6 sm:w-[400px]">
      <div class="flex flex-col space-y-2 text-center">
        <h1 class="text-2xl font-semibold tracking-tight">歡迎使用 HorizAI</h1>
        <p class="text-sm text-muted-foreground">請設定您的公司資訊以開始使用完整功能</p>
      </div>

      <div class="space-y-3">
        <Button @click="handleCompanySetup" class="w-full">
          <Building class="w-4 h-4 mr-2" />
          設定公司資訊
        </Button>

        <Button @click="handlePersonalMode" variant="outline" class="w-full">
          <User class="w-4 h-4 mr-2" />
          以個人模式繼續
        </Button>

        <Button @click="handleLogout" variant="ghost" class="w-full">
          <LogOut class="w-4 h-4 mr-2" />
          登出
        </Button>
      </div>
    </div>
  </div>
</template>
