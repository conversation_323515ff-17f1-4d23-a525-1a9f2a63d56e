<template>
  <div class="flex min-h-screen">
    <!-- 左側背景區域 -->
    <div class="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-[#0F766E] to-[#0D6D66] relative overflow-hidden">
      <!-- 裝飾性圓形 -->
      <div class="absolute top-0 right-0 w-[500px] h-[500px] bg-white/5 rounded-full translate-x-1/3 -translate-y-1/3">
      </div>
      <div
        class="absolute bottom-0 left-0 w-[500px] h-[500px] bg-white/5 rounded-full -translate-x-1/3 translate-y-1/3">
      </div>

      <!-- 動態背景效果 -->
      <div class="absolute inset-0">
        <div class="firefly"></div>
        <div class="firefly"></div>
        <div class="firefly"></div>
        <div class="firefly"></div>
        <div class="firefly"></div>
        <div class="firefly"></div>
      </div>

      <!-- 網格背景 -->
      <div class="absolute inset-0 bg-grid opacity-10"></div>

      <!-- 內容區域 -->
      <div class="relative z-10 flex flex-col justify-center px-16">
        <div class="max-w-md">
          <h2 class="text-4xl font-bold tracking-tight mb-4 text-white">歡迎回到 HorizAI</h2>
          <p class="text-lg text-white/90 leading-relaxed">
            室內設計與裝修工程管理平台，讓您的專案管理更輕鬆、更高效。
          </p>
          <div class="mt-8 space-y-6">
            <div v-for="(feature, index) in features" :key="index" class="flex items-center space-x-4 feature-card">
              <div
                class="flex-shrink-0 w-12 h-12 rounded-full bg-white/10 backdrop-blur-sm flex items-center justify-center">
                <component :is="feature.icon" class="w-6 h-6 text-white" />
              </div>
              <div class="backdrop-blur-sm bg-white/5 rounded-lg p-4 flex-1">
                <h3 class="text-lg font-medium text-white">{{ feature.title }}</h3>
                <p class="text-white/70">{{ feature.description }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 右側登入表單 -->
    <div class="flex-1 flex items-center justify-center p-8 lg:p-12 bg-background">
      <div class="w-full max-w-md space-y-8">
        <div class="text-center lg:text-left">
          <h1 class="text-3xl font-bold tracking-tight">登入帳號</h1>
          <p class="mt-2 text-base text-muted-foreground">歡迎回來，請登入您的帳號</p>
        </div>

        <form class="mt-8 space-y-6" @submit.prevent="handleSubmit">
          <div class="space-y-4">
            <!-- 電子郵件 -->
            <div class="space-y-2">
              <Label for="email">電子郵件</Label>
              <Input id="email" type="email" v-model="email" required placeholder="請輸入您的電子郵件" />
            </div>

            <!-- 密碼 -->
            <div class="space-y-2">
              <div class="flex items-center justify-between">
                <Label for="password">密碼</Label>
                <Button variant="link" class="px-0 h-auto text-sm" @click="router.push('/auth/forgot-password')">
                  忘記密碼？
                </Button>
              </div>
              <Input id="password" type="password" v-model="password" required placeholder="請輸入密碼"
                @keydown.enter="handleSubmit" />
            </div>

            <!-- 記住我 -->
            <div class="flex items-center space-x-2">
              <Checkbox id="rememberMe" v-model="rememberMe" />
              <Label for="rememberMe" class="text-sm">記住我</Label>
            </div>
          </div>

          <!-- 登入按鈕 -->
          <Button type="submit" class="w-full" :loading="isLoading" :disabled="isLoading">
            {{ isLoading ? '登入中...' : '登入' }}
          </Button>
        </form>

        <div class="text-center">
          <p class="text-muted-foreground">
            還沒有帳號？
            <Button variant="link" class="px-0" @click="router.push('/auth/register')">
              註冊
            </Button>
          </p>
        </div>

        <div class="text-center mt-4">
          <p class="text-sm text-muted-foreground">或使用以下帳號登入</p>
          <div class="mt-2 space-y-4">
            <Button variant="outline" class="w-full py-3 flex items-center justify-center space-x-2"
              @click="handleGoogleLogin">
              <Icon icon="simple-icons:google" class="h-5 w-5" />
              <span>Google 登入</span>
            </Button>
            <Button variant="outline" class="w-full py-3 flex items-center justify-center space-x-2"
              @click="handleLineLogin">
              <Icon icon="simple-icons:line" class="h-5 w-5" />
              <span>LINE 登入</span>
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { useNotification } from '@/composables/shared/useNotification'
import { CheckCircle2, ShieldCheck, Zap } from 'lucide-vue-next'
import { Icon } from '@iconify/vue'
import { useAuth, useAbility, useExternalLogin } from '@horizai/auth'
import type { AuthResponse, LoginDto } from '@/types/models/auth.model'
import type { Action, Subject, Permission } from '@horizai/auth'
import { authConfig } from '@/config/auth.config'
import { USER_ROLES } from '@/types/models/user.model'
import { getRedirectTarget, getDefaultRedirectPath } from '@/utils/redirect.utils'

const router = useRouter()
const route = useRoute()
const notification = useNotification()
const auth = useAuth()
const { ability, update } = useAbility()
const { loginWithGoogle, loginWithLine } = useExternalLogin()

const email = ref('')
const password = ref('')
const rememberMe = ref(false)
const isLoading = ref(false)

const features = [
  {
    title: '智能專案管理',
    description: '自動化工作流程，提高效率',
    icon: CheckCircle2
  },
  {
    title: '安全可靠',
    description: '企業級安全防護',
    icon: ShieldCheck
  },
  {
    title: '高效協作',
    description: '團隊即時溝通與協作',
    icon: Zap
  }
]

const handleGoogleLogin = async () => {
  try {
    await loginWithGoogle(window.location.href, '')
  } catch (error: any) {
    console.error('Google 登入失敗:', error)
    const errorMessage = extractErrorMessage(error)
    notification.flash.error(errorMessage, {
      title: 'Google 登入失敗',
      duration: 0 // 持續顯示，直到用戶手動關閉
    })
  }
}

const handleLineLogin = async () => {
  try {
    await loginWithLine(window.location.href, '')
  } catch (error: any) {
    console.error('LINE 登入失敗:', error)
    const errorMessage = extractErrorMessage(error)
    notification.flash.error(errorMessage, {
      title: 'LINE 登入失敗',
      duration: 0 // 持續顯示，直到用戶手動關閉
    })
  }
}

/**
 * 從錯誤對象中提取錯誤訊息
 * @param error 錯誤對象
 * @returns 錯誤訊息字串
 */
const extractErrorMessage = (error: any): string => {
  // 如果是後端 API 錯誤回應格式
  if (error?.response?.data) {
    const errorData = error.response.data

    // 優先使用後端的 message 欄位
    if (errorData.message) {
      return errorData.message
    }

    // 如果有 errors 陣列且不為空，使用第一個錯誤
    if (errorData.errors && Array.isArray(errorData.errors) && errorData.errors.length > 0) {
      return errorData.errors[0]
    }
  }

  // 如果錯誤對象直接有 message 屬性
  if (error?.message) {
    return error.message
  }

  // 預設錯誤訊息
  return '登入失敗，請檢查您的帳號密碼'
}

const handleSubmit = async (e: Event) => {
  e.preventDefault()
  isLoading.value = true

  try {
    const loginData: LoginDto = {
      email: email.value,
      password: password.value,
      remember_me: rememberMe.value
    }

    const response = await auth.login(loginData)

    if (!response || !response.user) {
      throw new Error('登入回應格式不正確或缺少使用者資訊')
    }

    notification.toast.success(
      '登入成功',
      `歡迎回來, ${response.user.name || response.user.email}`
    )

    const redirectPath = getRedirectTarget(
      route.query.redirect as string,
      getDefaultRedirectPath(response.user)
    )
    router.push(redirectPath)
  } catch (error: any) {
    console.error('登入失敗:', error)
    const errorMessage = extractErrorMessage(error)
    notification.flash.error(errorMessage, {
      title: '登入失敗',
      duration: 0 // 持續顯示
    })
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.bg-grid {
  background-size: 50px 50px;
  background-image:
    linear-gradient(to right, rgba(255, 255, 255, 0.05) 1px, transparent 1px),
    linear-gradient(to bottom, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
}

.feature-card {
  transition: all 0.3s ease-in-out;
}

.feature-card:hover {
  transform: translateX(10px);
}

.backdrop-blur-sm {
  transition: all 0.3s ease-in-out;
}

.backdrop-blur-sm:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

/* 螢火蟲動畫效果 */
.firefly {
  position: absolute;
  width: 3px;
  height: 3px;
  background-color: rgba(255, 255, 255, 0.5);
  border-radius: 50%;
  animation: firefly 4s ease-in-out infinite;
}

.firefly:nth-child(1) {
  left: 10%;
  animation-delay: 0s;
}

.firefly:nth-child(2) {
  left: 30%;
  animation-delay: -1s;
}

.firefly:nth-child(3) {
  left: 50%;
  animation-delay: -2s;
}

.firefly:nth-child(4) {
  left: 70%;
  animation-delay: -3s;
}

.firefly:nth-child(5) {
  left: 90%;
  animation-delay: -4s;
}

.firefly:nth-child(6) {
  left: 20%;
  animation-delay: -5s;
}

@keyframes firefly {

  0%,
  100% {
    transform: translateY(0) scale(1);
    opacity: 0.2;
  }

  50% {
    transform: translateY(-100px) scale(1.2);
    opacity: 0.8;
  }
}
</style>
