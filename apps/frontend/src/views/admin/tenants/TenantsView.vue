<script setup lang="ts">
import { computed, onMounted, watch, ref } from "vue";
import { useAuth } from "@horizai/auth";
import { useTenants } from "@/composables/admin/useTenants";
import { useTenantPermissions } from "@/composables/admin/useTenantPermissions";
import type { ITenant } from "@/types/models/tenant.model";
import { useAdminPlanStore } from "@/stores/admin/plan.store";
import { USER_ROLES } from "@/constants/user.constants";
import { useNotification } from "@/composables/shared/useNotification";
import { useRouter } from "vue-router";
import { Actions, Subjects } from "@horizai/permissions";

// UI 元件
import { Card, CardContent } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// 模態視窗元件
import ModalTenantForm from "@/components/admin/tenant/ModalTenantForm.vue";
import ModalTenantAdmin from "@/components/admin/tenant/ModalTenantAdmin.vue";
import ModalTenantOrders from "@/components/admin/tenant/ModalTenantOrders.vue";

// 圖示
import {
  Plus,
  Search,
  Building2,
  Trash2,
  Users,
  Edit,
  ArrowUpDown,
  Ban,
  Check,
} from "lucide-vue-next";
import ExportDropdown from "@/components/admin/common/ExportDropdown.vue";

// 權限與狀態管理
const auth = useAuth();
const {
  tenantPermissions,
  canManage,
  canRead,
  isSuperAdmin,
  userRole,
  isLoaded,
  checkPermission,
} = useTenantPermissions();
const planStore = useAdminPlanStore();
const notification = useNotification();
const router = useRouter();

const {
  // 狀態
  isLoading,
  tenants,
  searchQuery,
  filterStatus,
  filterPlan,
  selectedTenants,
  expandedRows,
  currentPage,
  pageSize,
  showCreateModal,
  showEditModal,
  editingTenant,
  statistics,
  isPermissionLoaded,

  // 計算屬性
  filteredTenants,
  paginatedTenants,
  totalPages,
  allSelected,
  someSelected,

  // 方法
  fetchTenants,
  fetchTenantsWithPermissionCheck,
  handleTenantCreated,
  handleEditTenant,
  handleTenantUpdated,
  handleDeleteTenant,
  handleActivateTenant,
  handleDeactivateTenant,
  handleBatchAction,
  toggleSelectAll,
  handleSelectTenant,
  toggleRow,
  exportTenants,

  // 格式化方法
  formatNumber,
  formatDate,
  getPlanLabel,
  getPlanVariant,
} = useTenants();

// 權限檢查
const hasAccess = computed(() => {
  if (!isLoaded.value) return false;
  return tenantPermissions.value.canView;
});

// 清晰的權限計算屬性
const canCreateTenant = computed(() => checkPermission("create"));

// 狀態顯示方法
const getStatusDisplay = (status: string) => {
  switch (status) {
    case "active":
      return { text: "啟用", variant: "default" };
    case "inactive":
      return { text: "停用", variant: "secondary" };
    default:
      return { text: status, variant: "secondary" };
  }
};

// 格式化匯出資料
const formatExportData = () => {
  return (
    filteredTenants.value.length ? filteredTenants.value : tenants.value
  ).map((t) => ({
    ID: t.id,
    名稱: t.name,
    網域: t.domain,
    狀態: getStatusDisplay(t.status).text,
  }));
};
const canEditTenant = computed(() => checkPermission("update"));
const canDeleteTenant = computed(() => checkPermission("delete"));
const canManageUsers = computed(() =>
  checkPermission(Actions.MANAGE, Subjects.TENANT_USER)
);

// 在權限載入完成後載入資料
onMounted(async () => {
  // 使用封裝的權限檢查與載入方法
  const loaded = await fetchTenantsWithPermissionCheck();
  if (loaded) {
    planStore.fetchPlans();
  }
});

// 模態視窗狀態
const tenantFormModal = ref<{
  show: boolean;
  isEditing: boolean;
  tenantData: ITenant | null;
}>({
  show: false,
  isEditing: false,
  tenantData: null,
});

// 監聽模態視窗相關狀態
watch(
  () => [showCreateModal.value, showEditModal.value, editingTenant.value],
  (newValues) => {
    const [newShowCreate, newShowEdit, newEditingTenant] = newValues as [
      boolean,
      boolean,
      ITenant | null,
    ];
    tenantFormModal.value.show = newShowCreate || newShowEdit;
    tenantFormModal.value.isEditing = newShowEdit;
    tenantFormModal.value.tenantData = newEditingTenant;
  }
);

// 監聽模態框關閉狀態
watch(
  () => tenantFormModal.value.show,
  (newShow) => {
    if (!newShow) {
      showCreateModal.value = false;
      showEditModal.value = false;
    }
  }
);

const tenantAdminModal = ref<{
  show: boolean;
  tenantId: string | null;
  tenantName: string;
}>({
  show: false,
  tenantId: null,
  tenantName: "",
});

// 刪除確認對話框狀態
const deleteDialog = ref<{
  show: boolean;
  tenantId: string | null;
  tenantName: string;
}>({
  show: false,
  tenantId: null,
  tenantName: "",
});

// 處理刪除確認
const handleDeleteConfirm = async () => {
  try {
    if (deleteDialog.value.tenantId) {
      await handleDeleteTenant(deleteDialog.value.tenantId);
      deleteDialog.value.show = false;
      notification.toast.success("租戶已成功刪除");
    }
  } catch (err) {
    notification.flash.error("系統錯誤：無法刪除租戶，請稍後再試");
  }
};

// 權限相關錯誤處理使用 flash
const handleApiError = (error: any) => {
  if (error?.response?.status === 401) {
    notification.flash.error("請重新登入，您的登入狀態已失效");
    router.push("/auth/login");
  } else if (error?.response?.status === 403) {
    notification.flash.error("權限不足，您沒有權限執行此操作");
    router.push("/");
  } else {
    notification.flash.error("系統錯誤：" + (error.message || "發生未知錯誤"));
  }
};

// 取得方案列表
const plans = computed(() => planStore.plans);

// 處理全選事件
const handleSelectAllChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target) {
    toggleSelectAll();
  }
};

// 處理單個選擇事件
const handleSelectChange = (id: string, e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target) {
    handleSelectTenant(id, target.checked);
  }
};

const orderModal = ref({ show: false, tenantName: "" });
</script>

<template>
  <div
    v-if="!hasAccess"
    class="flex items-center justify-center h-[calc(100vh-200px)]"
  >
    <div class="text-center space-y-4">
      <Ban class="w-12 h-12 text-destructive mx-auto" />
      <h2 class="text-2xl font-semibold">無權限訪問</h2>
      <p class="text-muted-foreground">您沒有權限訪問租戶管理頁面</p>
      <pre
        class="mt-4 text-left text-sm bg-muted p-4 rounded-md overflow-auto max-w-xl mx-auto"
      >
使用者角色: {{ userRole || "未載入" }}
已認證: {{ auth.isAuthenticated.value }}
已載入: {{ isLoaded }}
權限檢查結果:
方法1 (tenantPermissions): {{ tenantPermissions.canView }}
方法2 (直接角色檢查): {{
          userRole &&
          [
            USER_ROLES.SUPER_ADMIN,
            USER_ROLES.SYSTEM_ADMIN,
            USER_ROLES.TENANT_ADMIN,
          ].some((r) => r === userRole)
        }}
方法3 (CASL): {{ canRead || canManage || isSuperAdmin }}
CASL詳情:
- canRead: {{ canRead }}
- canManage: {{ canManage }}
- isSuperAdmin: {{ isSuperAdmin }}
      </pre>

      <div>
        <Button @click="fetchTenants" variant="outline" class="mr-2"
          >重新載入</Button
        >
        <Button @click="fetchTenants" variant="default">重試</Button>
      </div>
    </div>
  </div>

  <div v-else class="p-6 space-y-6">
    <!-- 頁面標題 -->
    <div>
      <h1 class="text-2xl font-semibold text-zinc-900 dark:text-white">
        租戶管理
      </h1>
      <p class="text-muted-foreground">管理系統中的所有租戶及其設定</p>
    </div>

    <!-- 主要內容 -->
    <template v-if="!isLoading">
      <!-- 操作列 -->
      <Card>
        <CardContent class="p-4">
          <div
            class="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-4 sm:items-center sm:justify-between"
          >
            <!-- 搜尋與篩選 -->
            <div class="flex flex-wrap items-center gap-2 flex-1">
              <div class="relative w-full sm:w-auto sm:min-w-[300px]">
                <Search
                  class="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground"
                />
                <Input
                  v-model="searchQuery"
                  placeholder="搜尋租戶..."
                  class="pl-8 w-full"
                />
              </div>

              <Select v-model="filterStatus">
                <SelectTrigger class="w-28">
                  <SelectValue placeholder="狀態" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有狀態</SelectItem>
                  <SelectItem value="active">啟用</SelectItem>
                  <SelectItem value="inactive">停用</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <!-- 操作按鈕 -->
            <div class="flex space-x-2">
              <Button
                v-if="canCreateTenant"
                variant="default"
                @click="showCreateModal = true"
              >
                <Plus class="h-4 w-4 mr-2" />
                新增租戶
              </Button>
              <ExportDropdown
                :data="
                  (filteredTenants.length ? filteredTenants : tenants).map(
                    (t) => ({
                      ID: t.id,
                      名稱: t.name,
                      網域: t.domain,
                      狀態: getStatusDisplay(t.status).text,
                    })
                  )
                "
                filename="tenants"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- 租戶列表 -->
      <div
        v-if="tenants.length === 0"
        class="text-center py-16 border rounded-md"
      >
        <div class="space-y-3">
          <Building2 class="w-12 h-12 text-muted-foreground mx-auto" />
          <h3 class="text-lg font-medium">尚無租戶</h3>
          <p class="text-muted-foreground">系統中尚未建立任何租戶</p>
          <Button
            v-if="tenantPermissions.canCreate"
            @click="showCreateModal = true"
            class="mt-4"
          >
            <Plus class="h-4 w-4 mr-2" />
            建立第一個租戶
          </Button>
        </div>
      </div>

      <div
        v-else-if="filteredTenants.length === 0"
        class="text-center py-16 border rounded-md"
      >
        <div class="space-y-3">
          <Search class="w-12 h-12 text-muted-foreground mx-auto" />
          <h3 class="text-lg font-medium">找不到符合條件的租戶</h3>
          <p class="text-muted-foreground">請嘗試其他搜尋或篩選條件</p>
          <Button
            @click="
              searchQuery = '';
              filterStatus = 'all';
            "
            variant="outline"
            class="mt-4"
          >
            清除篩選
          </Button>
        </div>
      </div>

      <div v-else>
        <!-- 批次操作選單 -->
        <div
          v-if="selectedTenants.length > 0"
          class="mb-4 flex items-center space-x-2"
        >
          <span class="text-sm font-medium"
            >已選取 {{ selectedTenants.length }} 項</span
          >
          <Button
            v-if="canManageUsers"
            size="sm"
            variant="outline"
            @click="handleBatchAction('activate')"
          >
            <Check class="h-4 w-4 mr-2" />
            批次啟用
          </Button>
          <Button
            v-if="canManageUsers"
            size="sm"
            variant="outline"
            @click="handleBatchAction('deactivate')"
          >
            <Ban class="h-4 w-4 mr-2" />
            批次停用
          </Button>
        </div>

        <!-- 表格 -->
        <div class="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead class="w-12">
                  <Checkbox
                    :checked="allSelected"
                    :indeterminate="someSelected"
                    @change="handleSelectAllChange"
                  />
                </TableHead>
                <TableHead>租戶名稱</TableHead>
                <TableHead>狀態</TableHead>
                <TableHead>使用者數</TableHead>
                <TableHead>建立時間</TableHead>
                <TableHead class="w-[120px] text-right">操作</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              <template v-for="tenant in paginatedTenants" :key="tenant.id">
                <TableRow
                  class="cursor-pointer hover:bg-muted/50 transition-colors"
                  @click="toggleRow(tenant.id)"
                >
                  <TableCell class="w-12" @click.stop>
                    <Checkbox
                      :checked="selectedTenants.includes(tenant.id)"
                      @change="(e: Event) => handleSelectChange(tenant.id, e)"
                    />
                  </TableCell>
                  <TableCell>
                    <div class="font-medium">{{ tenant.name }}</div>
                    <div class="text-sm text-muted-foreground">
                      {{ tenant.domain }}
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge
                      :variant="
                        tenant.status === 'active' ? 'default' : 'secondary'
                      "
                    >
                      {{ tenant.status === "active" ? "啟用" : "停用" }}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    <div class="flex items-center gap-2">
                      <Users class="h-4 w-4 text-muted-foreground" />
                      {{ formatNumber(tenant.usersCount) }}
                    </div>
                  </TableCell>
                  <TableCell>{{
                    tenant.createdAt
                      ? new Date(tenant.createdAt).toLocaleDateString("zh-TW")
                      : "-"
                  }}</TableCell>
                  <TableCell class="w-[120px] text-right" @click.stop>
                    <div class="flex items-center justify-end gap-2">
                      <TooltipProvider>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              variant="ghost"
                              size="icon"
                              @click="
                                orderModal.show = true;
                                orderModal.tenantName = tenant.name;
                              "
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                class="h-4 w-4"
                                fill="none"
                                viewBox="0 0 24 24"
                                stroke="currentColor"
                              >
                                <path
                                  stroke-linecap="round"
                                  stroke-linejoin="round"
                                  stroke-width="2"
                                  d="M9 17v-2a4 4 0 018 0v2m-4-4V7a4 4 0 10-8 0v6m4 4v2a4 4 0 01-8 0v-2"
                                />
                              </svg>
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>查看訂單</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              v-if="canManageUsers"
                              variant="ghost"
                              size="icon"
                              @click="
                                tenantAdminModal.show = true;
                                tenantAdminModal.tenantId = tenant.id;
                                tenantAdminModal.tenantName = tenant.name;
                              "
                            >
                              <Users class="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>管理使用者</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              v-if="canEditTenant"
                              variant="ghost"
                              size="icon"
                              @click="
                                handleEditTenant(tenant);
                                tenantFormModal.show = true;
                              "
                            >
                              <Edit class="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>編輯租戶</p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              v-if="canEditTenant"
                              variant="ghost"
                              size="icon"
                              @click="
                                tenant.status === 'active'
                                  ? handleDeactivateTenant(tenant.id)
                                  : handleActivateTenant(tenant.id)
                              "
                            >
                              <component
                                :is="tenant.status === 'active' ? Ban : Check"
                                class="h-4 w-4"
                              />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>
                              {{
                                tenant.status === "active"
                                  ? "停用租戶"
                                  : "啟用租戶"
                              }}
                            </p>
                          </TooltipContent>
                        </Tooltip>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              v-if="canDeleteTenant"
                              variant="ghost"
                              size="icon"
                              class="text-destructive hover:text-destructive"
                              @click="
                                deleteDialog.show = true;
                                deleteDialog.tenantId = tenant.id;
                                deleteDialog.tenantName = tenant.name;
                              "
                            >
                              <Trash2 class="h-4 w-4" />
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>刪除租戶</p>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    </div>
                  </TableCell>
                </TableRow>

                <!-- 展開的詳細資訊 -->
                <TableRow
                  v-if="expandedRows.includes(tenant.id)"
                  class="bg-muted/30"
                >
                  <TableCell colspan="6" class="p-0">
                    <div class="p-4 space-y-4 border-t">
                      <h3 class="text-lg font-medium">
                        {{ tenant.name }} 詳細資訊
                      </h3>

                      <div
                        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4"
                      >
                        <!-- 基本資訊 -->
                        <div class="space-y-2">
                          <h4 class="text-sm font-medium text-muted-foreground">
                            基本資訊
                          </h4>
                          <div class="space-y-1">
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >租戶 ID:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.id
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >網域:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.domain || "未設定"
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >建立日期:</span
                              >
                              <span class="text-sm font-medium">{{
                                formatDate(tenant.createdAt)
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >最後更新:</span
                              >
                              <span class="text-sm font-medium">{{
                                formatDate(tenant.updatedAt)
                              }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- 管理員資訊 -->
                        <div class="space-y-2">
                          <h4 class="text-sm font-medium text-muted-foreground">
                            管理員資訊
                          </h4>
                          <div class="space-y-1">
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >管理員姓名:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.adminName || "未設定"
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >管理員信箱:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.adminEmail || "未設定"
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >公司規模:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.companySize || "未設定"
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >產業:</span
                              >
                              <span class="text-sm font-medium">{{
                                tenant.industry || "未設定"
                              }}</span>
                            </div>
                          </div>
                        </div>

                        <!-- 方案與使用情況 -->
                        <div class="space-y-2">
                          <h4 class="text-sm font-medium text-muted-foreground">
                            使用情況
                          </h4>
                          <div class="space-y-1">
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >使用者數量:</span
                              >
                              <span class="text-sm font-medium">{{
                                formatNumber(tenant.usersCount)
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >專案數量:</span
                              >
                              <span class="text-sm font-medium">{{
                                formatNumber(tenant.projectsCount)
                              }}</span>
                            </div>
                            <div class="flex justify-between">
                              <span class="text-sm text-muted-foreground"
                                >狀態:</span
                              >
                              <Badge
                                :variant="
                                  tenant.status === 'active'
                                    ? 'default'
                                    : 'secondary'
                                "
                              >
                                {{
                                  tenant.status === "active" ? "啟用" : "停用"
                                }}
                              </Badge>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- 無操作按鈕 -->
                    </div>
                  </TableCell>
                </TableRow>
              </template>
            </TableBody>
          </Table>
        </div>

        <!-- 分頁資訊 -->
        <div class="mt-4 flex items-center justify-between">
          <div class="text-sm text-muted-foreground">
            顯示 {{ (currentPage - 1) * pageSize + 1 }} -
            {{
              Math.min(currentPage * pageSize, filteredTenants.length)
            }}
            筆，共 {{ filteredTenants.length }} 筆
          </div>

          <div v-if="totalPages > 1" class="flex justify-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              :disabled="currentPage === 1"
              @click="currentPage > 1 && currentPage--"
            >
              上一頁
            </Button>

            <span class="px-3 py-1 border rounded"
              >{{ currentPage }} / {{ totalPages }}</span
            >

            <Button
              variant="outline"
              size="sm"
              :disabled="currentPage === totalPages"
              @click="currentPage < totalPages && currentPage++"
            >
              下一頁
            </Button>
          </div>
        </div>
      </div>
    </template>

    <!-- 模態視窗 -->
    <ModalTenantForm
      v-model:show="tenantFormModal.show"
      :isEditing="tenantFormModal.isEditing"
      :tenant-data="tenantFormModal.tenantData"
      @submitted="fetchTenants"
    />

    <ModalTenantAdmin
      v-model:show="tenantAdminModal.show"
      :tenant-id="tenantAdminModal.tenantId"
      :tenant-name="tenantAdminModal.tenantName"
    />

    <ModalTenantOrders
      :show="orderModal.show"
      :tenant-name="orderModal.tenantName"
      @close="orderModal.show = false"
    />

    <!-- 刪除確認對話框 -->
    <AlertDialog
      :open="deleteDialog.show"
      @update:open="(value) => (deleteDialog.show = value)"
    >
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>刪除租戶確認</AlertDialogTitle>
          <AlertDialogDescription>
            您確定要刪除租戶 "{{ deleteDialog.tenantName }}"
            嗎？此操作將不可逆。
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel>取消</AlertDialogCancel>
          <AlertDialogAction
            class="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            @click="handleDeleteConfirm"
          >
            確認刪除
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  </div>
</template>
