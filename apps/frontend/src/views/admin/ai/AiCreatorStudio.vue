<template>
  <div class="flex h-screen bg-gray-50 overflow-hidden relative">
    <!-- 行動端背景遮罩 -->
    <div
      v-if="showLeftSidebar || showRightSidebar"
      @click="showLeftSidebar = false; showRightSidebar = false"
      class="fixed inset-0 bg-black bg-opacity-50 z-5 lg:hidden"
    />

    <!-- Mobile Menu Toggle -->
    <Button
      @click="toggleLeftSidebar"
      variant="outline"
      size="sm"
      class="absolute top-4 left-4 z-20 lg:hidden"
    >
      <Menu class="h-4 w-4" />
    </Button>
    <!-- Left Sidebar - Node Palette -->
    <div
      :class="[
        'bg-white border-r border-gray-200 flex flex-col h-full overflow-hidden transition-transform duration-300 ease-in-out',
        'w-80 lg:relative lg:translate-x-0',
        'absolute lg:static z-10',
        showLeftSidebar ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'
      ]"
    >
      <div class="p-4 border-b border-gray-200">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold text-gray-900">節點調色盤</h2>
            <p class="text-sm text-gray-500 mt-1">拖拽節點來建立您的工作流程</p>
          </div>
          <Button
            @click="toggleLeftSidebar"
            variant="ghost"
            size="sm"
            class="lg:hidden"
          >
            <X class="h-4 w-4" />
          </Button>
        </div>
      </div>

      <div class="flex-1 overflow-y-auto p-4 h-0">
        <NodePalette @drag-start="onDragStart" />
      </div>
    </div>

    <!-- Main Canvas Area -->
    <div class="flex-1 flex flex-col h-full overflow-hidden">
      <!-- Top Toolbar -->
      <div
        class="h-16 bg-white border-b border-gray-200 flex items-center justify-between px-4"
      >
        <!-- 工具欄 -->
        <div class="flex items-center mb-3 space-x-1">
          <Button
            v-if="currentWorkflow"
            @click="saveWorkflow"
            variant="outline"
            size="sm"
            :disabled="saving"
          >
            <Save class="h-3.5 w-3.5 mr-1" />
            {{ saving ? "儲存中..." : "儲存" }}
          </Button>
          <Button @click="openWorkflowManager" variant="outline" size="sm">
            <FolderOpen class="h-3.5 w-3.5 mr-1" />
            開啟
          </Button>
          <Button @click="createNewWorkflow" variant="outline" size="sm">
            <Plus class="h-3.5 w-3.5 mr-1" />
            新建
          </Button>
          <Button
            v-if="currentWorkflow && !currentWorkflow.is_published"
            @click="publishWorkflow"
            variant="outline"
            size="sm"
          >
            <Upload class="h-3.5 w-3.5 mr-1" />
            發布
          </Button>

          <!-- 分隔線 -->
          <div class="h-5 w-px bg-border mx-1"></div>

          <!-- 編輯操作按鈕 -->
          <Button
            @click="undo"
            variant="outline"
            size="sm"
            :disabled="historyIndex <= 0"
          >
            <Undo class="h-3.5 w-3.5" />
          </Button>
          <Button
            @click="redo"
            variant="outline"
            size="sm"
            :disabled="historyIndex >= history.length - 1"
          >
            <Redo class="h-3.5 w-3.5" />
          </Button>

          <!-- 分隔線 -->
          <div class="h-5 w-px bg-border mx-1"></div>

          <!-- 複製/粘貼按鈕 -->
          <Button @click="copySelectedNodes" variant="outline" size="sm">
            <Copy class="h-3.5 w-3.5" />
          </Button>
          <Button @click="cutSelectedNodes" variant="outline" size="sm">
            <Scissors class="h-3.5 w-3.5" />
          </Button>
          <Button
            @click="pasteNodes"
            variant="outline"
            size="sm"
            :disabled="clipboard.length === 0"
          >
            <Clipboard class="h-3.5 w-3.5" />
          </Button>
        </div>

        <div class="flex items-center space-x-2">
          <span class="text-sm text-gray-500">
            {{ currentWorkflow?.name || "未命名工作流程" }}
          </span>
        </div>

        <!-- 快速預覽按鈕 -->
        <Button
          v-if="currentWorkflow"
          @click="togglePreviewPanel"
          variant="outline"
          size="sm"
          class="ml-2"
        >
          <Eye class="h-3.5 w-3.5 mr-1" />
          {{ showPreviewPanel ? "關閉預覽" : "快速預覽" }}
        </Button>
      </div>

      <!-- Vue Flow Canvas -->
      <div class="flex-1 relative h-0">
        <!-- Debug info -->
        <div
          class="absolute top-2 right-2 z-10 bg-white p-2 rounded shadow text-xs"
        >
          <div>拖拽節點類型: {{ draggedNodeType || "無" }}</div>
          <div>當前工作流程: {{ currentWorkflow?.name || "無" }}</div>
          <div>總元素數量: {{ elements.length }}</div>
          <div>節點數量: {{ elements.filter((el) => !el.source).length }}</div>
          <div>邊數量: {{ elements.filter((el) => el.source).length }}</div>
          <div>選中節點: {{ selectedNode?.id || "無" }}</div>
          <div>選中邊: {{ selectedEdge?.id || "無" }}</div>
          <div class="text-gray-400 text-xs mt-1">按 Delete 刪除選中項目</div>
        </div>

        <VueFlow
          ref="vueFlowRef"
          v-model="elements"
          @dragover="onDragOver"
          @drop="onDrop"
          @node-drag-stop="onNodeDragStop"
          @connect="onConnect"
          @node-click="onNodeClick"
          @edge-click="onEdgeClick"
          @edge-context-menu="onEdgeContextMenu"
          @keydown="onKeyDown"
          class="vue-flow-basic-example"
          :default-zoom="0.8"
          :min-zoom="0.2"
          :max-zoom="2"
          :nodes-connectable="true"
          :auto-connect="false"
        >
          <Background pattern="dots" :gap="20" />
          <Controls />
          <MiniMap />

          <!-- Custom Node Templates -->
          <template #node-ai-bot="{ data, id }">
            <AiBotNode :id="id" :data="data" @config="openNodeConfig" />
          </template>

          <template #node-input="{ data, id }">
            <InputNode :id="id" :data="data" @config="openNodeConfig" />
          </template>

          <template #node-output="{ data, id }">
            <OutputNode :id="id" :data="data" @config="openNodeConfig" />
          </template>

          <template #node-condition="{ data, id }">
            <ConditionNode :id="id" :data="data" @config="openNodeConfig" />
          </template>
        </VueFlow>
      </div>
    </div>

    <!-- Right Sidebar - Property Inspector & Help -->
    <div
      class="w-80 bg-white border-l border-gray-200 flex flex-col h-full overflow-hidden"
    >
      <!-- Tab Headers -->
      <div class="border-b border-gray-200">
        <div class="flex">
          <button
            @click="activeTab = 'properties'"
            :class="[
              'flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'properties'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50',
            ]"
          >
            屬性
          </button>
          <button
            @click="activeTab = 'help'"
            :class="[
              'flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors',
              activeTab === 'help'
                ? 'border-blue-500 text-blue-600 bg-blue-50'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50',
            ]"
          >
            說明
          </button>
        </div>
      </div>

      <!-- Tab Content -->
      <div class="flex-1 overflow-y-auto h-0">
        <!-- Properties Tab -->
        <div v-show="activeTab === 'properties'" class="p-4">
          <PropertyInspector
            :selected-node="selectedNode"
            :selected-edge="selectedEdge"
            @updateNode="updateNodeData"
            @updateEdge="updateEdgeData"
            @deleteEdge="deleteEdge"
            @deleteNode="deleteNode"
          />
        </div>

        <!-- Help Tab -->
        <div v-show="activeTab === 'help'" class="p-4">
          <HelpPanel
            :selected-node="selectedNode"
            :selected-edge="selectedEdge"
          />
        </div>
      </div>
    </div>

    <!-- Workflow Manager Dialog -->
    <WorkflowManagerDialog
      v-model:open="showWorkflowManager"
      @load-workflow="loadWorkflow"
      @create-workflow="createWorkflow"
    />

    <!-- 預覽面板 -->
    <div
      v-if="showPreviewPanel"
      class="h-1/2 bg-white border-t border-gray-200 overflow-hidden flex flex-col"
    >
      <div
        class="p-3 border-b border-gray-200 bg-gray-50 flex justify-between items-center"
      >
        <h3 class="font-medium">工作流程預覽</h3>
        <div class="flex items-center space-x-2">
          <Button
            @click="executePreview"
            variant="default"
            size="sm"
            :disabled="previewLoading"
          >
            <Play class="h-3.5 w-3.5 mr-1" />
            {{ previewLoading ? "執行中..." : "執行" }}
          </Button>
          <Button @click="togglePreviewPanel" variant="outline" size="sm">
            <X class="h-3.5 w-3.5" />
          </Button>
        </div>
      </div>

      <div class="flex-1 flex overflow-hidden">
        <!-- 輸入面板 -->
        <div class="w-1/2 border-r border-gray-200 flex flex-col">
          <div class="p-2 bg-gray-50 border-b border-gray-200">
            <span class="text-sm font-medium">輸入</span>
          </div>
          <div class="flex-1 p-3 overflow-auto">
            <Textarea
              v-model="previewInput"
              placeholder="輸入 JSON 格式的測試資料..."
              class="h-full font-mono text-sm"
            />
          </div>
        </div>

        <!-- 輸出面板 -->
        <div class="w-1/2 flex flex-col">
          <div class="p-2 bg-gray-50 border-b border-gray-200">
            <span class="text-sm font-medium">輸出</span>
          </div>
          <div class="flex-1 p-3 overflow-auto bg-gray-50">
            <div
              v-if="previewLoading"
              class="flex justify-center items-center h-full"
            >
              <div
                class="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500"
              ></div>
            </div>
            <div
              v-else-if="previewError"
              class="text-red-500 text-sm whitespace-pre-wrap font-mono"
            >
              {{ previewError }}
            </div>
            <pre
              v-else-if="previewResult"
              class="text-sm font-mono whitespace-pre-wrap"
              >{{ formatJson(previewResult) }}</pre
            >
            <div
              v-else
              class="text-gray-400 text-sm flex justify-center items-center h-full"
            >
              點擊「執行」按鈕來測試工作流程
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ref,
  reactive,
  computed,
  onMounted,
  nextTick,
  onUnmounted,
  watch,
  onBeforeUnmount,
} from "vue";
import { VueFlow, useVueFlow } from "@vue-flow/core";
import { Controls } from "@vue-flow/controls";
import { MiniMap } from "@vue-flow/minimap";
import { Background } from "@vue-flow/background";
import { Button } from "@/components/ui/button";
import {
  Save,
  FolderOpen,
  Plus,
  Upload,
  Undo,
  Redo,
  Copy,
  Scissors,
  Clipboard,
  Eye,
  Play,
  X,
  Menu,
  Settings,
} from "lucide-vue-next";
import { useRoute } from "vue-router";
import { Textarea } from "@/components/ui/textarea";

// Import custom components
import NodePalette from "@/components/ai-creator-studio/NodePalette.vue";
import PropertyInspector from "@/components/ai-creator-studio/PropertyInspector.vue";
import WorkflowManagerDialog from "@/components/ai-creator-studio/WorkflowManagerDialog.vue";
import HelpPanel from "@/components/ai-creator-studio/HelpPanel.vue";

// Import custom node components
import AiBotNode from "@/components/ai-creator-studio/nodes/AiBotNode.vue";
import InputNode from "@/components/ai-creator-studio/nodes/InputNode.vue";
import OutputNode from "@/components/ai-creator-studio/nodes/OutputNode.vue";
import ConditionNode from "@/components/ai-creator-studio/nodes/ConditionNode.vue";
import DataTransformNode from "@/components/ai-creator-studio/nodes/DataTransformNode.vue";
import ApiCallNode from "@/components/ai-creator-studio/nodes/ApiCallNode.vue";

// Import services
import { useAiWorkflowsApi } from "@/composables/useAiWorkflowsApi";
import { useNotification } from "@/composables/shared/useNotification";

// Vue Flow composable
const {
  addNodes,
  addEdges,
  removeNodes,
  removeEdges,
  findNode,
  findEdge,
  project,
  onEdgesChange,
  onNodesChange,
} = useVueFlow();
const notification = useNotification();

// Vue Flow ref
const vueFlowRef = ref();

// Reactive state
const elements = ref<any[]>([]);
const selectedNode = ref<any>(null);
const selectedEdge = ref<any>(null);
const currentWorkflow = ref<any>(null);
const saving = ref(false);
const showWorkflowManager = ref(false);
const activeTab = ref("properties");

// 響應式狀態
const showLeftSidebar = ref(false);
const showRightSidebar = ref(false);

// API composable
const {
  createWorkflow,
  updateWorkflow,
  getWorkflow: loadWorkflow,
  publishWorkflow: apiPublishWorkflow,
  createNode,
  updateNode,
  deleteNode: apiDeleteNode,
  createConnection: apiCreateConnection,
  deleteConnection: apiDeleteConnection,
  executeWorkflow,
  loading,
} = useAiWorkflowsApi();

// Drag and drop state
const draggedNodeType = ref<string | null>(null);

// Route composable
const route = useRoute();

// 在 nodeTypes 對象中添加新的節點類型
const nodeTypes = {
  input: InputNode,
  output: OutputNode,
  "ai-bot": AiBotNode,
  condition: ConditionNode,
  "data-transform": DataTransformNode,
  "api-call": ApiCallNode,
};

// 擴展 getDefaultNodeData 函數以包含新的節點類型
const getDefaultNodeData = (nodeType: string) => {
  const defaults: Record<string, any> = {
    "ai-bot": {
      label: "AI 機器人",
      botId: null,
      config: {},
    },
    input: {
      label: "輸入",
      schema: {},
    },
    output: {
      label: "輸出",
      schema: {},
    },
    condition: {
      label: "條件",
      expression: "",
    },
    "data-transform": {
      label: "資料轉換",
      transformType: "map",
      description: "轉換資料格式",
      mapExpression: "",
      filterCondition: "",
      reduceExpression: "",
    },
    "api-call": {
      label: "API 調用",
      url: "",
      method: "GET",
      headers: {},
      body: "",
      description: "調用外部 API",
    },
  };

  return defaults[nodeType] || { label: "未知節點" };
};

// 添加以下代碼，實現複製/粘貼功能
// 定義複製/粘貼相關的狀態
const clipboard = ref<any[]>([]);
const isCopying = ref(false);

// 複製選中的節點
const copySelectedNodes = () => {
  const selectedNodes = elements.value.filter(
    (el) => !el.source && el.selected
  );

  if (selectedNodes.length === 0) {
    notification.toast.info("請先選擇要複製的節點");
    return;
  }

  clipboard.value = JSON.parse(JSON.stringify(selectedNodes));
  isCopying.value = true;
  notification.toast.success(`已複製 ${selectedNodes.length} 個節點`);
};

// 粘貼節點
const pasteNodes = () => {
  if (clipboard.value.length === 0) {
    notification.toast.info("剪貼簿中沒有節點");
    return;
  }

  // 計算粘貼偏移量，使粘貼的節點不會與原始節點重疊
  const PASTE_OFFSET = 50;

  // 為複製的節點創建新的 ID 並調整位置
  const newNodes = clipboard.value.map((node) => {
    const newId = `${node.type}-${Date.now()}-${Math.floor(Math.random() * 1000)}`;
    return {
      ...node,
      id: newId,
      selected: false,
      position: {
        x: node.position.x + PASTE_OFFSET,
        y: node.position.y + PASTE_OFFSET,
      },
    };
  });

  // 將新節點添加到畫布
  elements.value.push(...newNodes);

  notification.toast.success(`已粘貼 ${newNodes.length} 個節點`);

  // 如果是複製而不是剪切，則保留剪貼簿內容，但更新偏移量
  if (isCopying.value) {
    clipboard.value = newNodes;
  } else {
    clipboard.value = [];
  }
};

// 剪切選中的節點
const cutSelectedNodes = () => {
  const selectedNodes = elements.value.filter(
    (el) => !el.source && el.selected
  );

  if (selectedNodes.length === 0) {
    notification.toast.info("請先選擇要剪切的節點");
    return;
  }

  // 保存到剪貼簿
  clipboard.value = JSON.parse(JSON.stringify(selectedNodes));
  isCopying.value = false;

  // 從畫布中移除選中的節點
  const selectedNodeIds = selectedNodes.map((node) => node.id);
  elements.value = elements.value.filter(
    (el) =>
      !(
        selectedNodeIds.includes(el.id) ||
        (el.source && selectedNodeIds.includes(el.source)) ||
        (el.target && selectedNodeIds.includes(el.target))
      )
  );

  notification.toast.success(`已剪切 ${selectedNodes.length} 個節點`);
};

// 處理鍵盤快捷鍵
const handleKeyDown = (event: KeyboardEvent) => {
  // 只在編輯器有焦點時處理快捷鍵
  if (
    document.activeElement?.tagName === "INPUT" ||
    document.activeElement?.tagName === "TEXTAREA"
  ) {
    return;
  }

  // 複製: Ctrl+C
  if (event.ctrlKey && event.key === "c") {
    event.preventDefault();
    copySelectedNodes();
  }

  // 粘貼: Ctrl+V
  if (event.ctrlKey && event.key === "v") {
    event.preventDefault();
    pasteNodes();
  }

  // 剪切: Ctrl+X
  if (event.ctrlKey && event.key === "x") {
    event.preventDefault();
    cutSelectedNodes();
  }

  // 撤銷: Ctrl+Z
  if (event.ctrlKey && event.key === "z" && !event.shiftKey) {
    event.preventDefault();
    undo();
  }

  // 重做: Ctrl+Y 或 Ctrl+Shift+Z
  if (
    (event.ctrlKey && event.key === "y") ||
    (event.ctrlKey && event.shiftKey && event.key === "z")
  ) {
    event.preventDefault();
    redo();
  }

  // 刪除: Delete 或 Backspace
  if (event.key === "Delete" || event.key === "Backspace") {
    const selectedNodes = elements.value.filter(
      (el) => !el.source && el.selected
    );
    if (selectedNodes.length > 0) {
      selectedNodes.forEach((node) => deleteNode(node.id));
    }

    const selectedEdges = elements.value.filter(
      (el) => el.source && el.selected
    );
    if (selectedEdges.length > 0) {
      selectedEdges.forEach((edge) => {
        // 簡單地刪除選中的邊
        elements.value = elements.value.filter((el) => el.id !== edge.id);
      });
    }
  }
};

// Vue Flow元件內部的鍵盤事件處理
const onKeyDown = (event: KeyboardEvent) => {
  // 處理 Delete 或 Backspace 鍵刪除選中的節點或邊
  if (event.key === "Delete" || event.key === "Backspace") {
    // 如果有選中的節點，則刪除它
    if (selectedNode.value) {
      const nodeId = selectedNode.value.id;
      elements.value = elements.value.filter((el) => el.id !== nodeId);
      // 同時刪除與該節點相關的所有邊
      elements.value = elements.value.filter(
        (el) => !(el.source === nodeId || el.target === nodeId)
      );
      selectedNode.value = null;
    }
    // 如果有選中的邊，則刪除它
    else if (selectedEdge.value) {
      elements.value = elements.value.filter(
        (el) => el.id !== selectedEdge.value?.id
      );
      selectedEdge.value = null;
    }
  }
};

// 添加撤銷/重做功能
const history = ref<any[][]>([]);
const historyIndex = ref(-1);
const MAX_HISTORY = 50;

// 保存當前狀態
const saveCurrentState = () => {
  // 如果我們在歷史中間，則刪除從當前點開始的所有未來歷史
  if (historyIndex.value < history.value.length - 1) {
    history.value = history.value.slice(0, historyIndex.value + 1);
  }

  // 添加當前狀態到歷史
  history.value.push(JSON.parse(JSON.stringify(elements.value)));

  // 限制歷史大小
  if (history.value.length > MAX_HISTORY) {
    history.value = history.value.slice(history.value.length - MAX_HISTORY);
  }

  historyIndex.value = history.value.length - 1;
};

// 撤銷
const undo = () => {
  if (historyIndex.value > 0) {
    historyIndex.value--;
    elements.value = JSON.parse(
      JSON.stringify(history.value[historyIndex.value])
    );
    notification.toast.info("已撤銷");
  } else {
    notification.toast.info("無法撤銷");
  }
};

// 重做
const redo = () => {
  if (historyIndex.value < history.value.length - 1) {
    historyIndex.value++;
    elements.value = JSON.parse(
      JSON.stringify(history.value[historyIndex.value])
    );
    notification.toast.info("已重做");
  } else {
    notification.toast.info("無法重做");
  }
};

// 監聽節點變化並保存歷史
watch(
  elements,
  () => {
    // 使用防抖動來避免過於頻繁地保存歷史
    if (debounceTimer) clearTimeout(debounceTimer);
    debounceTimer = setTimeout(() => {
      saveCurrentState();
    }, 500);
  },
  { deep: true }
);

// 防抖動計時器
let debounceTimer: any = null;

// Lifecycle
onMounted(() => {
  // 檢查 URL 參數是否有工作流程 ID
  const workflowId = route.query.workflowId as string;
  if (workflowId) {
    loadWorkflow(workflowId);
  } else {
    // 如果沒有指定的工作流程 ID，則創建一個新的工作流程
    createNewWorkflow();
  }

  // 不再自動打開工作流程管理器

  // 先添加初始空白狀態
  history.value = [[]];
  historyIndex.value = 0;

  // 載入工作流程或創建新工作流程後更新歷史
  setTimeout(() => {
    saveCurrentState();
  }, 1000);

  // 添加鍵盤事件監聽器
  window.addEventListener("keydown", handleKeyDown);
});

onBeforeUnmount(() => {
  // 移除鍵盤事件監聽器
  window.removeEventListener("keydown", handleKeyDown);

  // 清除定時器
  if (debounceTimer) clearTimeout(debounceTimer);
});

// Event handlers
const onDragStart = (nodeType: string) => {
  draggedNodeType.value = nodeType;
  console.log("Drag started:", nodeType); // Debug log
};

const onDragOver = (event: DragEvent) => {
  event.preventDefault();
  event.stopPropagation();
  if (draggedNodeType.value) {
    event.dataTransfer!.dropEffect = "move";
  }
};

const onDrop = async (event: DragEvent) => {
  event.preventDefault();
  event.stopPropagation();

  console.log("Drop event triggered:", draggedNodeType.value); // Debug log

  if (!draggedNodeType.value) {
    return;
  }

  // 使用 Vue Flow 的 project 函數來正確計算座標
  const reactFlowBounds = vueFlowRef.value?.$el?.getBoundingClientRect();
  if (!reactFlowBounds) {
    console.error("Could not get Vue Flow bounds");
    return;
  }

  const position = project({
    x: event.clientX - reactFlowBounds.left,
    y: event.clientY - reactFlowBounds.top,
  });

  console.log("Calculated position:", position); // Debug log

  // Create new node
  const newNode = {
    id: `${draggedNodeType.value}-${Date.now()}`,
    type: draggedNodeType.value,
    position,
    data: getDefaultNodeData(draggedNodeType.value),
    connectable: true,
  };

  console.log("Creating new node:", newNode); // Debug log

  // Add to canvas
  addNodes([newNode]);

  // 若有後端 workflow，嘗試保存
  if (currentWorkflow.value) {
    try {
      await createNode(currentWorkflow.value.id, {
        name: newNode.data.label,
        node_type: draggedNodeType.value.toUpperCase(),
        position_x: position.x,
        position_y: position.y,
        config: newNode.data,
      });
      console.log("Node saved to backend successfully");
    } catch (error) {
      console.error("Failed to create node:", error);
      notification.toast.error("建立節點失敗");
      removeNodes([newNode.id]);
    }
  }

  draggedNodeType.value = null;
};

const onNodeDragStop = async (event: any) => {
  if (!currentWorkflow.value) return;

  const node = event.node;
  try {
    await updateNode(node.id, {
      position_x: node.position.x,
      position_y: node.position.y,
    });
  } catch (error) {
    console.error("Failed to update node position:", error);
  }
};

const onConnect = async (connection: any) => {
  console.log("Connection event:", connection); // Debug log

  // Create the edge locally first
  const newEdge = {
    id: `${connection.source}-${connection.target}-${Date.now()}`,
    source: connection.source,
    target: connection.target,
    sourceHandle: connection.sourceHandle,
    targetHandle: connection.targetHandle,
  };

  console.log("Creating new edge:", newEdge); // Debug log

  // Add to local elements array immediately
  elements.value.push(newEdge);

  // Also add using Vue Flow's addEdges method
  addEdges([newEdge]);

  // If we have a backend workflow, try to save the connection
  if (currentWorkflow.value) {
    try {
      await apiCreateConnection(currentWorkflow.value.id, {
        source_node_id: connection.source,
        target_node_id: connection.target,
        source_port: connection.sourceHandle,
        target_port: connection.targetHandle,
      });
      console.log("Connection saved to backend successfully");
    } catch (error) {
      console.error("Failed to create connection:", error);
      notification.toast.error("建立連線失敗");

      // Remove the edge from local state if backend save failed
      const edgeIndex = elements.value.findIndex(
        (edge) => edge.id === newEdge.id
      );
      if (edgeIndex !== -1) {
        elements.value.splice(edgeIndex, 1);
      }
      removeEdges([newEdge.id]);
    }
  }
};

const onNodeClick = (event: any) => {
  console.log("Node clicked:", event.node); // Debug log
  selectedNode.value = event.node;
  selectedEdge.value = null;
};

const onEdgeClick = (event: any) => {
  console.log("Edge clicked:", event.edge); // Debug log
  selectedEdge.value = event.edge;
  selectedNode.value = null;
};

// Edge deletion functionality
const onEdgeContextMenu = (event: any) => {
  event.preventDefault();
  const edge = event.edge;

  // Show context menu or directly delete
  if (confirm(`確定要刪除連接線嗎？`)) {
    deleteEdge(edge.id);
  }
};

const deleteEdge = async (edgeId: string) => {
  console.log("Deleting edge:", edgeId);

  // Remove from local state
  const edgeIndex = elements.value.findIndex((el) => el.id === edgeId);
  if (edgeIndex !== -1) {
    elements.value.splice(edgeIndex, 1);
  }

  // Remove using Vue Flow API
  removeEdges([edgeId]);

  // Remove from backend if we have a workflow
  if (currentWorkflow.value) {
    try {
      await apiDeleteConnection(currentWorkflow.value.id, edgeId);
      console.log("Edge deleted from backend");
    } catch (error) {
      console.error("Failed to delete edge from backend:", error);
      notification.toast.error("刪除連線失敗");
    }
  }

  // Clear selection if this edge was selected
  if (selectedEdge.value?.id === edgeId) {
    selectedEdge.value = null;
  }
};

const deleteNode = (id: string) => {
  const node = elements.value.find((el) => el.id === id);
  if (!node) return;

  // Remove from vue flow
  elements.value = elements.value.filter((el) => {
    return el.id !== id && el.source !== id && el.target !== id;
  });

  // Remove from backend if we have a workflow
  if (currentWorkflow.value) {
    apiDeleteNode(currentWorkflow.value.id, id).catch((error) => {
      console.error("Failed to delete node:", error);
    });
  }
};

// Workflow management
const createNewWorkflow = async () => {
  try {
    const workflow = await createWorkflow({
      name: "新工作流程",
      description: "一個新的 AI 工作流程",
    });
    currentWorkflow.value = workflow;
    elements.value = [];
    notification.toast.success("新工作流程已建立");
  } catch (error) {
    console.error("Failed to create workflow:", error);
    notification.toast.error("建立工作流程失敗");
  }
};

const saveWorkflow = async () => {
  if (!currentWorkflow.value) return;

  saving.value = true;
  try {
    // Prepare the workflow data with nodes and edges
    const nodes = elements.value
      .filter((el) => !el.source)
      .map((node) => ({
        id: node.id,
        name: node.data.label || "未命名節點",
        node_type: node.type.toUpperCase().replace("-", "_"),
        position_x: node.position.x,
        position_y: node.position.y,
        config: node.data,
      }));

    const edges = elements.value
      .filter((el) => el.source)
      .map((edge) => ({
        id: edge.id,
        source_node_id: edge.source,
        target_node_id: edge.target,
        source_port: edge.sourceHandle,
        target_port: edge.targetHandle,
      }));

    console.log("Saving workflow with:", { nodes, edges });

    // Update the workflow metadata
    await updateWorkflow(currentWorkflow.value.id, {
      name: currentWorkflow.value.name,
      description: currentWorkflow.value.description,
    });

    // TODO: Save nodes and edges structure to backend
    // This might require separate API calls to save the complete structure
    console.log("Workflow structure ready for saving:", { nodes, edges });

    notification.toast.success("工作流程已儲存");
  } catch (error) {
    console.error("Failed to save workflow:", error);
    notification.toast.error("儲存工作流程失敗");
  } finally {
    saving.value = false;
  }
};

const publishWorkflow = async () => {
  if (!currentWorkflow.value) return;

  try {
    await apiPublishWorkflow(currentWorkflow.value.id);
    currentWorkflow.value.is_published = true;
    notification.toast.success("工作流程已發布");
  } catch (error) {
    console.error("Failed to publish workflow:", error);
    notification.toast.error("發布工作流程失敗");
  }
};

// Node management
const openNodeConfig = (nodeId: string) => {
  const node = findNode(nodeId);
  if (node) {
    selectedNode.value = node;
  }
};

const updateNodeData = (nodeId: string, data: any) => {
  const node = findNode(nodeId);
  if (node) {
    node.data = { ...node.data, ...data };
  }
};

const updateEdgeData = (edgeId: string, data: any) => {
  const edge = findEdge(edgeId);
  if (edge) {
    edge.data = { ...edge.data, ...data };
  }
};

const openWorkflowManager = () => {
  showWorkflowManager.value = true;
};

// 快速預覽功能
const showPreviewPanel = ref(false);
const previewInput = ref('{\n  "message": "你好，AI"\n}');
const previewResult = ref<any>(null);
const previewError = ref<string | null>(null);
const previewLoading = ref(false);

const togglePreviewPanel = () => {
  showPreviewPanel.value = !showPreviewPanel.value;
};

const executePreview = async () => {
  if (!currentWorkflow.value) {
    notification.toast.error("請先保存工作流程");
    return;
  }

  previewLoading.value = true;
  previewError.value = null;
  previewResult.value = null;

  try {
    // 解析輸入 JSON
    let inputData;
    try {
      inputData = JSON.parse(previewInput.value);
    } catch (e) {
      previewError.value = "輸入資料格式錯誤，請確保是有效的 JSON";
      return;
    }

    // 執行工作流程
    const result = await executeWorkflow(currentWorkflow.value.id, inputData);
    previewResult.value = result;
  } catch (error: any) {
    console.error("執行工作流程失敗:", error);
    previewError.value = `執行錯誤: ${error.message || "未知錯誤"}`;
  } finally {
    previewLoading.value = false;
  }
};

// 響應式函數
const toggleLeftSidebar = () => {
  showLeftSidebar.value = !showLeftSidebar.value;
};

const toggleRightSidebar = () => {
  showRightSidebar.value = !showRightSidebar.value;
};

// 監聽螢幕尺寸變化，自動調整側邊欄顯示
const handleResize = () => {
  if (window.innerWidth >= 1024) {
    showLeftSidebar.value = false;
    showRightSidebar.value = false;
  }
};

onMounted(() => {
  window.addEventListener('resize', handleResize);
});

onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize);
});

const formatJson = (json: any): string => {
  return JSON.stringify(json, null, 2);
};
</script>

<style>
/* Vue Flow base styles */
@import "@vue-flow/core/dist/style.css";
@import "@vue-flow/core/dist/theme-default.css";
@import "@vue-flow/controls/dist/style.css";
@import "@vue-flow/minimap/dist/style.css";

.vue-flow-basic-example {
  background: #f8fafc;
}

.vue-flow-basic-example .vue-flow__node {
  background: transparent !important;
  border: none !important;
  box-shadow: none !important;
}

.vue-flow-basic-example .vue-flow__node:hover {
  /* Keep custom node hover if needed */
}

.vue-flow-basic-example .vue-flow__edge-default {
  stroke: #6b7280;
  stroke-width: 2;
}

.vue-flow-basic-example .vue-flow__edge.selected {
  stroke: #3b82f6;
  stroke-width: 3;
}

/* Handle styling */
.vue-flow-basic-example .vue-flow__handle {
  width: 12px !important;
  height: 12px !important;
  border-radius: 50% !important;
  border: 2px solid white !important;
  background: #6b7280 !important;
}

.vue-flow-basic-example .vue-flow__handle:hover {
  background: #3b82f6 !important;
}

.vue-flow-basic-example .vue-flow__handle.connectable {
  cursor: crosshair !important;
}

.vue-flow-basic-example .vue-flow__handle.connecting {
  background: #ef4444 !important;
}

/* Selection styling */
.vue-flow-basic-example .vue-flow__node.selected {
  box-shadow: 0 0 0 2px #3b82f6 !important;
}

.vue-flow-basic-example .vue-flow__edge.selected {
  stroke: #3b82f6 !important;
  stroke-width: 3px !important;
}

.vue-flow-basic-example .vue-flow__edge:hover {
  stroke: #6366f1 !important;
  stroke-width: 2.5px !important;
}
</style>
