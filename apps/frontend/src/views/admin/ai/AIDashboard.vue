<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 頁面標題 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">AI 管理中心</h1>
        <p class="text-muted-foreground mt-2">統一管理平台的 AI 功能與設定</p>
      </div>
      <div class="flex gap-2">
        <Button @click="refreshData" variant="outline" size="sm">
          <RefreshCw class="w-4 h-4 mr-2" :class="{ 'animate-spin': loading }" />
          重新整理
        </Button>
        <Button @click="router.push('/admin/ai-settings')" size="sm">
          <Settings class="w-4 h-4 mr-2" />
          進入設定
        </Button>
      </div>
    </div>

    <!-- 系統狀態卡片 -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card class="p-4">
        <div class="flex items-center space-x-2">
          <div class="p-2 bg-green-100 rounded-lg">
            <CheckCircle class="w-5 h-5 text-green-600" />
          </div>
          <div>
            <p class="text-sm font-medium">系統狀態</p>
            <p class="text-xs text-muted-foreground">運行正常</p>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center space-x-2">
          <div class="p-2 bg-blue-100 rounded-lg">
            <Bot class="w-5 h-5 text-blue-600" />
          </div>
          <div>
            <p class="text-sm font-medium">AI 助理</p>
            <p class="text-xs text-muted-foreground">{{ stats.totalBots }} 個已配置</p>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center space-x-2">
          <div class="p-2 bg-purple-100 rounded-lg">
            <Workflow class="w-5 h-5 text-purple-600" />
          </div>
          <div>
            <p class="text-sm font-medium">工作流程</p>
            <p class="text-xs text-muted-foreground">{{ stats.totalWorkflows }} 個流程</p>
          </div>
        </div>
      </Card>

      <Card class="p-4">
        <div class="flex items-center space-x-2">
          <div class="p-2 bg-orange-100 rounded-lg">
            <Activity class="w-5 h-5 text-orange-600" />
          </div>
          <div>
            <p class="text-sm font-medium">本月執行</p>
            <p class="text-xs text-muted-foreground">{{ stats.monthlyExecutions }} 次</p>
          </div>
        </div>
      </Card>
    </div>

    <!-- 主要功能區塊 -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 快速操作 -->
      <Card class="p-6">
        <h2 class="text-lg font-semibold mb-4 flex items-center">
          <Zap class="w-5 h-5 mr-2" />
          快速操作
        </h2>
        <div class="grid grid-cols-2 gap-3">
          <Button 
            @click="router.push('/admin/ai-settings?tab=bots')" 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Bot class="w-6 h-6" />
            <span class="text-sm">建立 AI 助理</span>
          </Button>
          
          <Button 
            @click="router.push('/admin/ai-creator-studio')" 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Workflow class="w-6 h-6" />
            <span class="text-sm">建立工作流程</span>
          </Button>
          
          <Button 
            @click="router.push('/admin/ai-settings?tab=keys')" 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Key class="w-6 h-6" />
            <span class="text-sm">API 金鑰設定</span>
          </Button>
          
          <Button 
            @click="router.push('/admin/ai-settings?tab=models')" 
            variant="outline" 
            class="h-auto p-4 flex flex-col items-center space-y-2"
          >
            <Brain class="w-6 h-6" />
            <span class="text-sm">AI 模型管理</span>
          </Button>
        </div>
      </Card>

      <!-- 使用統計 -->
      <Card class="p-6">
        <h2 class="text-lg font-semibold mb-4 flex items-center">
          <BarChart class="w-5 h-5 mr-2" />
          使用統計
        </h2>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm text-muted-foreground">API 呼叫</span>
            <span class="font-medium">{{ stats.apiCalls.toLocaleString() }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-muted-foreground">Token 使用</span>
            <span class="font-medium">{{ stats.tokenUsage.toLocaleString() }}</span>
          </div>
          <div class="flex items-center justify-between">
            <span class="text-sm text-muted-foreground">工作流程執行</span>
            <span class="font-medium">{{ stats.workflowExecutions }}</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2 mt-4">
            <div 
              class="bg-blue-600 h-2 rounded-full transition-all duration-500" 
              :style="{ width: `${(stats.tokenUsage / stats.tokenLimit * 100)}%` }"
            />
          </div>
          <p class="text-xs text-muted-foreground text-center">
            Token 使用率: {{ Math.round(stats.tokenUsage / stats.tokenLimit * 100) }}%
          </p>
        </div>
      </Card>
    </div>

    <!-- 最近活動與工作流程狀態 -->
    <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <!-- 最近活動 -->
      <Card class="lg:col-span-2 p-6">
        <h2 class="text-lg font-semibold mb-4 flex items-center">
          <Clock class="w-5 h-5 mr-2" />
          最近活動
        </h2>
        <div class="space-y-3">
          <div v-for="activity in recentActivities" :key="activity.id" class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
            <div class="p-2 rounded-full" :class="getActivityIconClass(activity.type)">
              <component :is="getActivityIcon(activity.type)" class="w-4 h-4" />
            </div>
            <div class="flex-1">
              <p class="text-sm font-medium">{{ activity.title }}</p>
              <p class="text-xs text-muted-foreground">{{ activity.description }}</p>
            </div>
            <span class="text-xs text-muted-foreground">{{ formatTime(activity.timestamp) }}</span>
          </div>
        </div>
      </Card>

      <!-- 系統健康度 -->
      <Card class="p-6">
        <h2 class="text-lg font-semibold mb-4 flex items-center">
          <Shield class="w-5 h-5 mr-2" />
          系統健康度
        </h2>
        <div class="space-y-4">
          <div class="flex items-center justify-between">
            <span class="text-sm">API 響應時間</span>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-sm font-medium">{{ healthMetrics.responseTime }}ms</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm">錯誤率</span>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-yellow-500 rounded-full"></div>
              <span class="text-sm font-medium">{{ healthMetrics.errorRate }}%</span>
            </div>
          </div>
          
          <div class="flex items-center justify-between">
            <span class="text-sm">可用性</span>
            <div class="flex items-center space-x-2">
              <div class="w-2 h-2 bg-green-500 rounded-full"></div>
              <span class="text-sm font-medium">{{ healthMetrics.uptime }}%</span>
            </div>
          </div>
        </div>
      </Card>
    </div>

    <!-- 用戶引導 -->
    <UserGuide 
      :steps="guideSteps" 
      guide-key="ai-dashboard"
      @complete="onGuideComplete"
    />

    <!-- 快捷鍵說明 -->
    <div 
      v-if="showHelp" 
      class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
      @click.self="showHelp = false"
    >
      <div class="bg-white rounded-lg p-6 max-w-lg mx-4 max-h-[80vh] overflow-y-auto">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-semibold">快捷鍵說明</h3>
          <Button @click="showHelp = false" variant="ghost" size="sm">
            <X class="w-4 h-4" />
          </Button>
        </div>
        
        <div class="space-y-3">
          <div 
            v-for="shortcut in getShortcutsByScope('ai')" 
            :key="shortcut.key + shortcut.description" 
            class="flex items-center justify-between"
          >
            <span class="text-sm text-gray-600">{{ shortcut.description }}</span>
            <div class="flex items-center space-x-1">
              <kbd v-if="shortcut.ctrlKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Ctrl</kbd>
              <kbd v-if="shortcut.altKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Alt</kbd>
              <kbd v-if="shortcut.shiftKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Shift</kbd>
              <kbd v-if="shortcut.metaKey" class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">Cmd</kbd>
              <kbd class="px-2 py-1 text-xs bg-gray-100 border border-gray-300 rounded">{{ shortcut.key.toUpperCase() }}</kbd>
            </div>
          </div>
        </div>
        
        <div class="mt-4 pt-4 border-t border-gray-200 text-xs text-gray-500">
          提示：在輸入框中時快捷鍵會被停用
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useKeyboardShortcuts } from '@/composables/useKeyboardShortcuts'
import UserGuide from '@/components/common/UserGuide.vue'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { 
  RefreshCw, Settings, CheckCircle, Bot, Workflow, Activity, 
  Zap, Key, Brain, BarChart, Clock, Shield, AlertCircle,
  PlayCircle, Pause
} from 'lucide-vue-next'

const router = useRouter()
const loading = ref(false)

// 引導和快捷鍵
const { showHelp, showShortcutHelp, getShortcutsByScope } = useKeyboardShortcuts()

// 引導步驟
const guideSteps = [
  {
    title: '歡迎使用 AI 管理中心',
    description: '這裡是管理所有 AI 功能的統一入口，您可以查看系統狀態、執行快速操作。',
    icon: 'Home'
  },
  {
    title: '快速操作區',
    description: '點擊這些按鈕可以快速創建 AI 助理、工作流程或管理 API 金鑰。',
    icon: 'Zap'
  },
  {
    title: '使用統計',
    description: '查看 AI 服務的使用情況，包括 API 呼叫次數和 Token 使用量。',
    icon: 'BarChart'
  },
  {
    title: '快捷鍵支援',
    description: '按 Alt+H 查看所有可用的快捷鍵，提高使用效率。',
    icon: 'Keyboard'
  }
]

// 統計數據
const stats = reactive({
  totalBots: 8,
  totalWorkflows: 12,
  monthlyExecutions: 1247,
  apiCalls: 15632,
  tokenUsage: 245000,
  tokenLimit: 500000,
  workflowExecutions: 89
})

// 最近活動
const recentActivities = ref([
  {
    id: 1,
    type: 'workflow',
    title: '客戶服務工作流程執行成功',
    description: '處理了 15 個客戶詢問',
    timestamp: new Date(Date.now() - 5 * 60000)
  },
  {
    id: 2,
    type: 'bot',
    title: '新增 AI 助理「產品推薦」',
    description: '配置完成並開始運行',
    timestamp: new Date(Date.now() - 25 * 60000)
  },
  {
    id: 3,
    type: 'error',
    title: 'API 金鑰即將到期',
    description: 'OpenAI API 金鑰將於 3 天後到期',
    timestamp: new Date(Date.now() - 2 * 3600000)
  }
])

// 健康度指標
const healthMetrics = reactive({
  responseTime: 156,
  errorRate: 0.2,
  uptime: 99.8
})

// 活動圖示相關函數
const getActivityIcon = (type: string) => {
  switch (type) {
    case 'workflow': return PlayCircle
    case 'bot': return Bot
    case 'error': return AlertCircle
    default: return Activity
  }
}

const getActivityIconClass = (type: string) => {
  switch (type) {
    case 'workflow': return 'bg-green-100 text-green-600'
    case 'bot': return 'bg-blue-100 text-blue-600'
    case 'error': return 'bg-red-100 text-red-600'
    default: return 'bg-gray-100 text-gray-600'
  }
}

const formatTime = (timestamp: Date) => {
  const now = new Date()
  const diff = now.getTime() - timestamp.getTime()
  const minutes = Math.floor(diff / 60000)
  const hours = Math.floor(minutes / 60)
  
  if (hours > 0) return `${hours} 小時前`
  if (minutes > 0) return `${minutes} 分鐘前`
  return '剛剛'
}

const refreshData = async () => {
  loading.value = true
  // 模擬 API 呼叫
  await new Promise(resolve => setTimeout(resolve, 1000))
  loading.value = false
}

const onGuideComplete = () => {
  console.log('用戶完成了 AI 管理中心引導')
}

onMounted(() => {
  // 載入初始數據
  refreshData()
})
</script>