<template>
  <div class="container mx-auto p-6 space-y-6">
    <!-- 頁面標題 -->
    <div class="flex items-center justify-between">
      <div>
        <h1 class="text-3xl font-bold">AI 測試中心</h1>
        <p class="text-muted-foreground mt-2">測試和驗證 AI 功能與工作流程</p>
      </div>
      <div class="flex gap-2">
        <Button @click="router.push('/admin/ai-dashboard')" variant="outline" size="sm">
          <ArrowLeft class="w-4 h-4 mr-2" />
          返回儀表板
        </Button>
      </div>
    </div>

    <!-- 測試類型選擇 -->
    <Tabs v-model:value="activeTab" class="w-full">
      <TabsList class="grid w-full grid-cols-3">
        <TabsTrigger value="bot-testing" class="flex items-center gap-2">
          <Bot class="w-4 h-4" />
          AI 助理測試
        </TabsTrigger>
        <TabsTrigger value="workflow-testing" class="flex items-center gap-2">
          <Workflow class="w-4 h-4" />
          工作流程測試
        </TabsTrigger>
        <TabsTrigger value="api-testing" class="flex items-center gap-2">
          <Zap class="w-4 h-4" />
          API 測試
        </TabsTrigger>
      </TabsList>

      <!-- AI 助理測試 -->
      <TabsContent value="bot-testing" class="space-y-6">
        <Card class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold">AI 助理對話測試</h2>
            <Button @click="router.push('/admin/ai-bot-tester')" size="sm">
              <ExternalLink class="w-4 h-4 mr-2" />
              進入詳細測試
            </Button>
          </div>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- 快速測試區 -->
            <div class="space-y-3">
              <Label>選擇 AI 助理</Label>
              <Select v-model:value="selectedBot">
                <SelectTrigger>
                  <SelectValue placeholder="請選擇助理" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="bot in availableBots" :key="bot.id" :value="bot.id">
                    {{ bot.name }}
                  </SelectItem>
                </SelectContent>
              </Select>
              
              <div class="space-y-2">
                <Label>測試訊息</Label>
                <Textarea 
                  v-model="testMessage" 
                  placeholder="輸入測試訊息..." 
                  rows="3"
                />
              </div>
              
              <Button @click="sendTestMessage" :disabled="!selectedBot || !testMessage || testing" class="w-full">
                <MessageSquare class="w-4 h-4 mr-2" />
                {{ testing ? '測試中...' : '發送測試' }}
              </Button>
            </div>
            
            <!-- 測試結果 -->
            <div class="space-y-3">
              <Label>測試結果</Label>
              <div class="border rounded-md p-4 min-h-[200px] bg-muted/50">
                <div v-if="testResult" class="space-y-2">
                  <div class="text-sm font-medium text-green-600">✓ 測試成功</div>
                  <div class="text-sm bg-white p-2 rounded border">
                    {{ testResult.response }}
                  </div>
                  <div class="text-xs text-muted-foreground">
                    響應時間: {{ testResult.responseTime }}ms | 
                    用量: {{ testResult.tokenUsage }} tokens
                  </div>
                </div>
                <div v-else-if="testError" class="text-sm text-red-600">
                  ✗ 測試失敗: {{ testError }}
                </div>
                <div v-else class="text-sm text-muted-foreground">
                  請發送測試訊息查看結果
                </div>
              </div>
            </div>
          </div>
        </Card>
      </TabsContent>

      <!-- 工作流程測試 -->
      <TabsContent value="workflow-testing" class="space-y-6">
        <Card class="p-6">
          <div class="flex items-center justify-between mb-4">
            <h2 class="text-lg font-semibold">工作流程執行測試</h2>
            <div class="flex gap-2">
              <Button @click="router.push('/admin/ai-workflows')" variant="outline" size="sm">
                <Settings class="w-4 h-4 mr-2" />
                管理工作流程
              </Button>
              <Button @click="router.push('/admin/ai-creator-studio')" size="sm">
                <PlusCircle class="w-4 h-4 mr-2" />
                建立新流程
              </Button>
            </div>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <!-- 工作流程選擇 -->
            <div class="space-y-3">
              <Label>選擇工作流程</Label>
              <div class="space-y-2 max-h-64 overflow-y-auto">
                <div 
                  v-for="workflow in availableWorkflows" 
                  :key="workflow.id"
                  @click="selectedWorkflow = workflow.id"
                  class="p-3 border rounded-lg cursor-pointer transition-colors"
                  :class="selectedWorkflow === workflow.id ? 'border-primary bg-primary/10' : 'hover:bg-muted/50'"
                >
                  <div class="font-medium text-sm">{{ workflow.name }}</div>
                  <div class="text-xs text-muted-foreground mt-1">{{ workflow.description }}</div>
                  <div class="flex items-center gap-2 mt-2">
                    <Badge :variant="workflow.status === 'active' ? 'default' : 'secondary'" class="text-xs">
                      {{ workflow.status }}
                    </Badge>
                    <span class="text-xs text-muted-foreground">{{ workflow.nodeCount }} 節點</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 測試參數 -->
            <div class="space-y-3">
              <Label>測試參數</Label>
              <div class="space-y-2">
                <div>
                  <Label class="text-xs">輸入數據 (JSON)</Label>
                  <Textarea 
                    v-model="workflowInput" 
                    placeholder='{"key": "value"}' 
                    rows="4"
                    class="font-mono text-sm"
                  />
                </div>
                <div class="flex items-center space-x-2">
                  <Checkbox v-model:checked="debugMode" id="debug" />
                  <Label for="debug" class="text-sm">除錯模式</Label>
                </div>
                <Button 
                  @click="executeWorkflowTest" 
                  :disabled="!selectedWorkflow || executingWorkflow" 
                  class="w-full"
                >
                  <Play class="w-4 h-4 mr-2" />
                  {{ executingWorkflow ? '執行中...' : '執行測試' }}
                </Button>
              </div>
            </div>

            <!-- 執行結果 -->
            <div class="space-y-3">
              <Label>執行結果</Label>
              <div class="border rounded-md p-4 min-h-[300px] bg-muted/50">
                <div v-if="workflowResult" class="space-y-3">
                  <div class="flex items-center gap-2">
                    <CheckCircle class="w-4 h-4 text-green-600" />
                    <span class="text-sm font-medium">執行成功</span>
                  </div>
                  <div class="text-xs bg-white p-2 rounded border font-mono">
                    <pre>{{ JSON.stringify(workflowResult.output, null, 2) }}</pre>
                  </div>
                  <div class="text-xs text-muted-foreground border-t pt-2">
                    執行時間: {{ workflowResult.duration }}ms | 
                    成功節點: {{ workflowResult.successNodes }}/{{ workflowResult.totalNodes }}
                  </div>
                </div>
                <div v-else-if="workflowError" class="text-sm text-red-600">
                  <div class="flex items-center gap-2 mb-2">
                    <XCircle class="w-4 h-4" />
                    <span class="font-medium">執行失敗</span>
                  </div>
                  <div class="bg-red-50 p-2 rounded text-xs">{{ workflowError }}</div>
                </div>
                <div v-else class="text-sm text-muted-foreground">
                  選擇工作流程並執行測試
                </div>
              </div>
            </div>
          </div>
        </Card>
      </TabsContent>

      <!-- API 測試 -->
      <TabsContent value="api-testing" class="space-y-6">
        <Card class="p-6">
          <h2 class="text-lg font-semibold mb-4">API 連接測試</h2>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- API 提供商測試 -->
            <div class="space-y-4">
              <h3 class="font-medium">AI 服務連接測試</h3>
              <div class="space-y-3">
                <div v-for="provider in apiProviders" :key="provider.name" class="flex items-center justify-between p-3 border rounded-lg">
                  <div class="flex items-center gap-3">
                    <div class="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                      <component :is="provider.icon" class="w-4 h-4" />
                    </div>
                    <div>
                      <div class="font-medium text-sm">{{ provider.name }}</div>
                      <div class="text-xs text-muted-foreground">{{ provider.description }}</div>
                    </div>
                  </div>
                  <div class="flex items-center gap-2">
                    <div 
                      class="w-2 h-2 rounded-full"
                      :class="provider.status === 'connected' ? 'bg-green-500' : 
                              provider.status === 'error' ? 'bg-red-500' : 'bg-yellow-500'"
                    />
                    <Button @click="testApiConnection(provider)" size="sm" variant="outline">
                      測試
                    </Button>
                  </div>
                </div>
              </div>
            </div>

            <!-- 系統健康檢查 -->
            <div class="space-y-4">
              <h3 class="font-medium">系統健康檢查</h3>
              <div class="space-y-3">
                <div v-for="check in healthChecks" :key="check.name" class="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <div class="font-medium text-sm">{{ check.name }}</div>
                    <div class="text-xs text-muted-foreground">{{ check.description }}</div>
                  </div>
                  <div class="flex items-center gap-2">
                    <div 
                      class="w-2 h-2 rounded-full"
                      :class="check.status === 'healthy' ? 'bg-green-500' : 
                              check.status === 'unhealthy' ? 'bg-red-500' : 'bg-yellow-500'"
                    />
                    <span class="text-xs">{{ check.value }}</span>
                  </div>
                </div>
              </div>
              
              <Button @click="runFullHealthCheck" :disabled="runningHealthCheck" class="w-full">
                <Shield class="w-4 h-4 mr-2" />
                {{ runningHealthCheck ? '檢查中...' : '執行完整檢查' }}
              </Button>
            </div>
          </div>
        </Card>
      </TabsContent>
    </Tabs>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Card } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Checkbox } from '@/components/ui/checkbox'
import { 
  ArrowLeft, Bot, Workflow, Zap, ExternalLink, MessageSquare, Settings, 
  PlusCircle, Play, CheckCircle, XCircle, Shield, Brain, Key, Database
} from 'lucide-vue-next'

const router = useRouter()
const activeTab = ref('bot-testing')

// AI 助理測試相關
const selectedBot = ref('')
const testMessage = ref('')
const testing = ref(false)
const testResult = ref<any>(null)
const testError = ref('')

const availableBots = ref([
  { id: '1', name: '客戶服務助理' },
  { id: '2', name: '產品推薦助理' },
  { id: '3', name: '技術支援助理' }
])

// 工作流程測試相關
const selectedWorkflow = ref('')
const workflowInput = ref('{\n  "input": "測試數據"\n}')
const debugMode = ref(false)
const executingWorkflow = ref(false)
const workflowResult = ref<any>(null)
const workflowError = ref('')

const availableWorkflows = ref([
  {
    id: '1',
    name: '客戶諮詢處理',
    description: '自動處理客戶諮詢並分類',
    status: 'active',
    nodeCount: 5
  },
  {
    id: '2',
    name: '內容生成流程',
    description: '根據關鍵字生成行銷內容',
    status: 'active',
    nodeCount: 8
  },
  {
    id: '3',
    name: '數據分析管道',
    description: '分析用戶行為數據',
    status: 'draft',
    nodeCount: 12
  }
])

// API 測試相關
const apiProviders = ref([
  {
    name: 'OpenAI',
    description: 'GPT 模型服務',
    icon: Brain,
    status: 'connected'
  },
  {
    name: 'Anthropic',
    description: 'Claude 模型服務',
    icon: Bot,
    status: 'connected'
  },
  {
    name: 'Google AI',
    description: 'Gemini 模型服務',
    icon: Zap,
    status: 'warning'
  }
])

const healthChecks = ref([
  {
    name: '資料庫連接',
    description: 'PostgreSQL 連接狀態',
    status: 'healthy',
    value: '< 10ms'
  },
  {
    name: 'Redis 快取',
    description: '快取服務狀態',
    status: 'healthy',
    value: '正常'
  },
  {
    name: '檔案儲存',
    description: '檔案上傳服務',
    status: 'healthy',
    value: '可用'
  }
])

const runningHealthCheck = ref(false)

// 測試函數
const sendTestMessage = async () => {
  if (!selectedBot.value || !testMessage.value) return
  
  testing.value = true
  testResult.value = null
  testError.value = ''
  
  try {
    // 模擬 API 呼叫
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    testResult.value = {
      response: '這是一個測試回應。我已經收到您的訊息並進行了處理。',
      responseTime: Math.floor(Math.random() * 1000) + 500,
      tokenUsage: Math.floor(Math.random() * 100) + 50
    }
  } catch (error) {
    testError.value = '測試失敗，請檢查 AI 助理設定'
  } finally {
    testing.value = false
  }
}

const executeWorkflowTest = async () => {
  if (!selectedWorkflow.value) return
  
  executingWorkflow.value = true
  workflowResult.value = null
  workflowError.value = ''
  
  try {
    // 模擬工作流程執行
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    workflowResult.value = {
      output: {
        status: 'completed',
        result: '工作流程執行成功',
        processedData: JSON.parse(workflowInput.value)
      },
      duration: Math.floor(Math.random() * 2000) + 1000,
      successNodes: 4,
      totalNodes: 5
    }
  } catch (error) {
    workflowError.value = '工作流程執行失敗，請檢查輸入參數'
  } finally {
    executingWorkflow.value = false
  }
}

const testApiConnection = async (provider: any) => {
  provider.status = 'testing'
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1500))
    provider.status = Math.random() > 0.2 ? 'connected' : 'error'
  } catch (error) {
    provider.status = 'error'
  }
}

const runFullHealthCheck = async () => {
  runningHealthCheck.value = true
  
  for (const check of healthChecks.value) {
    check.status = 'checking'
    await new Promise(resolve => setTimeout(resolve, 500))
    check.status = Math.random() > 0.1 ? 'healthy' : 'unhealthy'
  }
  
  runningHealthCheck.value = false
}

onMounted(() => {
  // 載入初始數據
})
</script>