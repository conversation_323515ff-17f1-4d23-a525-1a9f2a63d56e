<template>
  <div 
    v-if="showGuide" 
    class="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center"
    @click.self="closeGuide"
  >
    <div class="bg-white rounded-lg p-6 max-w-md mx-4 relative">
      <Button
        @click="closeGuide"
        variant="ghost"
        size="sm"
        class="absolute top-2 right-2"
      >
        <X class="h-4 w-4" />
      </Button>

      <div class="space-y-4">
        <!-- 當前步驟內容 -->
        <div class="flex items-center space-x-3">
          <div class="w-8 h-8 bg-primary text-white rounded-full flex items-center justify-center text-sm font-bold">
            {{ currentStep + 1 }}
          </div>
          <div>
            <h3 class="font-semibold">{{ steps[currentStep].title }}</h3>
            <p class="text-sm text-muted-foreground">步驟 {{ currentStep + 1 }} / {{ steps.length }}</p>
          </div>
        </div>

        <div class="text-sm text-gray-600">
          {{ steps[currentStep].description }}
        </div>

        <!-- 螢幕截圖或圖示 -->
        <div v-if="steps[currentStep].image" class="w-full h-32 bg-gray-100 rounded-lg flex items-center justify-center">
          <component :is="steps[currentStep].icon" class="w-12 h-12 text-gray-400" />
        </div>

        <!-- 進度指示器 -->
        <div class="flex space-x-1">
          <div 
            v-for="(step, index) in steps" 
            :key="index"
            class="flex-1 h-2 rounded-full"
            :class="index <= currentStep ? 'bg-primary' : 'bg-gray-200'"
          />
        </div>

        <!-- 導航按鈕 -->
        <div class="flex justify-between items-center pt-4">
          <Button
            @click="previousStep"
            variant="outline"
            size="sm"
            :disabled="currentStep === 0"
          >
            上一步
          </Button>

          <div class="flex space-x-2">
            <Button
              @click="skipGuide"
              variant="ghost"
              size="sm"
              class="text-muted-foreground"
            >
              跳過
            </Button>
            
            <Button
              v-if="currentStep < steps.length - 1"
              @click="nextStep"
              size="sm"
            >
              下一步
            </Button>
            
            <Button
              v-else
              @click="completeGuide"
              size="sm"
            >
              完成
            </Button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Button } from '@/components/ui/button'
import { X, Bot, Workflow, Settings, Zap } from 'lucide-vue-next'

// Props
interface GuideStep {
  title: string
  description: string
  icon?: any
  image?: string
  action?: () => void
}

interface Props {
  steps: GuideStep[]
  guideKey: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  complete: []
  step: [number]
}>()

// State
const showGuide = ref(false)
const currentStep = ref(0)

// 檢查用戶是否已完成引導
const hasCompletedGuide = computed(() => {
  return localStorage.getItem(`guide_completed_${props.guideKey}`) === 'true'
})

// 開始引導
const startGuide = () => {
  showGuide.value = true
  currentStep.value = 0
  emit('step', 0)
}

// 下一步
const nextStep = () => {
  if (currentStep.value < props.steps.length - 1) {
    currentStep.value++
    emit('step', currentStep.value)
    
    // 執行當前步驟的動作
    if (props.steps[currentStep.value].action) {
      props.steps[currentStep.value].action!()
    }
  }
}

// 上一步
const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
    emit('step', currentStep.value)
  }
}

// 跳過引導
const skipGuide = () => {
  closeGuide()
  markGuideCompleted()
}

// 完成引導
const completeGuide = () => {
  closeGuide()
  markGuideCompleted()
  emit('complete')
}

// 關閉引導
const closeGuide = () => {
  showGuide.value = false
}

// 標記引導為已完成
const markGuideCompleted = () => {
  localStorage.setItem(`guide_completed_${props.guideKey}`, 'true')
}

// 重置引導（用於開發或重新顯示）
const resetGuide = () => {
  localStorage.removeItem(`guide_completed_${props.guideKey}`)
  showGuide.value = false
  currentStep.value = 0
}

// 自動開始引導（如果未完成）
onMounted(() => {
  if (!hasCompletedGuide.value) {
    // 延遲顯示引導，讓頁面先載入
    setTimeout(() => {
      startGuide()
    }, 1000)
  }
})

// 暴露方法給父組件
defineExpose({
  startGuide,
  resetGuide,
  hasCompletedGuide
})
</script>