<template>
  <!-- 統計卡片 -->
  <div class="grid gap-3 sm:gap-4 md:gap-5 grid-cols-1 xs:grid-cols-2 sm:grid-cols-2 lg:grid-cols-4">
    <!-- 方案統計卡片模式 -->
    <template v-if="isPlanStats">
      <!-- 總方案數 -->
      <Card class="transition-all hover:shadow-lg">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-base font-medium">總方案數</CardTitle>
          <Package class="h-5 w-5 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ formatNumber(stats.totalPlans) }}</div>
          <p class="text-base text-muted-foreground mt-2">熱門方案: {{ stats.popularPlans }}</p>
        </CardContent>
      </Card>
      <!-- 平均價格 -->
      <Card class="transition-all hover:shadow-lg">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-base font-medium">平均價格</CardTitle>
          <DollarSign class="h-5 w-5 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ formatCurrency(stats.averagePrice) }}</div>
          <p class="text-base text-muted-foreground mt-2">每月訂閱費用</p>
        </CardContent>
      </Card>
      <!-- 企業版比例 -->
      <Card class="transition-all hover:shadow-lg">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-base font-medium">企業版比例</CardTitle>
          <ClipboardList class="h-5 w-5 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ enterprisePercent }}%</div>
          <p class="text-base text-muted-foreground mt-2">{{ stats.enterpriseCount > 0 ? `${stats.enterpriseCount} 個企業版方案` : '非數值 個企業版方案' }}</p>
        </CardContent>
      </Card>
      <!-- 平均儲存空間 -->
      <Card class="transition-all hover:shadow-lg">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-base font-medium">平均儲存空間</CardTitle>
          <HardDrive class="h-5 w-5 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-2xl font-bold">{{ formatStorage(stats.averageStorage) }}</div>
          <p class="text-base text-muted-foreground mt-2">每個方案平均配額</p>
        </CardContent>
      </Card>
    </template>

    <!-- 原有使用者統計卡片模式 -->
    <template v-else>
      <!-- 總使用者數卡片 -->
      <Card class="transition-all hover:shadow-lg">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">總使用者數</CardTitle>
          <Users class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatNumber(stats.totalUsers) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">
            活躍: {{ formatNumber(stats.activeUsers) }}
            ({{ stats.totalUsers > 0 ? Math.round((stats.activeUsers / stats.totalUsers) * 100) : 0 }}%)
          </p>
        </CardContent>
      </Card>

      <!-- 租戶管理員卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">租戶管理員</CardTitle>
          <ShieldCheck class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatNumber(stats.adminCount) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">
            平均 {{ stats.tenantCount > 0 ? formatNumber(Math.round(stats.adminCount / stats.tenantCount)) : 0 }} 位/租戶
          </p>
        </CardContent>
      </Card>

      <!-- 租戶數卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">租戶數</CardTitle>
          <Building2 class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatNumber(stats.tenantCount) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">
            平均 {{ stats.tenantCount > 0 ? formatNumber(Math.round(stats.totalUsers / stats.tenantCount)) : 0 }} 位使用者/租戶
          </p>
        </CardContent>
      </Card>

      <!-- 最近登入卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">最近登入</CardTitle>
          <Clock class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatNumber(stats.recentLogins) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">過去 7 天內</p>
        </CardContent>
      </Card>

      <!-- 系統健康度卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">系統健康度</CardTitle>
          <Activity class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ stats.systemHealth }}%</div>
          <div class="mt-2">
            <Progress :value="stats.systemHealth" class="h-1.5" 
                      :class="getHealthColorClass(stats.systemHealth)" />
          </div>
          <div v-if="stats.systemHealth < 30" class="text-destructive text-xs mt-2 flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" class="inline h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            {{ stats.systemHealthReason || '系統健康度過低，請檢查伺服器或服務狀態' }}
          </div>
        </CardContent>
      </Card>

      <!-- 月度營收卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">本月營收</CardTitle>
          <DollarSign class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatCurrency(stats.monthlyRevenue) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">
            <span :class="stats.revenueGrowth > 0 ? 'text-emerald-500' : 'text-destructive'">
              {{ stats.revenueGrowth > 0 ? '+' : '' }}{{ stats.revenueGrowth }}%
            </span>
            較上月
          </p>
        </CardContent>
      </Card>

      <!-- 儲存空間卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">儲存空間</CardTitle>
          <HardDrive class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatStorage(stats.storageUsed) }}</div>
          <div class="mt-2">
            <Progress :value="Math.min((stats.storageUsed / stats.storageLimit) * 100, 100)" class="h-1.5" />
            <p class="text-[10px] sm:text-xs text-muted-foreground mt-1">
              {{ Math.round((stats.storageUsed / stats.storageLimit) * 100) }}% 已使用
            </p>
            <div v-if="stats.storageLimit > 0 && (stats.storageUsed / stats.storageLimit) >= 0.9" class="text-destructive text-xs mt-2 flex items-center gap-1">
              <svg xmlns="http://www.w3.org/2000/svg" class="inline h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
              {{ stats.storageReason || '儲存空間即將用盡，請盡快擴容或清理資料' }}
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- API 呼叫卡片 -->
      <Card class="hover:shadow-lg transition-shadow">
        <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-1 sm:pb-2">
          <CardTitle class="text-xs sm:text-sm font-medium">API 呼叫</CardTitle>
          <Terminal class="h-3 w-3 sm:h-4 sm:w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div class="text-lg sm:text-2xl font-bold">{{ formatNumber(stats.apiCalls) }}</div>
          <p class="text-[10px] sm:text-xs text-muted-foreground">
            每分鐘平均呼叫次數
          </p>
          <div v-if="stats.apiCallsLimit && stats.apiCallsLimit > 0 && stats.apiCalls / stats.apiCallsLimit >= 0.9" class="text-destructive text-xs mt-2 flex items-center gap-1">
            <svg xmlns="http://www.w3.org/2000/svg" class="inline h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
            {{ stats.apiCallsReason || 'API 呼叫量接近上限，請注意流量或聯絡技術支援' }}
          </div>
        </CardContent>
      </Card>
    </template>
  </div>
</template>

<script setup lang="ts">
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
  Users,
  Building2,
  ShieldCheck,
  Clock,
  Activity,
  Terminal,
  DollarSign,
  HardDrive,
  Package,
  ClipboardList
} from 'lucide-vue-next'
import { computed } from 'vue'

defineOptions({
  name: 'StatsCards'
})

const props = defineProps<{
  stats: any
}>()

// 判斷是否為方案統計卡片模式
const isPlanStats = computed(() =>
  props.stats &&
  'totalPlans' in props.stats &&
  'popularPlans' in props.stats &&
  'averagePrice' in props.stats &&
  'enterpriseCount' in props.stats &&
  'averageStorage' in props.stats
)

// 企業版比例
const enterprisePercent = computed(() => {
  if (!props.stats.totalPlans || props.stats.totalPlans === 0) return 0
  return Math.round((props.stats.enterpriseCount / props.stats.totalPlans) * 100)
})

import { formatNumber, formatCurrency, formatStorage } from '@/utils/formatting.utils'

// 取得健康度顏色類別
const getHealthColorClass = (health: number) => {
  if (health >= 90) return 'bg-emerald-500'
  if (health >= 70) return 'bg-amber-500'
  return 'bg-destructive'
}
</script>