<template>
  <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">總訂單數</CardTitle>
        <Receipt class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatNumber(stats.totalOrders) }}</div>
          <p class="text-xs text-muted-foreground">
            已完成: {{ formatNumber(stats.completedOrders) }}
            ({{ stats.totalOrders > 0 ? Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0 }}%)
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">本月訂單</CardTitle>
        <CalendarClock class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatNumber(stats.monthlyOrders) }}</div>
          <p class="text-xs text-muted-foreground">
            較上月 {{ stats.monthlyGrowth > 0 ? '+' : '' }}{{ stats.monthlyGrowth }}%
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">總收入</CardTitle>
        <DollarSign class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">{{ formatCurrency(stats.totalRevenue) }}</div>
          <p class="text-xs text-muted-foreground">
            平均 {{ formatCurrency(stats.averageOrderValue) }}/訂單
          </p>
        </div>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">訂單完成率</CardTitle>
        <PieChart class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="flex flex-col space-y-1">
          <div class="text-2xl font-bold">
            {{ stats.totalOrders > 0 ? Math.round((stats.completedOrders / stats.totalOrders) * 100) : 0 }}%
          </div>
          <p class="text-xs text-muted-foreground">
            待處理: {{ formatNumber(stats.pendingOrders) }} 筆
          </p>
        </div>
      </CardContent>
    </Card>
  </div>
</template>

<script setup lang="ts">
import { Receipt, CalendarClock, DollarSign, PieChart } from 'lucide-vue-next'
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'

interface OrderStats {
  totalOrders: number
  completedOrders: number
  monthlyOrders: number
  monthlyGrowth: number
  totalRevenue: number
  averageOrderValue: number
  pendingOrders: number
}

const props = defineProps<{
  stats: OrderStats
}>()

import { formatNumber, formatCurrency, formatPercent } from '@/utils/formatting.utils'
</script> 