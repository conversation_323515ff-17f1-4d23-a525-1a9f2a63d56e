<!-- 方案統計卡片元件 -->
<script setup lang="ts">
import { Package, DollarSign, Building2, HardDrive } from 'lucide-vue-next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { formatCurrency, formatNumber, formatStorage } from '@/utils/formatting.utils'

interface Props {
  stats: {
    totalPlans: number
    popularPlans: number
    enterpriseCount: number
    averagePrice: number
    averageStorage: number
  }
}

defineProps<Props>()
</script>

<template>
  <div class="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">總方案數</CardTitle>
        <Package class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold">{{ formatNumber(stats.totalPlans) }}</div>
        <p class="text-xs text-muted-foreground">
          熱門方案: {{ formatNumber(stats.popularPlans) }}
        </p>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">平均價格</CardTitle>
        <DollarSign class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold">{{ formatCurrency(stats.averagePrice) }}</div>
        <p class="text-xs text-muted-foreground">每月訂閱費用</p>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">企業版比例</CardTitle>
        <Building2 class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold">
          {{ Math.round((stats.enterpriseCount / stats.totalPlans) * 100) || 0 }}%
        </div>
        <p class="text-xs text-muted-foreground">
          {{ formatNumber(stats.enterpriseCount) }} 個企業版方案
        </p>
      </CardContent>
    </Card>

    <Card class="hover:shadow-lg transition-shadow">
      <CardHeader class="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle class="text-sm font-medium">平均儲存空間</CardTitle>
        <HardDrive class="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div class="text-2xl font-bold">{{ formatStorage(stats.averageStorage) }}</div>
        <p class="text-xs text-muted-foreground">每個方案平均配額</p>
      </CardContent>
    </Card>
  </div>
</template> 