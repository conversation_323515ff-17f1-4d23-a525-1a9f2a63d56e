<!-- 方案列表元件 -->
<script setup lang="ts">
import type { IPlan, PlanFeature } from '@/types/models/plan.model';
import { Package, Pencil, Trash2, Check, Users, Folders, HardDrive } from 'lucide-vue-next';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { formatCurrency, formatNumber, formatStorage } from '@/utils/formatting.utils';

interface Props {
  plans: IPlan[];
}

interface Emits {
  (e: 'edit', plan: IPlan): void;
  (e: 'delete', plan: IPlan): void;
  (e: 'copy', plan: IPlan): void;
}

const props = defineProps<Props>();
const emit = defineEmits<Emits>();

// 解析功能列表
const parsedFeatures = (features: any): PlanFeature[] => {
  try {
    if (!features) return [];

    // 如果是字串，嘗試解析 JSON
    if (typeof features === 'string') {
      try {
        const parsed = JSON.parse(features);
        if (Array.isArray(parsed)) {
          return parsed.map((item, index) => ({
            id: typeof item === 'string' ? `feature-${index}` : item.id || `feature-${index}`,
            name: typeof item === 'string' ? item : item.name || '',
            description: typeof item === 'string' ? '' : item.description || '',
            included: true,
          }));
        }
      } catch (e) {
        console.error('JSON 解析錯誤:', e);
        return [];
      }
    }

    // 如果是陣列，直接處理
    if (Array.isArray(features)) {
      return features.map((item, index) => ({
        id: typeof item === 'string' ? `feature-${index}` : item.id || `feature-${index}`,
        name: typeof item === 'string' ? item : item.name || '',
        description: typeof item === 'string' ? '' : item.description || '',
        included: true,
      }));
    }

    return [];
  } catch (error) {
    console.error('功能解析錯誤:', error);
    return [];
  }
};
</script>

<template>
  <div v-if="plans.length > 0" class="grid gap-6">
    <Card
      v-for="plan in plans"
      :key="plan.id"
      class="relative overflow-hidden rounded-2xl border border-zinc-100 dark:border-zinc-800 shadow-sm hover:shadow-md transition-all duration-300"
    >
      <CardContent class="p-4 sm:p-6 lg:p-8">
        <!-- 操作按鈕 - 右上角 -->
        <div
          class="absolute right-2 sm:right-4 lg:right-6 top-2 sm:top-4 lg:top-6 flex space-x-1.5 sm:space-x-2 z-10"
        >
          <Button
            variant="ghost"
            size="icon"
            class="h-8 w-8 sm:h-9 sm:w-9 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800"
            @click="emit('edit', plan)"
          >
            <Pencil class="h-3.5 w-3.5 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            class="h-8 w-8 sm:h-9 sm:w-9 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800"
            @click="emit('copy', plan)"
          >
            <Package class="h-3.5 w-3.5 sm:h-4 sm:w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            class="h-8 w-8 sm:h-9 sm:w-9 rounded-full hover:bg-zinc-100 dark:hover:bg-zinc-800 text-destructive"
            @click="emit('delete', plan)"
          >
            <Trash2 class="h-3.5 w-3.5 sm:h-4 sm:w-4" />
          </Button>
        </div>

        <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 lg:gap-10">
          <!-- 左側：標題與價格 -->
          <div class="lg:col-span-4 space-y-4 sm:space-y-6">
            <!-- 標題區域 -->
            <div class="space-y-3">
              <Badge
                v-if="plan.is_popular"
                variant="default"
                class="px-2 py-0.5 text-xs font-medium rounded-full shrink-0 mb-1 inline-block"
              >
                熱門方案
              </Badge>
              <h2 class="text-xl sm:text-2xl font-medium tracking-tight break-words pr-16 truncate">
                {{ plan.name }}
              </h2>
              <p class="text-muted-foreground text-sm line-clamp-2">{{ plan.description }}</p>
            </div>

            <div class="pt-2">
              <div class="flex items-baseline">
                <span class="text-2xl sm:text-3xl font-medium tracking-tight">{{
                  formatCurrency(plan.price)
                }}</span>
                <span class="text-sm text-muted-foreground ml-2">/ 每月</span>
              </div>
            </div>
          </div>

          <!-- 分隔線 (僅在行動版顯示) -->
          <Separator class="lg:hidden my-2" />

          <!-- 中間：資源指標 -->
          <div
            class="lg:col-span-4 flex flex-col justify-center lg:border-l lg:border-r border-zinc-100 dark:border-zinc-800 lg:px-8"
          >
            <div class="grid grid-cols-3 lg:grid-cols-1 gap-4 lg:gap-6">
              <div class="space-y-2">
                <div class="flex items-center space-x-2 sm:space-x-3">
                  <Users class="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
                  <span class="text-xs sm:text-sm text-muted-foreground">使用者數上限</span>
                </div>
                <p class="text-lg sm:text-2xl font-medium pl-6 sm:pl-8">
                  {{ formatNumber(plan.max_users) }}
                </p>
              </div>

              <div class="space-y-2">
                <div class="flex items-center space-x-2 sm:space-x-3">
                  <Folders class="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
                  <span class="text-xs sm:text-sm text-muted-foreground">專案數上限</span>
                </div>
                <p class="text-lg sm:text-2xl font-medium pl-6 sm:pl-8">
                  {{ formatNumber(plan.max_projects) }}
                </p>
              </div>

              <div class="space-y-2">
                <div class="flex items-center space-x-2 sm:space-x-3">
                  <HardDrive class="h-4 w-4 sm:h-5 sm:w-5 text-muted-foreground" />
                  <span class="text-xs sm:text-sm text-muted-foreground">儲存空間</span>
                </div>
                <p class="text-lg sm:text-2xl font-medium pl-6 sm:pl-8">
                  {{ formatStorage(plan.max_storage) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 分隔線 (僅在行動版顯示) -->
          <Separator class="lg:hidden my-2" />

          <!-- 右側：功能列表 -->
          <div class="lg:col-span-4">
            <div class="flex items-center mb-4 sm:mb-6">
              <h3 class="text-base sm:text-lg font-medium tracking-tight">包含功能</h3>
              <Badge variant="outline" class="text-xs rounded-full ml-3">
                {{ parsedFeatures(plan.features)?.length || 0 }} 項功能
              </Badge>
            </div>

            <ScrollArea class="h-[140px] sm:h-[180px] pr-4">
              <div class="space-y-2 sm:space-y-3">
                <div
                  v-for="feature in parsedFeatures(plan.features)"
                  :key="feature.id"
                  class="flex items-center space-x-3 py-1"
                >
                  <Check class="h-3.5 w-3.5 sm:h-4 sm:w-4 text-primary flex-shrink-0" />
                  <span class="text-xs sm:text-sm">{{ feature.name }}</span>
                </div>

                <!-- 無功能時的顯示 -->
                <div
                  v-if="!parsedFeatures(plan.features)?.length"
                  class="flex flex-col items-center justify-center py-6 sm:py-8 text-center text-muted-foreground"
                >
                  <Package class="h-6 w-6 sm:h-7 sm:w-7 mb-2 opacity-60" />
                  <p class="text-xs sm:text-sm">尚未設定功能</p>
                </div>
              </div>
            </ScrollArea>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>

  <!-- 無資料顯示 -->
  <div v-else class="text-center py-12 sm:py-16 border border-dashed rounded-lg">
    <div class="space-y-4">
      <Package class="w-8 h-8 sm:w-10 sm:h-10 text-muted-foreground mx-auto opacity-60" />
      <h3 class="text-base sm:text-lg font-medium">找不到符合條件的方案</h3>
      <p class="text-muted-foreground max-w-xs mx-auto text-xs sm:text-sm">
        請嘗試調整搜尋關鍵字或清除篩選條件。
      </p>
    </div>
  </div>
</template>
