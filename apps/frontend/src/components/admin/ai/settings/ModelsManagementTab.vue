<script setup lang="ts">
// 定義 emits
const emit = defineEmits<{
  openNewModel: [void];
}>();

import { onMounted, ref, computed, watch } from 'vue';
import { useLocalStorage } from '@vueuse/core';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableHead,
  TableRow,
  TableCell,
  TableBody,
  TableHeader,
} from '@/components/ui/table';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Loader2,
  Filter,
  Plus,
  X,
  Check,
  Trash2,
  Power,
  PowerOff,
  Edit2,
  MoveRight,
  MoveLeft,
  Search,
  RefreshCw,
  ArrowUpDown,
  ArrowUp,
  ArrowDown,
  LayoutGrid,
  List,
  CheckCircle,
  XCircle,
  CheckSquare,
} from 'lucide-vue-next';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { ScrollArea } from '@/components/ui/scroll-area';
import { format, parseISO } from 'date-fns';
import { zhTW } from 'date-fns/locale';
import { useAIModelManager } from '@/composables/admin/ai/models/useAIModelManager';
import { useModelsTableSorting } from '@/composables/admin/ai/models/useModelsTableSorting';
import { useUtils } from '@/composables/shared/useUtils';
import { AIModel, AiKey } from '@/types/models/ai.model';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { useNotification } from '@/composables/shared/useNotification';
import { AiModelsService } from '@/services/admin/ai/ai-models.service';
import { useAIValidation } from '@/composables/admin/ai/shared/useAIValidation';
import { useAIKeyManager } from '@/composables/admin/ai/keys/useAIKeyManager';
import KeyRequiredGuide from '@/components/admin/ai/shared/KeyRequiredGuide.vue';

// 從 API 獲取的 syncPricing 回應型別
interface SyncPricingResponse {
  success: boolean;
  updated: number;
  errors?: Array<{
    provider: string;
    error: string;
  }>;
}

// 從 API 獲取的 fetchFromProviders 回應型別
interface FetchProvidersResponse {
  results: Array<{
    provider: string;
    created: number;
    existed: number;
    failed?: boolean;
    error?: string;
  }>;
}

// 使用工具 composable
const { formatDate: utilFormatDate } = useUtils();

// 格式化日期 (保持原有格式)
const formatDate = (dateString: string) => {
  if (!dateString) return '—';
  try {
    return format(parseISO(dateString), 'yyyy/MM/dd HH:mm', { locale: zhTW });
  } catch (e) {
    return '無效日期';
  }
};

// 使用 AIModelManager composable
const {
  isLoading,
  isSaving,
  modelList,
  filteredModels,
  editingId,
  tempEditValues,
  searchTerm,
  selectedProvider,
  newModel,
  showDeleteAlert,
  modelToDelete,
  showBatchDeleteAlert,
  availableSearch,
  enabledSearch,
  availableModels,
  enabledModels,
  availableSelectedModels,
  enabledSelectedModels,
  isAllAvailableSelected,
  isSomeAvailableSelected,
  isAllEnabledSelected,
  isSomeEnabledSelected,
  initialize,
  handleAdd,
  handleSaveNew,
  handleCancelNew,
  handleFieldUpdate,
  confirmDelete,
  handleDelete,
  toggleAvailableSelectModel,
  toggleEnabledSelectModel,
  toggleAvailableSelectAll,
  toggleEnabledSelectAll,
  moveToEnabled,
  moveToAvailable,
  moveAllToEnabled,
  moveAllToAvailable,
  startEditing,
  cancelEditing,
  saveEditing,
  triggerFetchFromProvidersAndRefreshList,
  triggerAutoSyncModelPricing,
  selectedModels,
  isAllSelected,
  isSomeSelected,
  toggleSelectModel,
  handleSelectAllToggle,
  clearSelection,
  batchEnableModels,
  batchDisableModels,
} = useAIModelManager();

// 使用排序 composable
const {
  toggleAvailableSort,
  toggleEnabledSort,
  createSortedAvailableModels,
  createSortedEnabledModels,
  getSortIndicator,
  availableSortField,
  availableSortDirection,
  enabledSortField,
  enabledSortDirection,
} = useModelsTableSorting();

// 使用排序 composable 建立排序後的模型列表
const sortedAvailableModels = createSortedAvailableModels(availableModels);
const sortedEnabledModels = createSortedEnabledModels(enabledModels);

// 新增載入中狀態
const isRefreshing = ref(false);
const isAutoSyncingPrice = ref(false);
const showLoadingDialog = computed(() => isLoading.value);
const notification = useNotification();

// KeyRequiredGuide 顯示狀態
const isGuideVisible = ref(true);

// 新 UI 的狀態變數
const modelStatusFilter = ref('all');

// 計算篩選後的模型
const computedFilteredModels = computed(() => {
  let models = filteredModels.value;

  // 狀態篩選
  if (modelStatusFilter.value === 'enabled') {
    models = models.filter((model: AIModel) => model.is_enabled);
  } else if (modelStatusFilter.value === 'disabled') {
    models = models.filter((model: AIModel) => !model.is_enabled);
  }

  // 搜尋詞篩選
  if (searchTerm.value) {
    const searchTermLower = searchTerm.value.toLowerCase();
    models = models.filter(
      (model: AIModel) =>
        model.model_name.toLowerCase().includes(searchTermLower) ||
        (model.display_name && model.display_name.toLowerCase().includes(searchTermLower)) ||
        model.provider.toLowerCase().includes(searchTermLower),
    );
  }

  return models;
});

// 修改 viewMode 為使用 localStorage 記憶
const viewMode = useLocalStorage<'grid' | 'list'>('ai-model-view-mode', 'grid');

// 在 refs 區域增加排序狀態
const sortField = ref<string | null>(null);
const sortDirection = ref<'asc' | 'desc'>('asc');

// 千分位格式化函數
const formatNumber = (num: number | undefined | null): string => {
  if (num === undefined || num === null) return '—';
  return num.toLocaleString();
};

// 修改 sortedFilteredModels 計算屬性，增加排序功能
const sortedFilteredModels = computed(() => {
  // 先按啟用狀態排序（已啟用的排前面）
  let sorted = [...computedFilteredModels.value].sort((a, b) => {
    if (a.is_enabled !== b.is_enabled) {
      return a.is_enabled ? -1 : 1;
    }
    // 然後按名稱字母順序排序
    return (a.display_name || a.model_name).localeCompare(b.display_name || b.model_name);
  });

  // 如果有指定排序欄位，則進一步排序
  if (sortField.value) {
    sorted = sorted.sort((a, b) => {
      let aValue, bValue;

      // 根據不同欄位取得對應值
      switch (sortField.value) {
        case 'provider':
          aValue = a.provider;
          bValue = b.provider;
          break;
        case 'inputPrice':
          aValue = a.input_price_per_1k_tokens;
          bValue = b.input_price_per_1k_tokens;
          break;
        case 'outputPrice':
          aValue = a.output_price_per_1k_tokens;
          bValue = b.output_price_per_1k_tokens;
          break;
        case 'contextWindow':
          aValue = a.context_window_tokens || 0;
          bValue = b.context_window_tokens || 0;
          break;
        case 'lastUpdated':
          aValue = a.price_last_updated_at ? new Date(a.price_last_updated_at).getTime() : 0;
          bValue = b.price_last_updated_at ? new Date(b.price_last_updated_at).getTime() : 0;
          break;
        default:
          return 0;
      }

      // 排序方向
      const direction = sortDirection.value === 'asc' ? 1 : -1;

      // 數值比較
      if (typeof aValue === 'number' && typeof bValue === 'number') {
        return (aValue - bValue) * direction;
      }

      // 字串比較
      if (typeof aValue === 'string' && typeof bValue === 'string') {
        return aValue.localeCompare(bValue) * direction;
      }

      return 0;
    });
  }

  return sorted;
});

// 排序處理函數
const toggleSort = (field: string) => {
  if (sortField.value === field) {
    // 切換排序方向
    sortDirection.value = sortDirection.value === 'asc' ? 'desc' : 'asc';
  } else {
    // 設定新的排序欄位和預設方向
    sortField.value = field;
    sortDirection.value = 'asc';
  }
};

// 獲取排序圖標
const getSortIcon = (field: string) => {
  if (sortField.value !== field) return 'none';
  return sortDirection.value === 'asc' ? 'asc' : 'desc';
};

// AI 金鑰管理和驗證
const { availableKeys, initialize: initializeKeys } = useAIKeyManager(() => {});
const { hasAnyValidKey, validateAIPrerequisites, showKeySetupNotification, handleAPIError } =
  useAIValidation(availableKeys);

// 監控金鑰狀態變化，重新顯示引導
watch(hasAnyValidKey, (newVal, oldVal) => {
  // 當從有金鑰變為沒有金鑰時，重新顯示引導
  if (oldVal === true && newVal === false) {
    isGuideVisible.value = true;
  }
});

// 包裝原有的同步函數以加入金鑰驗證
const handleSyncModelsWithKeyValidation = async () => {
  // 檢查是否有有效的 API 金鑰
  if (!validateAIPrerequisites(false, true)) {
    return;
  }

  try {
    await triggerFetchFromProvidersAndRefreshList();
  } catch (error) {
    console.error('模型同步失敗:', error);
    handleAPIError(error, {
      operation: 'sync-models',
      provider: selectedProvider.value !== 'all' ? selectedProvider.value : undefined,
    });
  }
};

// 包裝原有的價格同步函數以加入金鑰驗證
const handleSyncPricingWithKeyValidation = async () => {
  // 檢查是否有有效的 API 金鑰
  if (!validateAIPrerequisites(false, true)) {
    return;
  }

  try {
    await triggerAutoSyncModelPricing();
  } catch (error) {
    console.error('價格同步失敗:', error);
    handleAPIError(error, {
      operation: 'sync-pricing',
      provider: selectedProvider.value !== 'all' ? selectedProvider.value : undefined,
    });
  }
};

onMounted(() => {
  initialize();
  initializeKeys();
});
</script>

<template>
  <div>
    <!-- 金鑰驗證檢查 -->
    <div v-if="!hasAnyValidKey && isGuideVisible" class="mb-4">
      <KeyRequiredGuide
        variant="inline"
        :show-icon="true"
        :show-status="false"
        :show-help="true"
        :show-close="true"
        title="AI 模型管理需要 API 金鑰"
        description="模型同步、價格更新等功能需要有效的 AI API 金鑰支援。設定完成後即可開始管理您的 AI 模型。"
        primary-button-text="前往金鑰設定"
        secondary-button-text="查看設定指南"
        :available-key-count="availableKeys?.length || 0"
        :available-providers="availableKeys?.map((key) => key.provider) || []"
        redirect-tab="keys"
        @close="isGuideVisible = false"
      />
    </div>

    <div class="flex flex-row items-center justify-between mb-4">
      <div>
        <h3 class="text-lg font-medium">AI 模型管理</h3>
        <p class="text-sm text-muted-foreground">管理 AI 模型，設定其可用狀態與定價資訊。</p>
      </div>
      <div class="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          @click="handleSyncModelsWithKeyValidation"
          :disabled="isSaving || isLoading || !hasAnyValidKey"
          class="text-muted-foreground hover:text-foreground hover:bg-muted/50"
        >
          <RefreshCw class="h-4 w-4 mr-2" />同步模型與價格
        </Button>
      </div>
    </div>

    <!-- 篩選控制區 -->
    <div class="bg-muted/10 rounded-lg p-4 border shadow-sm mb-4">
      <div class="flex flex-col md:flex-row gap-4 w-full">
        <div class="flex-1">
          <Label class="text-sm mb-2 block">供應商篩選</Label>
          <Select v-model="selectedProvider" class="w-full">
            <SelectTrigger>
              <SelectValue placeholder="選擇供應商" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部供應商</SelectItem>
              <SelectItem value="openai">OpenAI</SelectItem>
              <SelectItem value="anthropic">Anthropic</SelectItem>
              <SelectItem value="google-gemini">Google Gemini</SelectItem>
              <SelectItem value="openai-compatible">OpenAI 相容</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div class="flex-1">
          <Label class="text-sm mb-2 block">模型狀態</Label>
          <Select v-model="modelStatusFilter" class="w-full">
            <SelectTrigger>
              <SelectValue placeholder="模型狀態" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">全部狀態</SelectItem>
              <SelectItem value="enabled">已啟用</SelectItem>
              <SelectItem value="disabled">未啟用</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div class="flex-[2]">
          <Label class="text-sm mb-2 block">搜尋模型</Label>
          <div class="relative">
            <Search
              class="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground"
            />
            <Input v-model="searchTerm" placeholder="搜尋模型名稱、供應商..." class="pl-8 w-full" />
            <button
              v-if="searchTerm"
              @click="searchTerm = ''"
              class="absolute right-2 top-1/2 transform -translate-y-1/2 text-muted-foreground hover:text-foreground"
            >
              <X class="h-4 w-4" />
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 載入指示器 -->
    <div
      v-if="showLoadingDialog"
      class="absolute inset-0 z-50 flex items-center justify-center"
      style="
        background: linear-gradient(
          135deg,
          rgba(243, 244, 246, 0.9) 0%,
          rgba(224, 231, 255, 0.6) 100%
        );
      "
    >
      <div class="flex flex-col items-center justify-center gap-4 py-8">
        <Loader2 class="h-8 w-8 animate-spin text-primary" />
      </div>
    </div>

    <!-- 模型顯示區域 -->
    <div v-if="computedFilteredModels.length > 0">
      <!-- 統一工具列：全選、批量操作和視圖模式 -->
      <div
        class="flex items-center justify-between py-3 px-1 mb-3 border-b transition-all duration-200"
        :class="selectedModels.length > 0 ? 'border-primary/20 bg-primary/5' : ''"
      >
        <div class="flex items-center gap-3">
          <!-- 全選按鍵 - 只在沒有選擇時顯示 -->
          <Button
            v-if="selectedModels.length === 0"
            variant="ghost"
            size="sm"
            @click="handleSelectAllToggle(true)"
            class="text-sm font-medium text-muted-foreground hover:text-foreground hover:bg-muted/50"
          >
            <CheckSquare class="h-4 w-4 mr-2" />
            全選
            <span class="text-muted-foreground ml-1 text-xs">
              ({{ sortedFilteredModels.length }})
            </span>
          </Button>

          <!-- 選擇狀態和清除按鈕 -->
          <div v-if="selectedModels.length > 0" class="flex items-center gap-3">
            <Badge variant="secondary" class="text-xs px-2.5 py-1 font-medium">
              {{ selectedModels.length }}/{{ sortedFilteredModels.length }} 已選
            </Badge>

            <Button
              variant="ghost"
              size="sm"
              @click="clearSelection"
              class="text-muted-foreground hover:text-foreground hover:bg-muted/50"
            >
              <X class="h-4 w-4 mr-1.5" />
              取消選擇
            </Button>
          </div>
        </div>

        <div class="flex items-center gap-2">
          <!-- 批量操作按鈕 - 只在有選擇時顯示 -->
          <template v-if="selectedModels.length > 0">
            <Button
              variant="outline"
              size="sm"
              @click="batchEnableModels"
              :disabled="isSaving"
              class="text-green-700 hover:text-green-800 hover:bg-green-50 border-green-200 hover:border-green-300"
            >
              <Power class="w-4 h-4 mr-1.5" />
              啟用
            </Button>

            <Button
              variant="outline"
              size="sm"
              @click="batchDisableModels"
              :disabled="isSaving"
              class="text-orange-600 hover:text-orange-700 hover:bg-orange-50 border-orange-200 hover:border-orange-300"
            >
              <PowerOff class="w-4 h-4 mr-1.5" />
              停用
            </Button>

            <div class="h-4 w-px bg-border mx-2"></div>
          </template>

          <!-- 視圖模式按鈕 -->
          <div class="flex items-center border rounded-md p-0.5 bg-muted/20">
            <Button
              variant="ghost"
              size="sm"
              @click="viewMode = 'grid'"
              :class="[
                'h-7 px-2 rounded-sm',
                viewMode === 'grid'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground',
              ]"
            >
              <LayoutGrid class="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              @click="viewMode = 'list'"
              :class="[
                'h-7 px-2 rounded-sm',
                viewMode === 'list'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground',
              ]"
            >
              <List class="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      <!-- 網格顯示模式 -->
      <div
        v-if="viewMode === 'grid'"
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-4"
      >
        <div
          v-for="model in sortedFilteredModels"
          :key="model.id"
          :class="[
            'relative border rounded-lg overflow-hidden shadow-sm transition-all hover:shadow cursor-pointer',
            model.is_enabled ? 'bg-white' : 'bg-muted/20',
            selectedModels.includes(model.id) ? 'ring-2 ring-primary bg-primary/5' : '',
          ]"
          @click="toggleSelectModel(model.id)"
        >
          <!-- 模型卡片內容 -->
          <div class="p-4">
            <!-- 狀態標記 -->
            <div class="absolute top-3 right-3 flex gap-1.5">
              <Badge :variant="model.is_enabled ? 'default' : 'outline'" class="text-xs">
                {{ model.is_enabled ? '已啟用' : '未啟用' }}
              </Badge>
            </div>

            <!-- 標題區 -->
            <div class="mt-2 mb-2">
              <h4 class="font-medium text-base truncate">
                {{ model.display_name || model.model_name }}
              </h4>
              <div class="flex items-center text-sm text-muted-foreground">
                <Badge variant="outline" class="mr-2">{{ model.provider }}</Badge>
                <p class="text-xs truncate">{{ model.model_name }}</p>
              </div>
            </div>

            <!-- 核心資訊 -->
            <div class="grid grid-cols-2 gap-x-4 gap-y-2 mt-4 text-sm">
              <div>
                <p class="text-muted-foreground text-xs">輸入價格</p>
                <p>${{ model.input_price_per_1k_tokens }}/1k tokens</p>
              </div>

              <div>
                <p class="text-muted-foreground text-xs">輸出價格</p>
                <p>${{ model.output_price_per_1k_tokens }}/1k tokens</p>
              </div>

              <div>
                <p class="text-muted-foreground text-xs">上下文窗口</p>
                <p>{{ formatNumber(model.context_window_tokens) }}</p>
              </div>

              <div>
                <p class="text-muted-foreground text-xs">價格更新</p>
                <p class="truncate">
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger class="underline decoration-dotted">
                        {{
                          model.price_last_updated_at
                            ? formatDate(model.price_last_updated_at)
                            : '—'
                        }}
                      </TooltipTrigger>
                      <TooltipContent v-if="model.price_last_updated_at">
                        <p>
                          價格最後更新於
                          {{ formatDate(model.price_last_updated_at) }}
                        </p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </p>
              </div>
            </div>
          </div>

          <!-- 操作按鈕 -->
          <div class="flex border-t divide-x">
            <Button
              variant="ghost"
              class="flex-1 rounded-none h-10 text-xs font-normal hover:bg-muted/20"
              @click="startEditing(model)"
              :disabled="!!editingId"
            >
              <Edit2 class="h-3.5 w-3.5 mr-1.5" /> 編輯
            </Button>

            <Button
              variant="ghost"
              class="flex-1 rounded-none h-10 text-xs font-normal hover:bg-destructive/10 hover:text-destructive"
              @click="confirmDelete(model.id)"
            >
              <Trash2 class="h-3.5 w-3.5 mr-1.5" /> 刪除
            </Button>
          </div>
        </div>
      </div>

      <!-- 列表顯示模式 -->
      <div v-else class="rounded-md border mt-4">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>模型</TableHead>
              <TableHead>
                <div class="flex items-center cursor-pointer" @click="toggleSort('provider')">
                  供應商
                  <ArrowUp v-if="getSortIcon('provider') === 'asc'" class="ml-1 h-3 w-3" />
                  <ArrowDown v-if="getSortIcon('provider') === 'desc'" class="ml-1 h-3 w-3" />
                </div>
              </TableHead>
              <TableHead>
                <div class="flex items-center cursor-pointer" @click="toggleSort('inputPrice')">
                  輸入價格
                  <ArrowUp v-if="getSortIcon('inputPrice') === 'asc'" class="ml-1 h-3 w-3" />
                  <ArrowDown v-if="getSortIcon('inputPrice') === 'desc'" class="ml-1 h-3 w-3" />
                </div>
              </TableHead>
              <TableHead>
                <div class="flex items-center cursor-pointer" @click="toggleSort('outputPrice')">
                  輸出價格
                  <ArrowUp v-if="getSortIcon('outputPrice') === 'asc'" class="ml-1 h-3 w-3" />
                  <ArrowDown v-if="getSortIcon('outputPrice') === 'desc'" class="ml-1 h-3 w-3">
                  </ArrowDown>
                </div>
              </TableHead>
              <TableHead>
                <div class="flex items-center cursor-pointer" @click="toggleSort('contextWindow')">
                  上下文窗口
                  <ArrowUp v-if="getSortIcon('contextWindow') === 'asc'" class="ml-1 h-3 w-3" />
                  <ArrowDown v-if="getSortIcon('contextWindow') === 'desc'" class="ml-1 h-3 w-3" />
                </div>
              </TableHead>
              <TableHead>
                <div class="flex items-center cursor-pointer" @click="toggleSort('lastUpdated')">
                  最後更新
                  <ArrowUp v-if="getSortIcon('lastUpdated') === 'asc'" class="ml-1 h-3 w-3" />
                  <ArrowDown v-if="getSortIcon('lastUpdated') === 'desc'" class="ml-1 h-3 w-3" />
                </div>
              </TableHead>
              <TableHead class="text-right">操作</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            <TableRow
              v-for="model in sortedFilteredModels"
              :key="model.id"
              :class="['cursor-pointer', selectedModels.includes(model.id) ? 'bg-primary/5' : '']"
              @click="toggleSelectModel(model.id)"
            >
              <TableCell>
                <div class="flex items-center space-x-2">
                  <Badge v-if="model.is_enabled" variant="default" class="mr-2">啟用</Badge>
                  <Badge v-else variant="outline" class="mr-2">未啟用</Badge>
                  <div>
                    <div class="font-medium">
                      {{ model.display_name || model.model_name }}
                    </div>
                    <div class="text-xs text-muted-foreground">
                      {{ model.model_name }}
                    </div>
                  </div>
                </div>
              </TableCell>
              <TableCell>{{ model.provider }}</TableCell>
              <TableCell>${{ model.input_price_per_1k_tokens }}</TableCell>
              <TableCell>${{ model.output_price_per_1k_tokens }}</TableCell>
              <TableCell>{{ formatNumber(model.context_window_tokens) }}</TableCell>
              <TableCell>
                {{ model.price_last_updated_at ? formatDate(model.price_last_updated_at) : '—' }}
              </TableCell>
              <TableCell class="text-right space-x-1">
                <Button
                  variant="ghost"
                  size="icon"
                  @click="startEditing(model)"
                  :disabled="!!editingId"
                  title="編輯模型"
                >
                  <Edit2 class="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  @click="handleFieldUpdate(model, 'is_enabled', !model.is_enabled)"
                  :disabled="isSaving !== null"
                  :title="model.is_enabled ? '停用模型' : '啟用模型'"
                >
                  <Power v-if="!model.is_enabled" class="h-4 w-4" />
                  <PowerOff v-else class="h-4 w-4" />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  @click="confirmDelete(model.id)"
                  title="刪除模型"
                >
                  <Trash2 class="h-4 w-4" />
                </Button>
              </TableCell>
            </TableRow>
          </TableBody>
        </Table>
      </div>
    </div>

    <!-- 空狀態 -->
    <div
      v-else
      class="flex flex-col items-center justify-center text-center p-8 border rounded-lg bg-muted/10 mt-4"
    >
      <div class="h-12 w-12 rounded-full bg-muted flex items-center justify-center mb-3">
        <Search class="h-6 w-6 text-muted-foreground" />
      </div>
      <h3 class="text-lg font-medium mb-1">找不到符合條件的模型</h3>
      <p class="text-muted-foreground">
        {{
          searchTerm
            ? '請嘗試其他搜尋條件'
            : modelStatusFilter !== 'all'
              ? '請嘗試變更狀態篩選'
              : '請點擊「同步模型列表」或「新增 AI 模型」'
        }}
      </p>
    </div>
  </div>

  <!-- 刪除確認 Dialog -->
  <AlertDialog :open="showDeleteAlert">
    <AlertDialogContent>
      <AlertDialogHeader>
        <AlertDialogTitle>確認刪除</AlertDialogTitle>
        <AlertDialogDescription> 確定要刪除這個模型嗎？此操作無法復原。 </AlertDialogDescription>
      </AlertDialogHeader>
      <AlertDialogFooter>
        <AlertDialogCancel @click="showDeleteAlert = false">取消</AlertDialogCancel>
        <AlertDialogAction @click="handleDelete">確定刪除</AlertDialogAction>
      </AlertDialogFooter>
    </AlertDialogContent>
  </AlertDialog>
</template>
