<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Skeleton } from '@/components/ui/skeleton';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import {
  RefreshCw,
  Activity,
  AlertCircle,
  CheckCircle,
  Database,
  Key,
  Bot,
  Settings,
} from 'lucide-vue-next';
import { AiSettingsService } from '@/services/admin/ai/ai-settings.service';
import type {
  AiConfigurationOverview,
  ConnectionTestResult,
  SystemSyncResult,
  ProviderStatus,
  ModelStatus,
  BotStatus,
  UsageStatistics,
  HealthCheckResult,
} from '@/types/dto/ai-settings.dto';
import { useToast } from '@/components/ui/toast/use-toast';

const { toast } = useToast();

// 響應式資料
const loading = ref(true);
const syncing = ref(false);
const testing = ref<Record<string, boolean>>({});
const overview = ref<AiConfigurationOverview | null>(null);
const lastUpdated = ref<Date | null>(null);

// 計算屬性
const healthStatus = computed(() => {
  if (!overview.value?.health_check) return null;
  const { status, checks } = overview.value.health_check;
  const healthyCount = Object.values(checks).filter(Boolean).length;
  const totalCount = Object.keys(checks).length;

  return {
    status,
    percentage: (healthyCount / totalCount) * 100,
    healthyCount,
    totalCount,
    checks,
  };
});

const sortedProviders = computed(() => {
  if (!overview.value?.provider_status) return [];
  return [...overview.value.provider_status].sort((a, b) => {
    if (a.status === 'active' && b.status !== 'active') return -1;
    if (a.status !== 'active' && b.status === 'active') return 1;
    return b.enabled_keys_count - a.enabled_keys_count;
  });
});

const usageStats = computed(() => overview.value?.usage_stats);

const systemSummary = computed(() => {
  if (!overview.value) return null;

  const totalProviders = overview.value.provider_status.length;
  const activeProviders = overview.value.provider_status.filter(
    (p) => p.status === 'active',
  ).length;
  const totalModels = overview.value.model_status.length;
  const enabledModels = overview.value.model_status.filter((m) => m.is_enabled).length;
  const totalBots = overview.value.bot_status.length;
  const enabledBots = overview.value.bot_status.filter((b) => b.is_enabled).length;

  return {
    totalProviders,
    activeProviders,
    totalModels,
    enabledModels,
    totalBots,
    enabledBots,
  };
});

// 方法
const loadOverview = async () => {
  try {
    loading.value = true;
    overview.value = await AiSettingsService.getConfigurationOverview();
    lastUpdated.value = new Date();
  } catch (error) {
    console.error('載入系統概覽失敗:', error);
    toast({
      title: '載入失敗',
      description: '無法載入 AI 系統概覽，請稍後再試',
      variant: 'destructive',
    });
  } finally {
    loading.value = false;
  }
};

const testConnection = async (provider: string, keyId?: string) => {
  const testKey = keyId || provider;
  try {
    testing.value[testKey] = true;
    const result = await AiSettingsService.testProviderConnection(provider, keyId);

    toast({
      title: result.success ? '連接測試成功' : '連接測試失敗',
      description: result.message,
      variant: result.success ? 'default' : 'destructive',
    });

    // 測試完成後重新載入概覽以更新狀態
    if (result.success) {
      await loadOverview();
    }
  } catch (error) {
    console.error('測試連接失敗:', error);
    toast({
      title: '測試失敗',
      description: '連接測試過程中發生錯誤',
      variant: 'destructive',
    });
  } finally {
    testing.value[testKey] = false;
  }
};

const syncSystem = async () => {
  try {
    syncing.value = true;
    const result = await AiSettingsService.syncSystemConfiguration();

    toast({
      title: result.success ? '同步成功' : '同步失敗',
      description: result.message,
      variant: result.success ? 'default' : 'destructive',
    });

    if (result.success) {
      await loadOverview();
    }
  } catch (error) {
    console.error('系統同步失敗:', error);
    toast({
      title: '同步失敗',
      description: '系統配置同步過程中發生錯誤',
      variant: 'destructive',
    });
  } finally {
    syncing.value = false;
  }
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'active':
    case 'healthy':
      return 'bg-green-500';
    case 'warning':
      return 'bg-yellow-500';
    case 'inactive':
    case 'error':
      return 'bg-red-500';
    default:
      return 'bg-gray-500';
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case 'active':
    case 'healthy':
      return CheckCircle;
    case 'warning':
      return AlertCircle;
    case 'inactive':
    case 'error':
      return AlertCircle;
    default:
      return Activity;
  }
};

const formatCost = (cost: number) => {
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 4,
  }).format(cost);
};

const formatNumber = (num: number) => {
  return new Intl.NumberFormat('zh-TW').format(num);
};

const formatDate = (date: Date) => {
  return new Intl.DateTimeFormat('zh-TW', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  }).format(date);
};

onMounted(() => {
  loadOverview();
});
</script>

<template>
  <div class="space-y-6">
    <!-- 標題與操作列 -->
    <div class="flex justify-between items-center">
      <div>
        <h2 class="text-2xl font-bold">系統概覽</h2>
        <p class="text-muted-foreground">AI 系統配置與狀態監控</p>
      </div>

      <div class="flex gap-2">
        <Button variant="outline" size="sm" @click="loadOverview" :disabled="loading">
          <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': loading }]" />
          重新整理
        </Button>

        <Button size="sm" @click="syncSystem" :disabled="syncing">
          <Settings :class="['w-4 h-4 mr-2', { 'animate-spin': syncing }]" />
          同步配置
        </Button>
      </div>
    </div>

    <!-- 系統健康狀態 -->
    <Card v-if="!loading && healthStatus">
      <CardHeader>
        <div class="flex items-center justify-between">
          <div>
            <CardTitle class="flex items-center gap-2">
              <component
                :is="getStatusIcon(healthStatus.status)"
                :class="['w-5 h-5', getStatusColor(healthStatus.status).replace('bg-', 'text-')]"
              />
              系統健康狀態
            </CardTitle>
            <CardDescription>
              {{ healthStatus.healthyCount }}/{{ healthStatus.totalCount }}
              項檢查通過
            </CardDescription>
          </div>

          <!-- 最後更新時間 -->
          <div v-if="lastUpdated" class="text-sm text-muted-foreground">
            最後更新: {{ formatDate(lastUpdated) }}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div class="space-y-4">
          <div class="flex items-center gap-2">
            <Progress :value="healthStatus.percentage" class="flex-1" />
            <Badge :variant="healthStatus.status === 'healthy' ? 'default' : 'destructive'">
              {{
                healthStatus.status === 'healthy'
                  ? '健康'
                  : healthStatus.status === 'warning'
                    ? '警告'
                    : '錯誤'
              }}
            </Badge>
          </div>

          <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div
              v-for="(status, key) in healthStatus.checks"
              :key="key"
              class="flex items-center gap-2 p-2 rounded border"
            >
              <component
                :is="status ? CheckCircle : AlertCircle"
                :class="['w-4 h-4', status ? 'text-green-500' : 'text-red-500']"
              />
              <span class="text-sm">
                {{
                  key === 'database'
                    ? '資料庫'
                    : key === 'ai_keys'
                      ? 'AI 金鑰'
                      : key === 'ai_models'
                        ? 'AI 模型'
                        : key === 'ai_features'
                          ? 'AI 功能'
                          : key
                }}
              </span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 系統摘要統計 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <Card v-if="loading">
        <CardContent class="pt-6">
          <Skeleton class="h-8 w-24 mb-2" />
          <Skeleton class="h-4 w-32" />
        </CardContent>
      </Card>

      <template v-else-if="systemSummary">
        <Card>
          <CardContent class="pt-6">
            <div class="flex items-center gap-2">
              <Database class="w-5 h-5 text-blue-500" />
              <div>
                <div class="text-2xl font-bold">
                  {{ systemSummary.activeProviders }}/{{ systemSummary.totalProviders }}
                </div>
                <div class="text-sm text-muted-foreground">活躍供應商</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="pt-6">
            <div class="flex items-center gap-2">
              <Settings class="w-5 h-5 text-green-500" />
              <div>
                <div class="text-2xl font-bold">
                  {{ systemSummary.enabledModels }}/{{ systemSummary.totalModels }}
                </div>
                <div class="text-sm text-muted-foreground">啟用模型</div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent class="pt-6">
            <div class="flex items-center gap-2">
              <Bot class="w-5 h-5 text-purple-500" />
              <div>
                <div class="text-2xl font-bold">
                  {{ systemSummary.enabledBots }}/{{ systemSummary.totalBots }}
                </div>
                <div class="text-sm text-muted-foreground">啟用助理</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </template>
    </div>

    <!-- 供應商狀態 -->
    <Card>
      <CardHeader>
        <CardTitle>供應商狀態</CardTitle>
        <CardDescription>AI 供應商的配置和連接狀態</CardDescription>
      </CardHeader>
      <CardContent>
        <div v-if="loading" class="space-y-3">
          <Skeleton v-for="n in 3" :key="n" class="h-16" />
        </div>

        <div
          v-else-if="sortedProviders.length === 0"
          class="text-center py-8 text-muted-foreground"
        >
          尚未配置任何 AI 供應商
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="provider in sortedProviders"
            :key="provider.name"
            class="flex items-center justify-between p-4 rounded-lg border"
          >
            <div class="flex items-center gap-4">
              <Badge :variant="provider.status === 'active' ? 'default' : 'secondary'">
                {{ provider.status === 'active' ? '活躍' : '非活躍' }}
              </Badge>

              <div>
                <div class="font-medium">{{ provider.name }}</div>
                <div class="text-sm text-muted-foreground">
                  {{ provider.enabled_keys_count }} 個金鑰 •
                  {{ provider.enabled_models_count }} 個模型 •
                  {{ provider.enabled_bots_count }} 個助理
                </div>
              </div>
            </div>

            <Button
              variant="outline"
              size="sm"
              @click="testConnection(provider.name)"
              :disabled="testing[provider.name]"
            >
              <RefreshCw :class="['w-4 h-4 mr-2', { 'animate-spin': testing[provider.name] }]" />
              測試連接
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>

    <!-- 使用統計 -->
    <Card v-if="usageStats">
      <CardHeader>
        <CardTitle>使用統計</CardTitle>
        <CardDescription>{{ usageStats.period }} 的 AI 服務使用情況</CardDescription>
      </CardHeader>
      <CardContent>
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div class="text-center p-4 rounded-lg bg-muted">
            <div class="text-2xl font-bold">
              {{ formatNumber(usageStats.total_calls) }}
            </div>
            <div class="text-sm text-muted-foreground">總調用次數</div>
          </div>

          <div class="text-center p-4 rounded-lg bg-muted">
            <div class="text-2xl font-bold">{{ (usageStats.failure_rate * 100).toFixed(1) }}%</div>
            <div class="text-sm text-muted-foreground">失敗率</div>
          </div>

          <div class="text-center p-4 rounded-lg bg-muted">
            <div class="text-2xl font-bold">
              {{ formatNumber(usageStats.total_tokens) }}
            </div>
            <div class="text-sm text-muted-foreground">總 Token 數</div>
          </div>

          <div class="text-center p-4 rounded-lg bg-muted">
            <div class="text-2xl font-bold">
              {{ formatCost(usageStats.total_cost) }}
            </div>
            <div class="text-sm text-muted-foreground">總成本</div>
          </div>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
