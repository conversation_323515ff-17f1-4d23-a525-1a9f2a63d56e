<script setup lang="ts">
import { ref, watch, computed } from 'vue'
import { format } from 'date-fns'
import { Calendar as CalendarIcon } from 'lucide-vue-next'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'

type ButtonSize = 'default' | 'sm' | 'lg' | 'icon'

const props = defineProps<{
  modelValue?: Date
  class?: string
  size?: ButtonSize
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: Date): void
}>()

const date = ref<Date | undefined>(props.modelValue)

watch(() => props.modelValue, (newValue) => {
  if (newValue !== date.value) {
    date.value = newValue
  }
})

const handleSelect = (value: Date | undefined) => {
  date.value = value
  if (value) {
    emit('update:modelValue', value)
  }
}

const buttonClass = computed(() => {
  const baseClass = 'justify-start text-left font-normal gap-2 border-emerald-500 text-emerald-600 hover:bg-emerald-50 dark:border-emerald-400 dark:text-emerald-400 dark:hover:bg-emerald-500/10'
  const sizeClass = {
    'default': 'w-[280px]',
    'sm': 'w-[200px] text-sm',
    'lg': 'w-[320px] text-lg',
    'icon': 'w-10'
  }[props.size || 'default']
  
  return cn(
    baseClass,
    sizeClass,
    !date.value && 'text-muted-foreground',
    props.class
  )
})

const iconClass = computed(() => {
  const sizeClass = {
    'default': 'w-4 h-4',
    'sm': 'w-3.5 h-3.5',
    'lg': 'w-5 h-5',
    'icon': 'w-4 h-4'
  }[props.size || 'default']
  
  return sizeClass
})
</script>

<template>
  <Popover>
    <PopoverTrigger asChild>
      <Button
        :variant="'outline'"
        :size="props.size"
        :class="buttonClass"
      >
        <CalendarIcon :class="iconClass" />
        <span v-if="props.size !== 'icon'">{{ date ? format(date, 'PPP') : '選擇日期' }}</span>
      </Button>
    </PopoverTrigger>
    <PopoverContent class="w-auto p-0">
      <Calendar
        mode="single"
        :selected="date"
        :default-month="date"
        @select="handleSelect"
        initial-focus
      />
    </PopoverContent>
  </Popover>
</template> 
