<template>
  <SidebarProvider>
    <div
      class="flex h-screen w-full overflow-hidden bg-zinc-50 dark:bg-zinc-900 select-none"
    >
      <!-- 側邊欄 -->
      <AdminSidebar />

      <!-- 主要內容區域 -->
      <div
        class="flex-1 flex flex-col min-w-0 overflow-hidden peer-data-[collapsible=icon]:pl-0 transition-all duration-300"
      >
        <!-- 頂部導航欄 -->
        <header
          class="sticky top-0 z-40 flex h-12 min-h-[48px] shrink-0 items-center gap-x-2 sm:gap-x-3 border-b border-border/40 bg-white/95 dark:bg-zinc-900/95 px-2 sm:px-3 shadow-sm md:gap-x-4 md:px-4 lg:px-6 backdrop-blur supports-[backdrop-filter]:bg-white/60"
        >
          <!-- 漢堡選單按鈕（僅在行動裝置顯示） -->
          <SidebarTrigger
            class="lg:hidden -m-1.5 p-1.5 text-zinc-700 dark:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-md"
          />

          <!-- 分隔線 -->
          <div class="h-5 w-px bg-border/40 lg:hidden" aria-hidden="true" />

          <!-- 頁面標題和麵包屑區 -->
          <div class="flex flex-1 items-center justify-between min-w-0">
            <div class="flex items-center gap-2">
              <SidebarTrigger
                class="hidden lg:flex p-1 text-zinc-500 hover:text-zinc-700 dark:text-zinc-400 dark:hover:text-zinc-300 hover:bg-zinc-100 dark:hover:bg-zinc-800 rounded-md transition-colors"
              />
              <h1
                class="text-sm sm:text-base font-medium text-zinc-900 dark:text-zinc-100 truncate"
              >
                {{ route.meta.title || "管理後台" }}
              </h1>
            </div>

            <!-- 右側工具列 -->
            <div
              class="flex items-center gap-x-0.5 sm:gap-x-1.5 md:gap-x-3 lg:gap-x-4"
            >
              <!-- 通知按鈕（桌面版) -->
              <button
                class="hidden sm:flex p-1.5 min-w-[36px] min-h-[36px] text-zinc-400 hover:text-zinc-500 dark:text-zinc-500 dark:hover:text-zinc-400 rounded-md transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800"
                aria-label="查看通知"
              >
                <i class="i-heroicons-bell h-4 w-4 sm:h-5 sm:w-5" />
                <span class="sr-only">查看通知</span>
              </button>

              <!-- 主題切換按鈕 -->
              <button
                class="p-1.5 min-w-[36px] min-h-[36px] text-zinc-400 hover:text-zinc-500 dark:text-zinc-500 dark:hover:text-zinc-400 rounded-md transition-colors hover:bg-zinc-100 dark:hover:bg-zinc-800"
                @click="toggleTheme"
                aria-label="切換主題"
              >
                <i
                  :class="[isDarkMode ? 'i-heroicons-sun' : 'i-heroicons-moon']"
                  class="h-4 w-4 sm:h-5 sm:w-5"
                />
                <span class="sr-only">切換主題</span>
              </button>

              <!-- 分隔線 -->
              <div
                class="hidden md:block md:h-6 md:w-px md:bg-zinc-200 dark:md:bg-zinc-700"
                aria-hidden="true"
              />

              <!-- 使用者選單 -->
              <UserMenu
                :user-name="userName"
                :user-role="userRole"
                :avatar="userAvatar"
                @logout="handleLogout"
              />
            </div>
          </div>
        </header>

        <!-- 頁面內容 -->
        <main class="flex-1 overflow-auto px-3 md:px-4 lg:px-6 py-3">
          <router-view v-slot="{ Component, route }">
            <transition
              enter-active-class="transition-all duration-300 ease-out"
              enter-from-class="opacity-0 transform translate-y-4"
              enter-to-class="opacity-100 transform translate-y-0"
              leave-active-class="transition-all duration-200 ease-in"
              leave-from-class="opacity-100 transform translate-y-0"
              leave-to-class="opacity-0 transform translate-y-4"
            >
              <div v-if="isPageLoading" class="w-full h-full space-y-6">
                <!-- 骨架屏載入動畫 -->
                <Skeleton class="h-8 w-64 mb-2" />
                <Skeleton class="h-4 w-full max-w-md mb-8" />

                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
                  <Skeleton class="h-32 w-full rounded-lg" />
                  <Skeleton class="h-32 w-full rounded-lg" />
                  <Skeleton class="h-32 w-full rounded-lg" />
                </div>

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <Skeleton class="h-64 w-full rounded-lg" />
                  <Skeleton class="h-64 w-full rounded-lg" />
                </div>

                <Skeleton class="h-64 w-full rounded-lg mt-6" />
              </div>
              <div v-else>
                <component :is="Component" :key="route.path" />
              </div>
            </transition>
          </router-view>
        </main>
      </div>
    </div>
  </SidebarProvider>
</template>

<script setup lang="ts">
import { ref, computed, inject, onMounted, onBeforeUnmount, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import AdminSidebar from "@/components/common/navigation/AdminSidebar.vue";
import UserMenu from "@/components/common/navigation/UserMenu.vue";
import { useTheme } from "@/composables/useTheme";
import { usePermission } from "@/composables/admin/usePermission";
import { useAuth } from "@horizai/auth";
import { SidebarProvider, SidebarTrigger } from "@/components/ui/sidebar";
import { Skeleton } from "@/components/ui/skeleton";
import { Actions, Subjects } from "@horizai/permissions";

// 路由
const router = useRouter();
const route = useRoute();

// 狀態
const isMobileMenuOpen = ref(false);
const isPageLoading = ref(true);

// 監聽路由變化來控制載入狀態
watch(
  () => route.path,
  () => {
    isPageLoading.value = true;
    // 模擬載入時間
    setTimeout(() => {
      isPageLoading.value = false;
    }, 1000);
  }
);

// 使用權限 composable
const { currentUser, isSuperAdmin, isSystemAdmin } = usePermission();

// 使用者資訊
const userName = computed(() => currentUser.value?.name ?? "管理員");
const userRole = computed(() => {
  if (isSuperAdmin.value) return "超級管理員";
  if (isSystemAdmin.value) return "系統管理員";
  if (currentUser.value) return "管理員";
  return "訪客";
});
const userAvatar = computed(
  () => currentUser.value?.avatar ?? "/images/default1.png"
);

// 主題切換
const { isDarkMode, toggleTheme } = useTheme();

// 行動選單切換
const toggleMobileMenu = () => {
  isMobileMenuOpen.value = !isMobileMenuOpen.value;
};

// 關閉行動選單
const closeMobileMenu = () => {
  isMobileMenuOpen.value = false;
};

// 登出
const handleLogout = async () => {
  try {
    const auth = useAuth();
    await auth.logout();

    // 登出成功，顯示成功訊息
    console.log('管理員登出成功');

  } catch (error: any) {
    console.error('登出過程中發生錯誤:', error);
  } finally {
    // 無論登出是否成功，都要執行重導向
    try {
      const { handleLogoutRedirect } = await import('@/utils/redirect.utils');
      const target = handleLogoutRedirect();
      await router.push(target);
    } catch (routerError) {
      console.error('重導向失敗，使用強制跳轉:', routerError);
      window.location.href = '/auth/login';
    }
  }
};

// 行動裝置支援 - 在視窗調整大小時自動關閉選單
const handleResize = () => {
  if (window.innerWidth >= 1024 && isMobileMenuOpen.value) {
    closeMobileMenu();
  }
};

onMounted(() => {
  window.addEventListener("resize", handleResize);
  // 初始載入時設定載入狀態
  setTimeout(() => {
    isPageLoading.value = false;
  }, 1000);
});

onBeforeUnmount(() => {
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
/* 基本樣式 */
:deep(.sidebar-container) {
  height: 100vh;
  display: flex;
  user-select: none;
}

/* 確保所有文字內容不可選取 */
:deep(*) {
  user-select: none;
}

/* 允許特定元素可選取（如果需要） */
:deep(.selectable) {
  user-select: text;
}

/* 自定義滾動條樣式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: transparent;
}

::-webkit-scrollbar-thumb {
  background-color: rgba(156, 163, 175, 0.5);
  border-radius: 4px;
  border: 2px solid transparent;
  background-clip: content-box;
}

::-webkit-scrollbar-thumb:hover {
  background-color: rgba(156, 163, 175, 0.7);
}

/* Safari 滾動優化 */
@supports (-webkit-overflow-scrolling: touch) {
  .overflow-auto {
    -webkit-overflow-scrolling: touch;
  }
}

/* 平滑滾動 */
html {
  scroll-behavior: smooth;
}

/* 確保內容區域最小高度和寬度 */
main {
  min-width: 320px;
}

/* 確保所有互動元素有過渡效果和最小觸控區域 */
button,
a,
input,
select {
  transition: all 0.2s ease;
  -webkit-tap-highlight-color: transparent;
  touch-action: manipulation;
}

/* 觸控優化 */
@media (max-width: 768px) {
  button,
  a {
    min-height: 44px;
    min-width: 44px;
  }

  header {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }
}
</style>
