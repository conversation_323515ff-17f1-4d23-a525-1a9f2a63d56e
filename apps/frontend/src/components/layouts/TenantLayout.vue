<template>
  <SidebarProvider>
    <div
      class="flex h-screen w-screen overflow-hidden bg-zinc-50/30 dark:bg-zinc-900 border border-zinc-200/50 dark:border-zinc-800/50"
    >
      <!-- 使用 Shadcn Sidebar -->
      <Sidebar class="w-[210px] shrink-0">
        <SidebarHeader>
          <div
            class="flex h-[60px] sm:h-16 items-center px-4 md:px-0 md:justify-center border-b border-zinc-200/50 dark:border-zinc-800/50 relative"
          >
            <SidebarStateObserver @state-changed="handleSidebarStateChanged" />
            <img
              v-if="!isSidebarCollapsed"
              src="@/assets/images/logo.svg"
              alt="HorizAI"
              class="h-6"
            />
            <img
              v-else
              src="@/assets/images/logo-icon.svg"
              alt="HorizAI"
              class="h-7 text-primary"
            />

            <!-- 收摺按鈕 - 僅在桌面版顯示 -->
            <SidebarCollapseButton :is-collapsed="isSidebarCollapsed" />
          </div>
        </SidebarHeader>

        <!-- 快速操作區 -->
        <div class="flex justify-center py-4">
          <button
            @click="openCreateTenant"
            :class="[
              'flex items-center justify-center transition-all duration-200',
              isSidebarCollapsed
                ? 'w-10 h-10 sm:w-8 sm:h-8 rounded-full border border-emerald-500'
                : 'h-12 md:h-9 px-4 md:px-3 rounded-xl md:rounded-lg',
              'bg-emerald-600 hover:bg-emerald-700 text-white',
            ]"
          >
            <PlusIcon class="w-5 h-5 sm:w-4 sm:h-4" />
            <span
              v-if="!isSidebarCollapsed"
              class="ml-3 md:ml-2 text-base md:text-sm font-medium"
              >新增租戶</span
            >
          </button>
        </div>

        <SidebarContent>
          <SidebarGroup>
            <SidebarGroupLabel>租戶管理</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <template
                  v-for="item in navigationItems"
                  :key="'type' in item ? 'divider' : item.path"
                >
                  <SidebarSeparator v-if="'type' in item" />
                  <SidebarMenuItem v-else>
                    <SidebarMenuButton asChild>
                      <a
                        :href="item.path"
                        @click.prevent="handleNavigation(item.path)"
                        :class="[
                          $route.path.includes(item.path)
                            ? 'text-emerald-600 dark:text-emerald-400'
                            : 'text-zinc-600 dark:text-zinc-400',
                        ]"
                      >
                        <component :is="item.icon" class="w-4 h-4" />
                        <span>{{ item.name }}</span>
                      </a>
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                </template>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>

          <SidebarSeparator />

          <SidebarGroup>
            <SidebarGroupLabel>最近活動</SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu>
                <SidebarMenuItem
                  v-for="activity in recentActivities"
                  :key="activity.id"
                >
                  <SidebarMenuButton asChild>
                    <a
                      href="#"
                      @click.prevent="handleActivityClick(activity.id)"
                      class="flex items-center space-x-2"
                    >
                      <span
                        class="w-2 h-2 rounded-full"
                        :class="activity.statusColor"
                      ></span>
                      <span>{{ activity.name }}</span>
                    </a>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        </SidebarContent>

        <SidebarFooter>
          <UserMenu
            :user-name="userName"
            :user-role="userRole"
            :avatar="userAvatar"
            @logout="handleLogout"
          />
        </SidebarFooter>

        <SidebarRail />
      </Sidebar>

      <!-- 主內容區 -->
      <div class="flex-1 flex flex-col bg-white dark:bg-zinc-900 w-full">
        <header
          class="h-16 flex items-center px-4 md:px-8 bg-white/80 dark:bg-zinc-900/80 backdrop-blur-xl border-b border-zinc-200/50 dark:border-zinc-800/50"
        >
          <SidebarTrigger class="md:hidden mr-4" />
          <BreadcrumbNav />
          <div class="ml-auto flex items-center space-x-3">
            <button
              @click="openSearch"
              class="hidden sm:inline-flex items-center px-4 h-9 rounded-lg text-sm text-zinc-600 dark:text-zinc-400 hover:bg-emerald-50 dark:hover:bg-emerald-500/10 transition-colors border border-emerald-500/20 dark:border-emerald-400/20 hover:border-emerald-500 dark:hover:border-emerald-400/50 w-[280px] justify-between"
            >
              <div class="flex items-center">
                <MagnifyingGlassIcon class="w-4 h-4" />
                <span class="ml-2">搜尋</span>
              </div>
              <kbd
                class="text-[10px] font-mono bg-zinc-100 dark:bg-zinc-800 px-1.5 py-0.5 rounded"
                >⌘K</kbd
              >
            </button>
            <NotificationBell />
          </div>
        </header>

        <main class="flex-1 overflow-y-auto py-4 md:py-6 min-h-0">
          <Container size="xl" padding fullHeight class="h-full">
            <router-view v-slot="{ Component }">
              <transition
                enter-active-class="transition ease-out duration-200"
                enter-from-class="opacity-0 translate-y-1"
                enter-to-class="opacity-100 translate-y-0"
                leave-active-class="transition ease-in duration-150"
                leave-from-class="opacity-100 translate-y-0"
                leave-to-class="opacity-0 translate-y-1"
              >
                <component :is="Component" />
              </transition>
            </router-view>
          </Container>
        </main>
      </div>
    </div>
  </SidebarProvider>
</template>

<script setup lang="ts">
import {
  ref,
  computed,
  onMounted,
  onUnmounted,
  watch,
  defineComponent,
  h,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  HomeIcon,
  UsersIcon,
  CurrencyDollarIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ShieldCheckIcon,
  PlusIcon,
  MagnifyingGlassIcon,
  BuildingOffice2Icon,
} from "@heroicons/vue/24/outline/index.js";
import NotificationBell from "@/components/common/NotificationBell.vue";
import UserMenu from "@/components/common/navigation/UserMenu.vue";
import ModalTenantForm from "@/components/admin/tenant/ModalTenantForm.vue";
import SearchModal from "@/components/common/modal/SearchModal.vue";
import BreadcrumbNav from "@/components/common/navigation/BreadcrumbNav.vue";
import Container from "@/components/layouts/Container.vue";
import {
  Sidebar,
  SidebarProvider,
  SidebarContent,
  SidebarHeader,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupLabel,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarSeparator,
  SidebarTrigger,
  SidebarRail,
  useSidebar,
} from "@/components/ui/sidebar";
import { useAuth } from '@horizai/auth'
import { usePermission } from "@/composables/admin/usePermission";

// 自定義收摺按鈕元件
const SidebarCollapseButton = defineComponent({
  props: {
    isCollapsed: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const sidebar = useSidebar();

    return () =>
      h(
        "button",
        {
          class: `hidden md:flex absolute right-0 translate-x-1/2 w-7 h-7 sm:w-6 sm:h-6 bg-white dark:bg-zinc-800 border border-zinc-200/50 dark:border-zinc-700/50 rounded-full items-center justify-center hover:bg-zinc-50 dark:hover:bg-zinc-700/50 transition-all duration-300 shadow-[0_2px_8px_-1px_rgba(0,0,0,0.1)] dark:shadow-[0_2px_8px_-1px_rgba(0,0,0,0.3)] hover:shadow-[0_4px_12px_-1px_rgba(0,0,0,0.15)] dark:hover:shadow-[0_4px_12px_-1px_rgba(0,0,0,0.4)] z-[200]`,
          onClick: () => sidebar.toggleSidebar(),
        },
        [
          h("span", {
            class: `w-5 h-5 sm:w-4 sm:h-4 text-zinc-400 transition-transform duration-300 ${
              props.isCollapsed ? "rotate-180" : ""
            }`,
            innerHTML: `<svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-full h-full"><path stroke-linecap="round" stroke-linejoin="round" d="M15.75 19.5 8.25 12l7.5-7.5" /></svg>`,
          }),
        ]
      );
  },
});

// 用於監聽側邊欄狀態的元件
const SidebarStateObserver = defineComponent({
  emits: ["state-changed"],
  setup(_, { emit }) {
    const sidebar = useSidebar();

    watch(
      () => sidebar.state.value,
      (newState) => {
        emit("state-changed", newState === "collapsed");
      },
      { immediate: true }
    );

    return () => null;
  },
});

const router = useRouter();
const route = useRoute();
const isSearchOpen = ref(false);
const isCreateTenantOpen = ref(false);
const isMobileMenuOpen = ref(false);
const isSidebarCollapsed = ref(false);

// 使用者資訊
const auth = useAuth()
const user = computed(() => auth.getUser())
const isAdmin = computed(() => user.value?.role === 'admin')
const isTenantAdmin = computed(() => user.value?.role === 'tenant_admin')
const userName = computed(() => user.value?.name || "使用者");
const userRole = computed(() => {
  if (isAdmin.value) return "超級管理員";
  if (isTenantAdmin.value) return "租戶管理員";
  return "租戶使用者";
});
const userAvatar = computed(
  () => user.value?.avatar || "/images/default-avatar.png"
);

// 修改導航項目的型別
interface NavigationItem {
  name: string;
  path: string;
  icon: any;
}

interface DividerItem {
  type: "divider";
}

type NavItem = NavigationItem | DividerItem;

const navigationItems: NavItem[] = [
  {
    name: "儀表板",
    path: `/workspace/${route.params.tenantId}/wsadmin/dashboard`,
    icon: HomeIcon,
  },
  {
    name: "成員管理",
    path: `/workspace/${route.params.tenantId}/wsadmin/users`,
    icon: UsersIcon,
  },
  {
    name: "部門管理",
    path: `/workspace/${route.params.tenantId}/wsadmin/departments`,
    icon: BuildingOffice2Icon,
  },
  {
    name: "帳務管理",
    path: `/workspace/${route.params.tenantId}/wsadmin/billing`,
    icon: CurrencyDollarIcon,
  },
  {
    type: "divider",
  },
  {
    name: "系統設定",
    path: `/workspace/${route.params.tenantId}/wsadmin/settings`,
    icon: Cog6ToothIcon,
  },
];

// 最近的活動
const recentActivities = [
  { id: 1, name: "新增使用者：王小明", statusColor: "bg-success" },
  { id: 2, name: "方案升級：專業版", statusColor: "bg-warning" },
  { id: 3, name: "權限更新：管理員", statusColor: "bg-info" },
];

// 新增租戶表單資料
interface TenantData {
  name: string;
  domain: string;
  plan: string;
  adminEmail: string;
}

const newTenant = ref<TenantData>({
  name: "",
  domain: "",
  plan: "",
  adminEmail: "",
});

// 處理租戶建立
const handleCreateTenant = (tenant: TenantData) => {
  console.log("Created tenant:", tenant);
  isCreateTenantOpen.value = false;
  // 重置表單
  newTenant.value = {
    name: "",
    domain: "",
    plan: "",
    adminEmail: "",
  };
};

// 處理搜尋結果選擇
const handleSearchSelect = (result: any) => {
  console.log("Selected search result:", result);
  closeSearch();
};

// 監聽快捷鍵
const handleKeyDown = (e: KeyboardEvent) => {
  if ((e.metaKey || e.ctrlKey) && e.key.toLowerCase() === "k") {
    e.preventDefault();
    isSearchOpen.value = true;
  }
};

// 打開搜尋
const openSearch = () => {
  isSearchOpen.value = true;
};

// 關閉搜尋
const closeSearch = () => {
  isSearchOpen.value = false;
};

// 打開建立租戶
const openCreateTenant = () => {
  isCreateTenantOpen.value = true;
};

// 關閉建立租戶
const closeCreateTenant = () => {
  isCreateTenantOpen.value = false;
};

// 處理導航點擊
const handleNavigation = (path: string) => {
  router.push(path);
};

// 處理活動點擊
const handleActivityClick = (activityId: number) => {
  console.log("Activity clicked:", activityId);
};

// 監聽視窗大小變化
const handleResize = () => {
  if (window.innerWidth >= 768) {
    // md breakpoint
    isMobileMenuOpen.value = false;
  }
};

// 處理側邊欄狀態變化
const handleSidebarStateChanged = (isCollapsed: boolean) => {
  isSidebarCollapsed.value = isCollapsed;
};

// 處理登出
const handleLogout = async () => {
  try {
    await auth.logout();

    // 登出成功，顯示成功訊息
    console.log('租戶用戶登出成功');

  } catch (error: any) {
    console.error('登出過程中發生錯誤:', error);
  } finally {
    // 無論登出是否成功，都要執行重導向
    try {
      const { handleLogoutRedirect } = await import('@/utils/redirect.utils');
      const target = handleLogoutRedirect();
      await router.push(target);
    } catch (routerError) {
      console.error('重導向失敗，使用強制跳轉:', routerError);
      window.location.href = '/auth/login';
    }
  }
};

onMounted(() => {
  window.addEventListener("keydown", handleKeyDown);
  window.addEventListener("resize", handleResize);
});

onUnmounted(() => {
  window.removeEventListener("keydown", handleKeyDown);
  window.removeEventListener("resize", handleResize);
});
</script>

<style scoped>
/* 確保在移動設備上不會出現水平滾動 */
@media (max-width: 640px) {
  .container {
    overflow-x: hidden;
  }
}
</style>
