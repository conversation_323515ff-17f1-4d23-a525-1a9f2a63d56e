import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { checkAuthStatus } from '@/utils/auth-check.utils'

/**
 * 租戶路由守衛
 * 確保用戶已認證且具有訪問租戶相關頁面的權限
 */
export async function tenantGuard(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
) {
    try {
        const authStatus = checkAuthStatus()

        if (!authStatus.isAuthenticated) {
            console.warn('用戶未認證，重定向到登入頁面')
            return next({
                name: 'login',
                query: { redirect: to.fullPath }
            })
        }

        // 檢查用戶資訊是否存在
        if (!authStatus.user) {
            console.error('無法獲取用戶資訊，重定向到登入頁面')
            return next({
                name: 'login',
                query: { redirect: to.fullPath }
            })
        }

        // 檢查租戶相關權限（如果路由需要特定權限）
        if (to.meta?.requiresPermission && authStatus.ability) {
            const { action, subject, field } = to.meta.requiresPermission
            
            if (!authStatus.ability.can(action, subject, field)) {
                console.warn(`用戶缺少權限: ${action} ${subject}${field ? ` (${field})` : ''}`)
                return next({
                    name: 'forbidden',
                    query: { reason: 'insufficient_permissions' }
                })
            }
        }

        // 檢查租戶 ID（如果路由包含租戶參數）
        if (to.params.tenantId) {
            const userTenantId = authStatus.user?.tenant_id
            
            if (userTenantId && userTenantId !== to.params.tenantId) {
                console.warn('用戶嘗試訪問非所屬租戶的資源')
                return next({
                    name: 'forbidden',
                    query: { reason: 'tenant_mismatch' }
                })
            }
        }

        // 所有檢查通過，允許訪問
        next()
        
    } catch (error) {
        console.error('租戶路由守衛執行錯誤:', error)
        
        // 發生錯誤時重定向到登入頁面
        next({
            name: 'login',
            query: { 
                redirect: to.fullPath,
                error: 'guard_error'
            }
        })
    }
} 