import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { useAuth, useAuthStore } from '@horizai/auth'
import { getDefaultRedirectPath, handleUnauthorizedAccess } from '@/utils/redirect.utils'
import {
    checkAuthStatus,
    parseRouteAuthRequirement,
    isRouteAccessible,
    checkUserSetupStatus,
    getAuthErrorMessage
} from '@/utils/auth-check.utils'

/**
 * 認證守衛 - 處理認證相關的路由保護
 */
export async function authGuard(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
) {
    try {
        console.log('🔒 認證守衛開始檢查:', to.path)
        
        // 檢查當前認證狀態
        const authStatus = checkAuthStatus()
        const authStore = useAuthStore()

        console.log('👤 認證狀態:', {
            isAuthenticated: authStatus.isAuthenticated,
            hasValidToken: authStatus.hasValidToken,
            user: authStatus.user ? {
                email: authStatus.user.email,
                role: authStatus.user.role,
                user_type: authStatus.user.user_type
            } : null
        })

        // 解析路由認證要求
        const routeRequirement = parseRouteAuthRequirement(to)
        console.log('📋 路由要求:', routeRequirement)

        // 檢查路由是否可訪問
        const accessCheck = isRouteAccessible(to, authStatus)
        console.log('🚪 訪問檢查:', accessCheck)

        // 如果路由允許未認證訪問
        if (routeRequirement.allowUnauthenticated) {
            console.log('✅ 路由允許未認證訪問')
            // 已登入使用者訪問首頁時，統一跳轉至對應儀表板
            if (to.name === 'home' && authStatus.isAuthenticated && authStatus.user) {
                console.log('🏠 已登入用戶訪問首頁，重定向到儀表板')
                return next(getDefaultRedirectPath(authStatus.user))
            }
            return next()
        }

        // 如果路由不可訪問，處理重定向
        if (!accessCheck.accessible) {
            console.log('❌ 路由不可訪問，原因:', accessCheck.reason)
            if (accessCheck.reason === 'authentication_required') {
                console.log('🔐 需要認證，重定向到登入頁')
                return next(handleUnauthorizedAccess(to.fullPath))
            }

            if (accessCheck.reason === 'guest_only' && authStatus.user) {
                console.log('👥 僅限訪客，重定向到儀表板')
                return next(getDefaultRedirectPath(authStatus.user))
            }
        }

        // 如果用戶已認證，檢查是否需要完成設置
        if (authStatus.isAuthenticated && authStatus.user) {
            console.log('🔧 檢查用戶設置狀態...')
            const setupStatus = await checkUserSetupStatus(authStatus.user, to.path)
            console.log('⚙️ 設置狀態:', setupStatus)

            if (setupStatus.needsSetup && setupStatus.redirectTo) {
                // 避免重定向循環
                if (to.path !== setupStatus.redirectTo) {
                    console.log('🔄 需要設置，重定向到:', setupStatus.redirectTo)
                    return next({ path: setupStatus.redirectTo })
                }
            }
        }

        // 所有檢查通過，允許訪問
        console.log('✅ 認證守衛檢查通過')
        next()
    } catch (error) {
        console.error('🚨 認證守衛執行失敗:', error)
        // 發生錯誤時，根據路由要求決定是否允許訪問
        const routeRequirement = parseRouteAuthRequirement(to)
        if (routeRequirement.requiresAuth) {
            return next(handleUnauthorizedAccess(to.fullPath))
        }
        next()
    }
} 