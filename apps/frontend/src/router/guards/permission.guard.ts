import { useAuthStore } from "@horizai/auth";
import { useAbility } from "@casl/vue";
import type { RouteLocationNormalized, NavigationGuardNext, NavigationGuard } from "vue-router";
import { useNotification } from "@/composables/shared/useNotification";
import { useWorkspaceStore } from "@/stores/workspace.store";

/**
 * 權限檢查結果
 */
interface PermissionCheckResult {
  hasPermission: boolean;
  reason?: string;
  requiredPermission?: {
    action: string;
    subject: string;
  };
}

/**
 * 檢查用戶是否有特定權限
 */
function checkPermission(action: string, subject: string, field?: string): PermissionCheckResult {
  try {
    // 獲取當前用戶
    const authStore = useAuthStore();
    const user = authStore.user;

    // Super Admin 跳過所有權限檢查
    if (user && user.role === 'SUPER_ADMIN') {
      return {
        hasPermission: true,
        requiredPermission: { action, subject }
      };
    }

    const { can } = useAbility();
    const hasPermission = field ? can(action, subject, field) : can(action, subject);

    return {
      hasPermission,
      requiredPermission: { action, subject },
      reason: hasPermission ? undefined : 'insufficient_permissions'
    };
  } catch (error) {
    console.error('權限檢查失敗:', error);
    return {
      hasPermission: false,
      reason: 'permission_check_error',
      requiredPermission: { action, subject }
    };
  }
}

/**
 * 檢查路由權限要求
 */
function checkRoutePermissions(route: RouteLocationNormalized): PermissionCheckResult {
  const permission = route.meta.permission as { action: string; subject: string } | undefined;

  if (!permission) {
    return { hasPermission: true };
  }

  return checkPermission(permission.action, permission.subject);
}

/**
 * 檢查管理員路徑權限
 */
function checkAdminPathPermissions(path: string): PermissionCheckResult {
  if (path.startsWith('/admin')) {
    // 獲取當前用戶
    const authStore = useAuthStore();
    const user = authStore.user;

    // Super Admin 跳過所有權限檢查
    if (user && user.role === 'SUPER_ADMIN') {
      return { hasPermission: true };
    }

    return checkPermission('access', 'AdminPanel');
  }

  return { hasPermission: true };
}

/**
 * 檢查租戶管理員路徑權限
 */
function checkTenantAdminPathPermissions(path: string): PermissionCheckResult {
  if (path.startsWith('/tenant')) {
    // 獲取當前用戶
    const authStore = useAuthStore();
    const user = authStore.user;

    // Super Admin 跳過所有權限檢查
    if (user && user.role === 'SUPER_ADMIN') {
      return { hasPermission: true };
    }

    return checkPermission('manage', 'Tenant');
  }

  return { hasPermission: true };
}

/**
 * 檢查工作區權限
 */
async function checkWorkspacePermissions(workspaceId: string): Promise<PermissionCheckResult> {
  try {
    // 檢查基本工作區訪問權限
    const basicCheck = checkPermission('access', 'Workspace');
    if (!basicCheck.hasPermission) {
      return basicCheck;
    }

    // 檢查特定工作區權限
    const workspaceStore = useWorkspaceStore();
    const hasAccess = await workspaceStore.checkWorkspaceAccess(workspaceId);

    return {
      hasPermission: hasAccess,
      reason: hasAccess ? undefined : 'workspace_access_denied',
      requiredPermission: { action: 'access', subject: 'Workspace' }
    };
  } catch (error) {
    console.error('工作區權限檢查失敗:', error);
    return {
      hasPermission: false,
      reason: 'workspace_check_error',
      requiredPermission: { action: 'access', subject: 'Workspace' }
    };
  }
}

/**
 * 獲取權限錯誤訊息
 */
function getPermissionErrorMessage(reason: string): string {
  const messages: Record<string, string> = {
    'insufficient_permissions': '您沒有權限訪問此頁面',
    'permission_check_error': '權限檢查失敗',
    'workspace_access_denied': '您沒有權限訪問此工作區',
    'workspace_check_error': '工作區權限檢查失敗',
    'admin_access_required': '需要管理員權限',
    'tenant_admin_required': '需要租戶管理員權限'
  };

  return messages[reason] || '訪問被拒絕';
}

// 統一的權限守衛
export const permissionGuard: NavigationGuard = async (to, from, next) => {
  try {
    console.log('🔐 權限守衛開始檢查:', to.path);
    
    // 獲取認證狀態
    const authStore = useAuthStore();
    
    // 確保用戶已登入
    if (!authStore.isAuthenticated || !authStore.user) {
      console.log('❌ 用戶未登入，重定向到登入頁');
      return next({
        name: 'login',
        query: { 
          redirect: to.fullPath,
          reason: 'unauthorized'
        }
      });
    }

    const user = authStore.user;
    console.log('👤 當前用戶:', {
      email: user.email,
      role: user.role,
      user_type: user.user_type
    });

    // 🚨 Super Admin 檢查 - 必須在所有其他檢查之前
    if (user.role === 'SUPER_ADMIN') {
      console.log('🔥 Super Admin 檢測到，跳過所有權限檢查');
      return next();
    }

    // 檢查路由是否需要權限
    if (!to.meta.requiresAuth) {
      console.log('✅ 路由不需要權限檢查');
      return next();
    }

    // 檢查路由特定權限
    const routePermissionCheck = checkRoutePermissions(to);
    if (!routePermissionCheck.hasPermission) {
      console.log('❌ 路由權限檢查失敗:', routePermissionCheck.reason);
      const notification = useNotification();
      notification.toast.error(getPermissionErrorMessage(routePermissionCheck.reason || 'insufficient_permissions'));
      return next({
        name: 'forbidden',
        query: { 
          reason: routePermissionCheck.reason,
          required: JSON.stringify(routePermissionCheck.requiredPermission)
        }
      });
    }

    // 檢查管理員路徑權限（非 Super Admin）
    const adminPathCheck = checkAdminPathPermissions(to.path);
    if (!adminPathCheck.hasPermission) {
      console.log('❌ 管理員路徑權限檢查失敗:', adminPathCheck.reason);
      const notification = useNotification();
      notification.toast.error(getPermissionErrorMessage(adminPathCheck.reason || 'admin_access_required'));
      return next({
        name: 'forbidden',
        query: { 
          reason: adminPathCheck.reason,
          required: JSON.stringify(adminPathCheck.requiredPermission)
        }
      });
    }

    // 檢查租戶管理員路徑權限（非 Super Admin）
    const tenantAdminPathCheck = checkTenantAdminPathPermissions(to.path);
    if (!tenantAdminPathCheck.hasPermission) {
      console.log('❌ 租戶管理員路徑權限檢查失敗:', tenantAdminPathCheck.reason);
      const notification = useNotification();
      notification.toast.error(getPermissionErrorMessage(tenantAdminPathCheck.reason || 'tenant_admin_required'));
      return next({
        name: 'forbidden',
        query: { 
          reason: tenantAdminPathCheck.reason,
          required: JSON.stringify(tenantAdminPathCheck.requiredPermission)
        }
      });
    }

    // 檢查工作區權限（如果路由包含工作區 ID）
    const workspaceId = to.params.workspaceId as string;
    if (workspaceId) {
      const workspaceCheck = await checkWorkspacePermissions(workspaceId);
      if (!workspaceCheck.hasPermission) {
        console.log('❌ 工作區權限檢查失敗:', workspaceCheck.reason);
        const notification = useNotification();
        notification.toast.error(getPermissionErrorMessage(workspaceCheck.reason || 'workspace_access_denied'));
        return next({
          name: 'forbidden',
          query: { 
            reason: workspaceCheck.reason,
            workspaceId
          }
        });
      }
    }

    console.log('✅ 所有權限檢查通過');
    next();

  } catch (error) {
    console.error('🚨 權限守衛執行錯誤:', error);
    const notification = useNotification();
    notification.toast.error('權限檢查失敗');
    next({
      name: 'error',
      query: { message: '權限檢查失敗' }
    });
  }
};
