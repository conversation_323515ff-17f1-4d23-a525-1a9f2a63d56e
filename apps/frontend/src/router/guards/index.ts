import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { authGuard } from './auth.guard'
import { tenantGuard } from './tenant.guard'
import { permissionGuard } from './permission.guard'

/**
 * 組合所有路由守衛
 * 按順序執行：認證 -> 租戶 -> 權限
 */
export async function combineGuards(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
) {
    // 創建一個包裝的 next 函數來捕獲重定向
    let redirected = false
    const wrappedNext = (result?: any) => {
        if (result !== undefined && result !== true) {
            redirected = true
            next(result)
        }
    }

    // 1. 認證守衛
    await authGuard(to, from, wrappedNext)
    if (redirected) return

    // 只有在路由需要認證時才執行租戶和權限守衛
    if (to.meta.requiresAuth) {
        // 2. 租戶守衛
        await tenantGuard(to, from, wrappedNext)
        if (redirected) return

        // 3. 權限守衛
        await permissionGuard(to, from, wrappedNext)
        if (redirected) return
    }

    // 所有守衛都通過，允許導航
    next()
}

// 導出所有守衛
export { authGuard } from './auth.guard'
export { tenantGuard } from './tenant.guard'
export { permissionGuard } from './permission.guard' 