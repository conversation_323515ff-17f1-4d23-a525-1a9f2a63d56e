import { createRouter, createWebHistory } from "vue-router";
import { useWorkspaceStore } from "@/stores/workspace.store";
import { useErrorStore } from "@/stores/error.store";
import { adminRoutes } from "./admin.routes";
import { useAuth, useAbility, useAuthStore } from "@horizai/auth";
import type { Action, Subject } from "@horizai/auth";
import { wsadminRoutes } from "./wsadmin.routes";
import { workspaceRoutes } from "./workspace.routes";
import NoTenant from "@/views/welcome/NoTenant.vue";
import { authConfig } from "@/config/auth.config";
import { watch } from "vue";
import type { RouteLocationNormalized } from "vue-router";
import {
  getRedirectTarget,
  handleUnauthorizedAccess,
  handleSessionExpired,
  handleForbiddenAccess,
  getDefaultRedirectPath,
} from "@/utils/redirect";
import { combineGuards } from './guards';

// 擴展 meta 類型
declare module "vue-router" {
  interface RouteMeta {
    requiresAuth?: boolean;
    requiresGuest?: boolean;
    permission?: {
      action: Action;
      subject: Subject;
    };
  }
}

// 新增：去重恢復請求，使其在每次新的路由導航會話之間被重置
let hasAttemptedRestore = false;

// 新增：用於跟踪429錯誤的變數
const received429Error = false;
const last429Time = 0;
const ERROR_429_COOLDOWN = 10000; // 10秒冷卻時間

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      name: "home",
      component: () => import("@/views/landing/home/<USER>"),
      meta: { requiresAuth: false, requiresGuest: false },
    },
    {
      path: "/about",
      name: "about",
      component: () => import("@/views/landing/about/AboutView.vue"),
    },
    ...workspaceRoutes,
    {
      path: "/auth",
      component: () => import("@/components/layouts/AuthLayout.vue"),
      meta: { requiresGuest: true },
      children: [
        {
          path: "login",
          name: "login",
          component: () => import("@/views/auth/LoginView.vue"),
        },
        {
          path: "register",
          name: "register",
          component: () => import("@/views/auth/RegisterView.vue"),
        },
        {
          path: "forgot-password",
          name: "forgot-password",
          component: () => import("@/views/auth/ForgotPasswordView.vue"),
        },
        {
          path: "reset-password-sent",
          name: "reset-password-sent",
          component: () => import("@/views/auth/ResetPasswordSentView.vue"),
        },
        {
          path: "reset-password",
          name: "reset-password",
          component: () => import("@/views/auth/ResetPasswordView.vue"),
        },
        {
          path: "email-verification",
          name: "email-verification",
          component: () => import("@/views/auth/EmailVerificationView.vue"),
          meta: { requiresAuth: false, requiresGuest: false },
        },
        {
          path: "company-conflict",
          name: "company-conflict",
          component: () => import("@/views/auth/CompanyConflictView.vue"),
          meta: { requiresAuth: false, requiresGuest: false },
        },
        {
          path: "accept-invitation",
          name: "accept-invitation",
          component: () => import("@/views/auth/AcceptInvitationView.vue"),
          meta: { requiresAuth: false, requiresGuest: false },
        },
      ],
    },
    // 公司設置相關路由 - 需要認證但不是訪客頁面
    {
      path: "/auth/company-setup",
      name: "company-setup",
      component: () => import("@/views/auth/CompanySetupView.vue"),
      meta: { requiresAuth: true, requiresGuest: false },
    },
    {
      path: "/auth/company-setup/create",
      name: "company-setup-create",
      component: () => import("@/views/auth/CreateCompanyView.vue"),
      meta: { requiresAuth: true, requiresGuest: false },
    },
    {
      path: "/auth/company-setup/join",
      name: "company-setup-join",
      component: () => import("@/views/auth/JoinCompanyView.vue"),
      meta: { requiresAuth: true, requiresGuest: false },
    },
    {
      path: "/auth/company-setup/pending",
      name: "company-setup-pending",
      component: () => import("@/views/auth/PendingApprovalView.vue"),
      meta: { requiresAuth: true, requiresGuest: false },
    },
    {
      path: "/forms",
      children: [
        {
          path: ":formId/fill",
          name: "public-form",
          component: () => import("@/views/forms/public/PublicFormView.vue"),
        },
        {
          path: ":formId/thanks",
          name: "form-thanks",
          component: () => import("@/views/forms/thanks/FormThanksView.vue"),
          meta: {
            requiresAuth: false,
            layout: "public",
          },
        },
      ],
    },
    {
      path: "/:pathMatch(.*)*",
      name: "not-found",
      component: () => import("@/views/error/NotFoundView.vue"),
    },
    ...adminRoutes,
    ...wsadminRoutes,
    {
      path: "/forbidden",
      name: "forbidden",
      component: () => import("@/views/error/Forbidden.vue"),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: "/error",
      name: "error",
      component: () => import("@/views/error/Error.vue"),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: "/welcome/no-tenant",
      name: "no-tenant",
      component: NoTenant,
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/welcome/personal",
      name: "personal",
      component: () => import("@/views/welcome/PersonalView.vue"),
      meta: {
        requiresAuth: true,
      },
    },
    {
      path: "/test/auth",
      name: "auth-test",
      component: () => import("@/views/test/AuthTestView.vue"),
      meta: {
        requiresAuth: false,
      },
    },
    {
      path: "/test/websocket",
      name: "websocket-test",
      component: () => import("@/views/test/WebSocketTest.vue"),
      meta: {
        requiresAuth: true,
      },
    },
  ],
});

// 新增全局路由守衛
router.beforeEach(async (to, from, next) => {
  // 頁面切換時重置重複嘗試標記 (保留針對重整的邏輯)
  // 只有當不是從登入頁過來，並且目標不是登入頁時才重置
  if (from.name && to.name !== "login" && from.name !== "login") {
    hasAttemptedRestore = false;
  }

  // 使用組合守衛處理所有路由保護邏輯
  await combineGuards(to, from, next);
});

export default router;
