import { Order } from '@/models/order.model'

/**
 * 計算訂單金額
 * @param planPrice 方案單價
 * @param numberOfSubscribers 訂閱人數
 * @param period 訂閱期間 (月)
 * @returns 訂單總金額
 */
export function calculateOrderAmount(
  planPrice: number,
  numberOfSubscribers: number,
  period: number
): number {
  return planPrice * numberOfSubscribers * period
}

/**
 * 檢查訂閱人數是否超過限制
 * @param currentSubscribers 目前訂閱人數
 * @param maxSubscribers 最大訂閱人數限制
 * @returns 是否超過限制
 */
export function isSubscriberLimitExceeded(
  currentSubscribers: number,
  maxSubscribers: number
): boolean {
  return currentSubscribers > maxSubscribers
}

/**
 * 檢查訂閱期間是否有效
 * @param period 訂閱期間 (月)
 * @returns 是否有效
 */
export function isValidPeriod(period: number): boolean {
  return period > 0 && period <= 12
} 