import { useAuth, useAuthStore } from '@horizai/auth'
import { checkAuthStatus } from './auth-check.utils'

/**
 * 根據用戶角色和狀態獲取預設重定向路徑
 */
export function getDefaultRedirectPath(user: any): string {
    if (!user) {
        return '/auth/login'
    }

    // Super Admin 重定向到系統管理後台
    if (user.role === 'SUPER_ADMIN') {
        return '/admin/dashboard'
    }

    // System Admin 重定向到系統管理後台
    if (user.role === 'SYSTEM_ADMIN') {
        return '/admin/dashboard'
    }

    // 租戶管理員重定向到租戶管理後台
    if (user.role === 'TENANT_ADMIN') {
        return '/tenant-admin/dashboard'
    }

    // 一般用戶重定向到工作區
    return '/workspace'
}

/**
 * 獲取重定向目標路徑
 */
export function getRedirectTarget(
    intendedPath?: string,
    fallbackPath?: string
): string {
    const authStatus = checkAuthStatus()

    // 如果用戶未認證，重定向到登入頁
    if (!authStatus.isAuthenticated) {
        const loginPath = '/auth/login'
        return intendedPath ? `${loginPath}?redirect=${encodeURIComponent(intendedPath)}` : loginPath
    }

    // 如果有預期路徑，使用預期路徑
    if (intendedPath) {
        return intendedPath
    }

    // 如果有備用路徑，使用備用路徑
    if (fallbackPath) {
        return fallbackPath
    }

    // 否則根據用戶角色決定預設路徑
    return getDefaultRedirectPath(authStatus.user)
}

/**
 * 處理未授權訪問
 */
export function handleUnauthorizedAccess(
    intendedPath?: string,
    showNotification = true
): string {
    if (showNotification) {
        // 這裡可以觸發通知
        console.warn('未授權訪問:', intendedPath)
    }

    const loginPath = '/auth/login'
    return intendedPath 
        ? `${loginPath}?redirect=${encodeURIComponent(intendedPath)}`
        : loginPath
}

/**
 * 處理權限不足的訪問
 */
export function handleForbiddenAccess(
    showNotification = true
): string {
    if (showNotification) {
        // 這裡可以觸發通知
        console.warn('權限不足')
    }

    const authStatus = checkAuthStatus()
    return getDefaultRedirectPath(authStatus.user)
}

/**
 * 處理登出重定向
 */
export function handleLogoutRedirect(): string {
    // 清除認證相關資料
    clearAuthData()
    
    return '/auth/login'
}

/**
 * 處理會話過期
 */
export function handleSessionExpired(
    intendedPath?: string,
    showNotification = true
): string {
    if (showNotification) {
        // 這裡可以觸發通知
        console.warn('會話已過期')
    }

    // 清除認證相關資料
    clearAuthData()

    const loginPath = '/auth/login'
    return intendedPath 
        ? `${loginPath}?redirect=${encodeURIComponent(intendedPath)}&reason=expired`
        : `${loginPath}?reason=expired`
}

/**
 * 清除認證相關資料
 */
export function clearAuthData(): void {
    try {
        // 清除 localStorage
        const keysToRemove = [
            'auth_token',
            'user_data',
            'refresh_token',
            'auth_state'
        ]
        
        keysToRemove.forEach(key => {
            localStorage.removeItem(key)
        })

        // 清除 sessionStorage
        sessionStorage.removeItem('temp_auth_data')

        // 清除相關 cookies（如果有的話）
        document.cookie = 'auth_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
        document.cookie = 'refresh_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'

        console.log('✅ 認證資料已清除')
    } catch (error) {
        console.error('清除認證資料失敗:', error)
    }
}
