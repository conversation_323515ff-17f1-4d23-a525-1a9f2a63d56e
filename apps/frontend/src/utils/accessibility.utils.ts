/**
 * 無障礙工具函數
 * 提供常用的無障礙功能和檢查
 */

/**
 * 生成唯一的 ID，用於 aria-labelledby 等屬性
 */
export function generateId(prefix: string = 'id'): string {
    return `${prefix}-${Math.random().toString(36).substr(2, 9)}`
}

/**
 * 檢查元素是否可聚焦
 */
export function isFocusable(element: HTMLElement): boolean {
    if (element.hasAttribute('disabled') || element.getAttribute('tabindex') === '-1') {
        return false
    }

    const focusableSelectors = [
        'button',
        'input',
        'select',
        'textarea',
        'a[href]',
        '[tabindex]:not([tabindex="-1"])',
        '[contenteditable="true"]'
    ]

    return focusableSelectors.some(selector => element.matches(selector))
}

/**
 * 獲取元素的可訪問名稱
 */
export function getAccessibleName(element: HTMLElement): string {
    // 檢查 aria-label
    const ariaLabel = element.getAttribute('aria-label')
    if (ariaLabel) return ariaLabel

    // 檢查 aria-labelledby
    const labelledBy = element.getAttribute('aria-labelledby')
    if (labelledBy) {
        const labelElement = document.getElementById(labelledBy)
        if (labelElement) return labelElement.textContent || ''
    }

    // 檢查關聯的 label 元素
    if (element.id) {
        const label = document.querySelector(`label[for="${element.id}"]`)
        if (label) return label.textContent || ''
    }

    // 檢查父級 label
    const parentLabel = element.closest('label')
    if (parentLabel) return parentLabel.textContent || ''

    // 檢查 title 屬性
    const title = element.getAttribute('title')
    if (title) return title

    // 檢查 placeholder（僅作為最後手段）
    const placeholder = element.getAttribute('placeholder')
    if (placeholder) return placeholder

    // 返回元素的文本內容
    return element.textContent || ''
}

/**
 * 檢查顏色對比度是否符合 WCAG 標準
 */
export function checkColorContrast(
    foreground: string,
    background: string,
    level: 'AA' | 'AAA' = 'AA'
): boolean {
    // 這是一個簡化的實現，實際應用中可能需要更完整的顏色對比度計算
    const getLuminance = (color: string): number => {
        // 簡化的亮度計算
        const rgb = color.match(/\d+/g)
        if (!rgb) return 0

        const [r, g, b] = rgb.map(Number)
        return (0.299 * r + 0.587 * g + 0.114 * b) / 255
    }

    const foregroundLuminance = getLuminance(foreground)
    const backgroundLuminance = getLuminance(background)

    const contrast = Math.abs(foregroundLuminance - backgroundLuminance)

    return level === 'AA' ? contrast >= 0.5 : contrast >= 0.7
}

/**
 * 設置焦點到指定元素，並確保元素可見
 */
export function setFocusWithScroll(element: HTMLElement): void {
    element.focus()

    // 確保元素在視窗中可見
    element.scrollIntoView({
        behavior: 'smooth',
        block: 'nearest',
        inline: 'nearest'
    })
}

/**
 * 創建可訪問的錯誤訊息
 */
export function createAccessibleError(
    inputId: string,
    errorMessage: string
): HTMLElement {
    const errorId = `${inputId}-error`
    const errorElement = document.createElement('div')

    errorElement.id = errorId
    errorElement.className = 'text-sm text-destructive mt-1'
    errorElement.setAttribute('role', 'alert')
    errorElement.setAttribute('aria-live', 'polite')
    errorElement.textContent = errorMessage

    // 將錯誤 ID 添加到輸入元素的 aria-describedby
    const inputElement = document.getElementById(inputId)
    if (inputElement) {
        const describedBy = inputElement.getAttribute('aria-describedby')
        const newDescribedBy = describedBy ? `${describedBy} ${errorId}` : errorId
        inputElement.setAttribute('aria-describedby', newDescribedBy)
    }

    return errorElement
}

/**
 * 移除可訪問的錯誤訊息
 */
export function removeAccessibleError(inputId: string): void {
    const errorId = `${inputId}-error`
    const errorElement = document.getElementById(errorId)

    if (errorElement) {
        errorElement.remove()

        // 從輸入元素的 aria-describedby 中移除錯誤 ID
        const inputElement = document.getElementById(inputId)
        if (inputElement) {
            const describedBy = inputElement.getAttribute('aria-describedby')
            if (describedBy) {
                const newDescribedBy = describedBy
                    .split(' ')
                    .filter(id => id !== errorId)
                    .join(' ')

                if (newDescribedBy) {
                    inputElement.setAttribute('aria-describedby', newDescribedBy)
                } else {
                    inputElement.removeAttribute('aria-describedby')
                }
            }
        }
    }
}

/**
 * 檢查是否使用螢幕閱讀器
 */
export function isUsingScreenReader(): boolean {
    // 檢查是否有螢幕閱讀器相關的媒體查詢
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches ||
        window.navigator.userAgent.includes('NVDA') ||
        window.navigator.userAgent.includes('JAWS') ||
        window.navigator.userAgent.includes('VoiceOver')
}

/**
 * 檢查是否偏好減少動畫
 */
export function prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
}

/**
 * 檢查是否偏好高對比度
 */
export function prefersHighContrast(): boolean {
    return window.matchMedia('(prefers-contrast: high)').matches
}

/**
 * 為表單元素添加無障礙屬性
 */
export function enhanceFormAccessibility(form: HTMLFormElement): void {
    const inputs = form.querySelectorAll('input, select, textarea')

    inputs.forEach((input) => {
        const element = input as HTMLElement

        // 確保每個輸入都有 ID
        if (!element.id) {
            element.id = generateId('input')
        }

        // 檢查是否有關聯的標籤
        const hasLabel = document.querySelector(`label[for="${element.id}"]`) ||
            element.closest('label')

        if (!hasLabel && !element.getAttribute('aria-label')) {
            console.warn(`Input ${element.id} 缺少可訪問的標籤`)
        }

        // 為必填欄位添加 aria-required
        if (element.hasAttribute('required')) {
            element.setAttribute('aria-required', 'true')
        }
    })
}

/**
 * 公告頁面變更給螢幕閱讀器
 */
export function announcePageChange(pageName: string): void {
    const announcement = document.createElement('div')
    announcement.setAttribute('aria-live', 'polite')
    announcement.setAttribute('aria-atomic', 'true')
    announcement.className = 'sr-only'
    announcement.textContent = `已導航到 ${pageName} 頁面`

    document.body.appendChild(announcement)

    // 清理公告元素
    setTimeout(() => {
        document.body.removeChild(announcement)
    }, 1000)
} 