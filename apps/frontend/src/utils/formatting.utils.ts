import { CalendarDate } from '@internationalized/date'


/**
 * 格式化貨幣顯示
 * @param price - 價格數值
 * @returns 格式化後的貨幣字串
 */
export const formatCurrency = (price: unknown): string => {
  const num = Number(price)
  if (isNaN(num) || price == null) return 'NT$0'
  return new Intl.NumberFormat('zh-TW', {
    style: 'currency',
    currency: 'TWD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(num)
}


/**
 * 格式化儲存空間
 * @param gb - GB為單位的儲存空間
 * @returns 格式化後的儲存空間字串
 */
export const formatStorage = (gb: unknown): string => {
  const num = Number(gb)
  if (isNaN(num) || gb == null) return '0 GB'
  if (num >= 1024) return `${(num / 1024).toFixed(1)} TB`
  if (num < 1 && num > 0) return `${Math.round(num * 1024)} MB`
  return `${num} GB`
}


/**
 * 格式化數字
 * @param num - 數字
 * @returns 格式化後的數字字串
 */
export const formatNumber = (num: unknown): string => {
  const n = Number(num)
  if (isNaN(n) || num == null) return '0'
  return new Intl.NumberFormat('zh-TW', {
    style: 'decimal',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(n)
}

/**
 * 格式化百分比
 * @param value - 數值 (0-1 或 0-100)
 * @param isDecimal - 是否為小數形式 (0-1)，預設為 false (0-100)
 * @returns 格式化後的百分比字串
 */
export const formatPercent = (value: unknown, isDecimal: boolean = false): string => {
  const num = Number(value)
  if (isNaN(num) || value == null) return '0%'

  const percentage = isDecimal ? num * 100 : num
  return new Intl.NumberFormat('zh-TW', {
    style: 'percent',
    minimumFractionDigits: 0,
    maximumFractionDigits: 1
  }).format(percentage / 100)
}


/**
 * 格式化日期
 * @param dateString - 日期字串
 * @returns 格式化後的日期字串
 */
export const formatDate = (dateString: string): string => {
  if (!dateString) return ''
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return '無效日期'
    return date.toLocaleDateString('zh-TW', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  } catch (e) {
    console.error('Error formatting date:', e)
    return '格式錯誤'
  }
}


/**
 * 解析日期字串為 CalendarDate 物件
 * @param dateString - 日期字串
 * @returns CalendarDate 物件或 undefined
 */
export const parseDate = (dateString: string | undefined): CalendarDate | undefined => {
  if (!dateString) return undefined
  try {
    const date = new Date(dateString)
    if (isNaN(date.getTime())) return undefined
    return new CalendarDate(
      date.getFullYear(),
      date.getMonth() + 1,
      date.getDate()
    )
  } catch (e) {
    console.error('Error parsing date:', e)
    return undefined
  }
}

// 導出所有格式化工具的物件
export const formatters = {
  currency: formatCurrency,
  storage: formatStorage,
  number: formatNumber,
  percent: formatPercent,
  date: formatDate,
  parseDate: parseDate
} 
