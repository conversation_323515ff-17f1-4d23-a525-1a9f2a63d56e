/**
 * 組件驗證工具
 * 用於驗證組件的功能性、無障礙性和響應式設計
 */

export interface ValidationResult {
    passed: boolean
    message: string
    severity: 'error' | 'warning' | 'info'
}

export interface ComponentValidationReport {
    componentName: string
    passed: boolean
    results: ValidationResult[]
    score: number
}

/**
 * 驗證組件的無障礙性
 */
export function validateAccessibility(element: HTMLElement): ValidationResult[] {
    const results: ValidationResult[] = []

    // 檢查是否有適當的 ARIA 標籤
    const hasAriaLabel = element.hasAttribute('aria-label') ||
        element.hasAttribute('aria-labelledby')

    if (!hasAriaLabel && element.tagName !== 'DIV' && element.tagName !== 'SPAN') {
        results.push({
            passed: false,
            message: '元素缺少 aria-label 或 aria-labelledby 屬性',
            severity: 'warning'
        })
    }

    // 檢查按鈕是否可聚焦
    if (element.tagName === 'BUTTON' && element.hasAttribute('disabled')) {
        const hasAriaDisabled = element.hasAttribute('aria-disabled')
        if (!hasAriaDisabled) {
            results.push({
                passed: false,
                message: '禁用的按鈕應該有 aria-disabled 屬性',
                severity: 'warning'
            })
        }
    }

    // 檢查圖片是否有 alt 屬性
    if (element.tagName === 'IMG') {
        const hasAlt = element.hasAttribute('alt')
        if (!hasAlt) {
            results.push({
                passed: false,
                message: '圖片缺少 alt 屬性',
                severity: 'error'
            })
        }
    }

    // 檢查表單元素是否有標籤
    if (['INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName)) {
        const hasLabel = document.querySelector(`label[for="${element.id}"]`) ||
            element.closest('label') ||
            element.hasAttribute('aria-label')

        if (!hasLabel) {
            results.push({
                passed: false,
                message: '表單元素缺少關聯的標籤',
                severity: 'error'
            })
        }
    }

    // 檢查顏色對比度（簡化版）
    const computedStyle = window.getComputedStyle(element)
    const color = computedStyle.color
    const backgroundColor = computedStyle.backgroundColor

    if (color && backgroundColor && color !== 'rgba(0, 0, 0, 0)' && backgroundColor !== 'rgba(0, 0, 0, 0)') {
        // 這裡可以實現更複雜的對比度檢查
        results.push({
            passed: true,
            message: '顏色對比度檢查通過',
            severity: 'info'
        })
    }

    return results
}

/**
 * 驗證響應式設計
 */
export function validateResponsiveDesign(element: HTMLElement): ValidationResult[] {
    const results: ValidationResult[] = []

    // 檢查是否有響應式類名
    const classList = Array.from(element.classList)
    const hasResponsiveClasses = classList.some(className =>
        /^(sm|md|lg|xl|2xl):/.test(className)
    )

    if (!hasResponsiveClasses) {
        results.push({
            passed: false,
            message: '元素缺少響應式類名',
            severity: 'warning'
        })
    } else {
        results.push({
            passed: true,
            message: '元素包含響應式類名',
            severity: 'info'
        })
    }

    // 檢查是否有適當的斷點
    const breakpoints = ['sm', 'md', 'lg', 'xl', '2xl']
    const usedBreakpoints = breakpoints.filter(bp =>
        classList.some(className => className.startsWith(`${bp}:`))
    )

    if (usedBreakpoints.length > 0) {
        results.push({
            passed: true,
            message: `使用了以下斷點: ${usedBreakpoints.join(', ')}`,
            severity: 'info'
        })
    }

    return results
}

/**
 * 驗證組件性能
 */
export function validatePerformance(element: HTMLElement): ValidationResult[] {
    const results: ValidationResult[] = []

    // 檢查圖片懶載入
    const images = element.querySelectorAll('img')
    images.forEach(img => {
        const hasLazyLoading = img.hasAttribute('loading') && img.getAttribute('loading') === 'lazy'
        if (!hasLazyLoading) {
            results.push({
                passed: false,
                message: '圖片未啟用懶載入',
                severity: 'warning'
            })
        }
    })

    // 檢查是否有過多的 DOM 節點
    const nodeCount = element.querySelectorAll('*').length
    if (nodeCount > 100) {
        results.push({
            passed: false,
            message: `DOM 節點過多 (${nodeCount})，可能影響性能`,
            severity: 'warning'
        })
    }

    return results
}

/**
 * 驗證組件功能性
 */
export function validateFunctionality(element: HTMLElement): ValidationResult[] {
    const results: ValidationResult[] = []

    // 檢查按鈕是否有點擊事件
    const buttons = element.querySelectorAll('button')
    buttons.forEach(button => {
        const hasClickHandler = button.onclick !== null ||
            button.hasAttribute('@click') ||
            button.hasAttribute('v-on:click')

        if (!hasClickHandler && !button.hasAttribute('type')) {
            results.push({
                passed: false,
                message: '按鈕缺少點擊事件處理器',
                severity: 'warning'
            })
        }
    })

    // 檢查表單驗證
    const forms = element.querySelectorAll('form')
    forms.forEach(form => {
        const hasValidation = form.hasAttribute('novalidate') === false
        if (!hasValidation) {
            results.push({
                passed: true,
                message: '表單啟用了瀏覽器驗證',
                severity: 'info'
            })
        }
    })

    return results
}

/**
 * 綜合驗證組件
 */
export function validateComponent(
    element: HTMLElement,
    componentName: string
): ComponentValidationReport {
    const allResults: ValidationResult[] = []

    // 執行各種驗證
    allResults.push(...validateAccessibility(element))
    allResults.push(...validateResponsiveDesign(element))
    allResults.push(...validatePerformance(element))
    allResults.push(...validateFunctionality(element))

    // 計算分數
    const totalTests = allResults.length
    const passedTests = allResults.filter(r => r.passed).length
    const score = totalTests > 0 ? Math.round((passedTests / totalTests) * 100) : 0

    // 判斷是否通過
    const hasErrors = allResults.some(r => r.severity === 'error')
    const passed = !hasErrors && score >= 70

    return {
        componentName,
        passed,
        results: allResults,
        score
    }
}

/**
 * 批量驗證頁面中的所有組件
 */
export function validatePage(): ComponentValidationReport[] {
    const reports: ComponentValidationReport[] = []

    // 查找所有可能的組件
    const components = document.querySelectorAll('[data-component]')

    components.forEach(element => {
        const componentName = element.getAttribute('data-component') || 'Unknown'
        const report = validateComponent(element as HTMLElement, componentName)
        reports.push(report)
    })

    return reports
}

/**
 * 生成驗證報告
 */
export function generateValidationReport(reports: ComponentValidationReport[]): string {
    let report = '# 組件驗證報告\n\n'

    const totalComponents = reports.length
    const passedComponents = reports.filter(r => r.passed).length
    const averageScore = reports.reduce((sum, r) => sum + r.score, 0) / totalComponents

    report += `## 總覽\n`
    report += `- 總組件數: ${totalComponents}\n`
    report += `- 通過驗證: ${passedComponents}\n`
    report += `- 平均分數: ${averageScore.toFixed(1)}%\n\n`

    reports.forEach(componentReport => {
        report += `## ${componentReport.componentName}\n`
        report += `- 狀態: ${componentReport.passed ? '✅ 通過' : '❌ 未通過'}\n`
        report += `- 分數: ${componentReport.score}%\n\n`

        if (componentReport.results.length > 0) {
            report += `### 詳細結果\n`
            componentReport.results.forEach(result => {
                const icon = result.passed ? '✅' :
                    result.severity === 'error' ? '❌' :
                        result.severity === 'warning' ? '⚠️' : 'ℹ️'
                report += `${icon} ${result.message}\n`
            })
            report += '\n'
        }
    })

    return report
}

/**
 * 自動驗證工具
 */
export class ComponentValidator {
    private reports: ComponentValidationReport[] = []

    /**
     * 驗證單個組件
     */
    validateComponent(element: HTMLElement, name: string): ComponentValidationReport {
        const report = validateComponent(element, name)
        this.reports.push(report)
        return report
    }

    /**
     * 驗證整個頁面
     */
    validatePage(): ComponentValidationReport[] {
        this.reports = validatePage()
        return this.reports
    }

    /**
     * 獲取報告
     */
    getReports(): ComponentValidationReport[] {
        return this.reports
    }

    /**
     * 生成報告
     */
    generateReport(): string {
        return generateValidationReport(this.reports)
    }

    /**
     * 清除報告
     */
    clearReports(): void {
        this.reports = []
    }
} 