/**
 * CASL 權限系統統一匯出
 * 提供完整的權限管理功能
 */

// 核心工廠和能力相關
export { CaslAbilityFactory } from './ability/casl-ability.factory';
export { CaslModule } from './casl.module';

// 工具函數
export * from './utils';

// 守衛
export { PoliciesGuard } from './guards/permission.guard';

// 裝飾器
export {
  CheckPolicies,
  CHECK_POLICIES_KEY,
  RequirePermissions,
  RequirePermission,
  RequireManage,
  RequireRead,
  RequireCreate,
  RequireUpdate,
  RequireDelete,
  RequireAccess,
  REQUIRE_PERMISSIONS_KEY,
  type PermissionRequirement,
} from './decorators/check-policies.decorator';

// 中介軟體和租戶上下文
export {
  TenantContextMiddleware,
  TenantContext,
  TenantIsolated,
  CrossTenant,
  TENANT_CONTEXT_KEY,
  type TenantContextConfig,
} from './middleware/tenant-context.middleware';

// 服務
export {
  PermissionCheckerService,
  type PermissionCheckResult,
  type BatchPermissionCheckResult,
} from './services/permission-checker.service';

// 介面
export {
  PolicyHandler,
  IPolicyHandler,
  PolicyHandlerCallback,
} from './interfaces/policy-handler.interface';

// 類型
export {
  AppAbility,
  Actions,
  Subjects,
  UserRole,
  type AppAbilityRule,
  type IPermissionRule,
  type Permission,
  type UserPermissions,
  type PermissionCheck,
  type CheckPermission,
  type PermissionConfig,
  DEFAULT_ROLE_PERMISSIONS,
} from '../types/models/casl.model';
