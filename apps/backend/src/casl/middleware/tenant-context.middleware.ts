import {
  Injectable,
  NestMiddleware,
  Logger,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { Request, Response, NextFunction } from 'express';
import { JwtUser } from '../../types/jwt-user.type';

/**
 * 租戶上下文驗證中介軟體
 * 確保多租戶環境下的資源隔離
 */
@Injectable()
export class TenantContextMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantContextMiddleware.name);

  use(req: Request, res: Response, next: NextFunction) {
    const user = req.user as JwtUser;

    // 如果沒有用戶資訊，跳過驗證（由認證守衛處理）
    if (!user) {
      return next();
    }

    try {
      this.validateTenantContext(req, user);
      next();
    } catch (error) {
      this.logger.error(`Tenant context validation failed for user ${user.id}:`, error.message);
      throw error;
    }
  }

  /**
   * 驗證租戶上下文
   */
  private validateTenantContext(req: Request, user: JwtUser): void {
    const { method, path, params, query, body } = req;

    // 系統用戶可以訪問所有租戶資源
    if (user.user_type === 'system') {
      this.logger.debug(
        `System user ${user.id} accessing ${method} ${path} - tenant validation skipped`,
      );
      return;
    }

    // 租戶用戶必須有租戶 ID
    if (user.user_type === 'tenant' && !user.tenant_id) {
      throw new ForbiddenException('租戶用戶缺少租戶資訊');
    }

    // 檢查路由參數中的租戶 ID
    const routeTenantId = params.tenant_id || query.tenant_id;
    if (routeTenantId && user.tenant_id && routeTenantId !== user.tenant_id) {
      this.logger.warn(
        `Tenant access violation: user ${user.id} (tenant: ${user.tenant_id}) ` +
          `attempting to access tenant ${routeTenantId} via ${method} ${path}`,
      );
      throw new ForbiddenException('無權訪問此租戶的資源');
    }

    // 檢查 request body 中的租戶 ID (適用於 POST, PUT, PATCH)
    if (['POST', 'PUT', 'PATCH'].includes(method) && body) {
      const tenantFields = ['tenant_id', 'tenantId'];

      for (const field of tenantFields) {
        if (body[field] && user.tenant_id && body[field] !== user.tenant_id) {
          this.logger.warn(
            `Tenant mismatch in request body: user ${user.id} (tenant: ${user.tenant_id}) ` +
              `trying to set ${field} to ${body[field]}`,
          );
          throw new BadRequestException(`請求內文的租戶 ID 不匹配`);
        }
      }
    }

    this.logger.debug(
      `Tenant context validated for user ${user.id} (tenant: ${user.tenant_id}) ` +
        `accessing ${method} ${path}`,
    );
  }
}

/**
 * 租戶上下文裝飾器
 * 用於標記需要租戶上下文驗證的路由
 */
export const TENANT_CONTEXT_KEY = 'tenant_context';

/**
 * 租戶上下文配置介面
 */
export interface TenantContextConfig {
  /**
   * 是否強制要求租戶 ID
   */
  required?: boolean;

  /**
   * 允許的租戶 ID 來源
   */
  sources?: ('params' | 'query' | 'body' | 'header')[];

  /**
   * 自訂驗證函數
   */
  validator?: (req: Request, user: JwtUser) => boolean | Promise<boolean>;
}

/**
 * 租戶上下文裝飾器
 *
 * @param config 租戶上下文配置
 *
 * @example
 * ```typescript
 * @TenantContext({ required: true, sources: ['params'] })
 * async getTenantData(@Param('tenant_id') tenantId: string) { ... }
 * ```
 */
export const TenantContext = (config: TenantContextConfig = {}) => {
  return (target: any, propertyKey: string, descriptor: PropertyDescriptor) => {
    Reflect.defineMetadata(TENANT_CONTEXT_KEY, config, descriptor.value);
    return descriptor;
  };
};

/**
 * 租戶隔離裝飾器
 * 確保租戶用戶只能訪問自己租戶的資源
 *
 * @example
 * ```typescript
 * @TenantIsolated()
 * async getUserData() { ... }
 * ```
 */
export const TenantIsolated = () => TenantContext({ required: true });

/**
 * 跨租戶訪問裝飾器
 * 允許系統用戶訪問任何租戶的資源
 *
 * @example
 * ```typescript
 * @CrossTenant()
 * async getAnyTenantData() { ... }
 * ```
 */
export const CrossTenant = () => TenantContext({ required: false });
