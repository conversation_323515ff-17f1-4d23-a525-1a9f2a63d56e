import { MongoQuery } from '@casl/ability';
import { Prisma } from '@prisma/client';
import { JwtUser } from '../../types/jwt-user.type';

/**
 * 用戶上下文介面
 */
export interface UserContext {
  id: string;
  tenant_id?: string | null;
  userType?: string;
  role?: string;
  [key: string]: any;
}

/**
 * 處理權限條件中的動態參數
 * 將條件中的特殊變數 (如 ${user.tenant_id}) 替換為實際值
 *
 * @param conditions 原始條件
 * @param userContext 用戶上下文
 * @returns 處理後的條件
 */
export function processConditions(
  conditions: Prisma.JsonValue | null | undefined,
  userContext: UserContext,
): MongoQuery | undefined {
  if (!conditions || typeof conditions !== 'object' || Array.isArray(conditions)) {
    return undefined;
  }

  const processed: MongoQuery = {};
  for (const key in conditions) {
    if (Object.prototype.hasOwnProperty.call(conditions, key)) {
      let value = (conditions as Prisma.JsonObject)[key];

      if (typeof value === 'string') {
        value = replaceVariables(value, userContext);
      } else if (value && typeof value === 'object') {
        value = processConditions(value, userContext) as any;
      }

      processed[key] = value;
    }
  }
  return Object.keys(processed).length > 0 ? processed : undefined;
}

/**
 * 替換字串中的變數
 *
 * @param str 包含變數的字串
 * @param userContext 用戶上下文
 * @returns 替換後的值
 */
export function replaceVariables(str: string, userContext: UserContext): any {
  if (str === '${user.tenant_id}') {
    return userContext.tenant_id ?? null;
  } else if (str === '${user.id}') {
    return userContext.id;
  }
  // Add more replacements if needed, e.g, for user roles or other context
  return str;
}

/**
 * Guard 專用的條件處理函數
 * 處理 Guard 中使用的條件格式
 *
 * @param conditions Guard 條件
 * @param user JWT 用戶
 * @returns 處理後的條件
 */
export function processConditionsForGuard(
  conditions: Record<string, any> | undefined,
  user: JwtUser,
): Record<string, any> | undefined {
  if (!conditions) {
    return undefined;
  }

  const processedConditions = { ...conditions };

  // 遞迴處理條件中的變數
  const replaceVariables = (obj: any): any => {
    if (!obj || typeof obj !== 'object') {
      return obj;
    }

    if (Array.isArray(obj)) {
      return obj.map(replaceVariables);
    }

    const result: Record<string, any> = {};

    for (const key in obj) {
      const value = obj[key];

      if (typeof value === 'string') {
        // 替換變數
        if (value === '${user.id}') {
          result[key] = user.id;
        } else if (value === '${user.tenant_id}') {
          result[key] = user.tenant_id;
        } else if (value === '${user_id}') {
          result[key] = user.id;
        } else if (value === '${tenant_id}') {
          result[key] = user.tenant_id;
        } else {
          result[key] = value;
        }
      } else if (value && typeof value === 'object') {
        result[key] = replaceVariables(value);
      } else {
        result[key] = value;
      }
    }

    return result;
  };

  return replaceVariables(processedConditions);
}
