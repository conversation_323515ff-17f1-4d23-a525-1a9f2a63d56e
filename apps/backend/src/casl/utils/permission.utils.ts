import { AppAbility, Actions, Subjects } from '../../types/models/casl.model';
import { Logger } from '@nestjs/common';

/**
 * 權限檢查工具函數
 */

const logger = new Logger('PermissionUtils');

/**
 * 執行實際的權限檢查
 * 從 PermissionCheckerService 中抽離出來的純函數
 *
 * @param ability CASL Ability 實例
 * @param action 操作動作
 * @param subject 資源主體（可以是字串或物件實例）
 * @param conditions 可選的條件
 * @param fields 可選的欄位限制
 * @returns 是否有權限
 */
export function performPermissionCheck(
  ability: AppAbility,
  action: Actions,
  subject: Subjects | object,
  conditions?: any,
  fields?: string[],
): boolean {
  try {
    // 若傳入實例物件，直接檢查
    if (typeof subject === 'object' && subject !== null) {
      if (fields?.length) {
        return fields.every((field) => ability.can(action, subject as any, field));
      }
      return ability.can(action, subject as any);
    }

    // 原有 string subject 流程
    const subjectType = subject as Subjects;

    if (fields && fields.length > 0) {
      // 檢查欄位級權限
      return fields.every((field) => ability.can(action, subjectType, field));
    } else if (conditions) {
      // 檢查條件權限
      return ability.can(action, subjectType, conditions);
    } else {
      // 檢查基本權限
      return ability.can(action, subjectType);
    }
  } catch (error: any) {
    logger.error(
      `Error performing permission check ${action}:${JSON.stringify(subject)}:`,
      error.message,
    );
    return false;
  }
}

/**
 * 檢查用戶是否為超級管理員
 *
 * @param userType 用戶類型
 * @param role 用戶角色
 * @returns 是否為超級管理員
 */
export function isSuperAdmin(userType?: string, role?: string): boolean {
  return userType === 'system' && role === 'SUPER_ADMIN';
}

/**
 * 檢查用戶是否為系統管理員
 *
 * @param userType 用戶類型
 * @param role 用戶角色
 * @returns 是否為系統管理員
 */
export function isSystemAdmin(userType?: string, role?: string): boolean {
  return userType === 'system' && ['SUPER_ADMIN', 'SYSTEM_ADMIN'].includes(role || '');
}

/**
 * 檢查用戶是否為租戶管理員
 *
 * @param userType 用戶類型
 * @param role 用戶角色
 * @returns 是否為租戶管理員
 */
export function isTenantAdmin(userType?: string, role?: string): boolean {
  return userType === 'tenant' && ['TENANT_ADMIN'].includes(role || '');
}

/**
 * 檢查是否有任何權限定義
 *
 * @param policyHandlers 策略處理器陣列
 * @param permissionRequirements 權限要求陣列
 * @returns 是否有權限檢查定義
 */
export function hasPermissionChecks(
  policyHandlers?: any[],
  permissionRequirements?: any[],
): boolean {
  return Boolean(
    (policyHandlers && policyHandlers.length > 0) ||
      (permissionRequirements && permissionRequirements.length > 0),
  );
}
