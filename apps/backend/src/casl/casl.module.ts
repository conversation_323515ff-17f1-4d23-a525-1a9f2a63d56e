import { Module, Global } from '@nestjs/common';
import { PrismaModule } from '../modules/core/prisma/prisma.module';
import { CaslAbilityFactory } from './ability/casl-ability.factory';
import { PermissionCheckerService } from './services/permission-checker.service';
import { PoliciesGuard } from './guards/permission.guard';
import { TenantContextMiddleware } from './middleware/tenant-context.middleware';

@Global()
@Module({
  imports: [PrismaModule],
  providers: [CaslAbilityFactory, PermissionCheckerService, PoliciesGuard, TenantContextMiddleware],
  exports: [CaslAbilityFactory, PermissionCheckerService, PoliciesGuard, TenantContextMiddleware],
})
export class CaslModule {}
