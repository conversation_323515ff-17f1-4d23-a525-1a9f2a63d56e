import { MulterModuleOptions } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { extname, join } from 'path';
import { v4 as uuidv4 } from 'uuid';
import { BadRequestException } from '@nestjs/common';

export const multerConfig: MulterModuleOptions = {
  storage: diskStorage({
    destination: (req, file, cb) => {
      // 暫存目錄，實際檔案會由 StorageService 處理
      const uploadPath = join(process.cwd(), 'uploads', 'temp');
      cb(null, uploadPath);
    },
    filename: (req, file, cb) => {
      // 產生唯一檔名
      const uniqueSuffix = `${Date.now()}-${uuidv4()}`;
      const ext = extname(file.originalname);
      cb(null, `${uniqueSuffix}${ext}`);
    },
  }),
  limits: {
    fileSize: 100 * 1024 * 1024, // 100MB 預設限制，實際限制由系統設定決定
  },
  fileFilter: (req, file, cb) => {
    // 基本檔案類型檢查，詳細檢查由 FilesService 處理
    if (
      !file.originalname.match(
        /\.(jpg|jpeg|png|gif|pdf|doc|docx|xls|xlsx|ppt|pptx|txt|zip|rar|mp4|mov|avi|mp3|wav)$/i,
      )
    ) {
      return cb(new BadRequestException('Invalid file type'), false);
    }
    cb(null, true);
  },
};
