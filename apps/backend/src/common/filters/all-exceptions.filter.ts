import { ExceptionFilter, Catch, ArgumentsHost, HttpException, Injectable, HttpStatus } from '@nestjs/common';
import { Request, Response } from 'express';
import { SystemLogService } from '../services/system-log.service';

function structuredLog({
  level,
  context,
  action,
  message,
  user,
  meta,
}: {
  level: string;
  context: string;
  action: string;
  message: string;
  user?: any;
  meta?: any;
}) {
  const log = {
    timestamp: new Date().toISOString(),
    level,
    context,
    action,
    message,
    ...(user ? { user } : {}),
    ...(meta ? { meta } : {}),
  };
  console.log(JSON.stringify(log));
}

@Injectable()
@Catch()
export class AllExceptionsFilter implements ExceptionFilter {
  constructor(private readonly systemLogService: SystemLogService) {}

  async catch(exception: any, host: ArgumentsHost) {
    const ctx = host.switchToHttp();
    const response = ctx.getResponse<Response>();
    const request = ctx.getRequest<Request>();
    const user = request.user as any; // 取得經過身份驗證的使用者資訊

    const status =
      exception instanceof HttpException
        ? exception.getStatus()
        // 根據 Prisma 錯誤代碼決定 HTTP 狀態
        : exception.code === 'P2002' 
          ? HttpStatus.CONFLICT // 唯一約束衝突
          : exception.code === 'P2025'
            ? HttpStatus.NOT_FOUND // 找不到記錄
            : HttpStatus.INTERNAL_SERVER_ERROR;

    const message =
      exception instanceof HttpException
        ? (exception.getResponse() as any)?.message || exception.message
        : '內部伺服器錯誤';
    
    const errors = 
      exception instanceof HttpException 
        ? (exception.getResponse() as any)?.errors || [] 
        : [];

    const responseBody = {
      statusCode: status,
      message: Array.isArray(message) ? message.join(', ') : message,
      errors: errors,
      timestamp: new Date().toISOString(),
      path: request.url,
    };

    // 只有在非 404/401/403 錯誤時才記錄日誌，以減少噪音
    if (status >= 500 || (status >= 400 && ![401, 403, 404].includes(status))) {
      try {
        await this.systemLogService.logError({
          level: 'error',
          message: `[API Error] ${exception.message}`,
          stack: exception.stack,
          path: request.url,
          method: request.method,
          userId: user ? user.id : undefined,
          ip: request.ip,
          details: {
            // 包含額外的請求和使用者資訊
            headers: request.headers,
            body: request.body,
            user: user ? { id: user.id, email: user.email } : undefined,
          }
        });
      } catch (logError) {
        console.error('--- FAILED TO WRITE TO SYSTEM LOG ---');
        console.error(logError);
        console.error('--- ORIGINAL EXCEPTION ---');
        console.error(exception);
        console.error('------------------------------------');
      }
    }

    response.status(status).json(responseBody);
  }
}
