import { Injectable, NestInterceptor, ExecutionContext, CallHandler } from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { throwError } from 'rxjs';
import { SystemLogService } from '../services/system-log.service';

@Injectable()
export class LoggingInterceptor implements NestInterceptor {
  constructor(private readonly systemLogService: SystemLogService) {}

  intercept(context: ExecutionContext, next: CallHandler): any {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    const method = request.method;

    // 只紀錄可能修改資料的請求
    if (!['POST', 'PUT', 'DELETE', 'PATCH'].includes(method)) {
      return next.handle();
    }

    const url = request.url;
    const userId = request.user?.id;
    const ip = request.ip || request.connection?.remoteAddress || request.socket?.remoteAddress;
    const userAgent = request.get('User-Agent');
    const startTime = Date.now();

    return next.handle().pipe(
      tap({
        next: async (data) => {
          const endTime = Date.now();
          const duration = endTime - startTime;

          try {
            await this.systemLogService.logAudit({
              message: `${method} ${url} - 成功 (${duration}ms)`,
              user_id: userId,
              ip,
              status: 'SUCCESS',
              path: url,
              method,
              action: 'API_REQUEST',
              details: {
                userAgent,
                requestBody: this.sanitizeRequestBody(request.body),
                responseStatusCode: response.statusCode,
                executionTime: duration,
              },
            });
          } catch (e) {
            console.error('記錄稽核日誌失敗:', e);
          }
        },
      }),
      catchError(async (error) => {
        const endTime = Date.now();
        const duration = endTime - startTime;

        try {
          await this.systemLogService.logAudit({
            message: `${method} ${url} - 失敗 (${duration}ms): ${error.message || '未知錯誤'}`,
            user_id: userId,
            ip,
            status: 'ERROR',
            path: url,
            method,
            action: 'API_REQUEST',
            error_message: error.message,
            details: {
              userAgent,
              requestBody: this.sanitizeRequestBody(request.body),
              responseStatusCode: error.status || 500,
              executionTime: duration,
              errorStack: error.stack,
            },
          });
        } catch (e) {
          console.error('記錄錯誤稽核日誌失敗:', e);
        }

        return throwError(() => error);
      }),
    );
  }

  /**
   * 清理請求內容，移除敏感資訊
   */
  private sanitizeRequestBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    const sanitized = { ...body };
    const sensitiveFields = ['password', 'secret', 'token', 'key', 'auth', 'credential'];

    for (const field of sensitiveFields) {
      if (field in sanitized) {
        sanitized[field] = '***已隱藏***';
      }
    }

    return sanitized;
  }
}
