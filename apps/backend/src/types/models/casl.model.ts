import {
  AbilityBuilder,
  Ability,
  AbilityClass,
  ExtractSubjectType,
  MongoQuery,
} from '@casl/ability';
import { PureAbility, Subject } from '@casl/ability';
import { SystemUserRole, TenantUserRole } from '@prisma/client';

export type UserRole = SystemUserRole | TenantUserRole | 'guest';

// 定義可能的操作動作 - 與 @horizai/permissions 保持一致
export type Actions =
  | 'manage'
  | 'create'
  | 'read'
  | 'update'
  | 'delete'
  | 'invite'
  | 'remove'
  | 'execute'
  | 'share'
  | 'access';

// 定義可能的資源主體 - 與 @horizai/permissions 保持一致
export type Subjects =
  | 'all'
  | 'System'
  | 'User'
  | 'Tenant'
  | 'TenantUser'
  | 'TenantInvitation'
  | 'Workspace'
  | 'workspace-member'
  | 'Project'
  | 'Client'
  | 'Form'
  | 'Permission'
  | 'Role'
  | 'Dashboard'
  | 'DashboardStats'
  | 'DashboardRecentTenants'
  | 'DashboardRecentOrders'
  | 'DashboardActiveUsers'
  | 'DashboardRevenue'
  | 'DashboardActivity'
  | 'AdminPanel'
  | 'SystemUser'
  | 'ai_models'
  | 'ai_bots'
  | 'Order'
  | 'Plan'
  | 'SystemSettings'
  | 'ai_keys'
  | 'AiFeatureConfig'
  | 'AiGlobalSetting'
  | 'AiUsageLog'
  | 'LoginLog'
  | 'SystemLog'
  | 'LineBot'
  | 'LineMessageLog'
  | 'LineLoginConfig'
  | 'Settings'
  | 'Report'
  | 'WorkspaceMember'
  | 'Department'
  | 'LineGroupVerification'
  // 協作功能相關
  | 'SharedFile'
  | 'FilePermission'
  | 'FileShare'
  | 'Comment'
  | 'CommentReaction'
  | 'Notification'
  | 'CollaborationSession'
  // 使用者資料相關
  | 'UserProfile'
  | 'SensitiveData';

// 定義權限規則類型
export interface AppAbilityRule {
  action: Actions;
  subject: Subjects;
  conditions?: any;
  fields?: string[];
  inverted?: boolean;
  reason?: string;
}

// 定義應用程式的能力類型
export type AppAbility = PureAbility<[Actions, Subjects]>;

// 定義 Ability 類別
export const AppAbility = PureAbility as AbilityClass<AppAbility>;

// 定義權限檢查函數的類型
export type CheckPermission = (action: Actions, subject: Subjects) => boolean;

// 定義權限配置類型
export interface PermissionConfig {
  role: UserRole;
  rules: AppAbilityRule[];
}

// 定義單一權限規則
export interface IPermissionRule {
  action: Actions;
  subject: Subjects;
  conditions?: MongoQuery;
  fields?: string[];
  inverted?: boolean;
  reason?: string;
}

// 簡化的權限規則，用於前端存儲和傳輸
export interface Permission {
  action: Actions;
  subject: Subjects;
  conditions?: Record<string, any>;
  fields?: string[];
}

// 定義權限集合
export interface UserPermissions {
  role: UserRole;
  permissions: IPermissionRule[];
  tenant_id?: string;
}

// 定義權限檢查的回傳型別
export interface PermissionCheck {
  granted: boolean;
  reason?: string;
  conditions?: MongoQuery;
}

// 定義預設權限映射
export const DEFAULT_ROLE_PERMISSIONS: Record<UserRole, IPermissionRule[]> = {
  ['SUPER_ADMIN']: [{ action: 'manage', subject: 'all' }],
  ['SYSTEM_ADMIN']: [
    { action: 'manage', subject: 'User' },
    { action: 'manage', subject: 'Workspace' },
    { action: 'manage', subject: 'Settings' },
    { action: 'read', subject: 'Report' },
    { action: 'read', subject: 'Dashboard' },
  ],
  ['SYSTEM_MODERATOR']: [
    { action: 'read', subject: 'User' },
    { action: 'read', subject: 'Workspace' },
    { action: 'read', subject: 'Settings' },
    { action: 'read', subject: 'Report' },
    { action: 'read', subject: 'Dashboard' },
  ],
  ['TENANT_ADMIN']: [
    { action: 'read', subject: 'User' },
    { action: 'manage', subject: 'Workspace' },
    { action: 'read', subject: 'Settings' },
    { action: 'read', subject: 'Report' },
    { action: 'read', subject: 'Dashboard' },
  ],
  ['TENANT_MANAGER']: [
    { action: 'read', subject: 'User' },
    { action: 'read', subject: 'Workspace' },
    { action: 'read', subject: 'Settings' },
    { action: 'read', subject: 'Dashboard' },
  ],
  ['TENANT_USER']: [
    { action: 'read', subject: 'Workspace' },
    { action: 'read', subject: 'Dashboard' },
  ],
  ['TENANT_VIEWER']: [{ action: 'read', subject: 'Dashboard' }],
  guest: [], // 訪客沒有任何權限
};
