import { PrismaClient, SystemUserRole } from '@prisma/client';

export interface ISystemUser {
  id: string;
  email: string;
  password: string;
  name: string | null;
  role: SystemUserRole;
  status: string;
  avatar: string | null;
  phone: string | null;
  mfa_enabled: boolean;
  mfa_secret: string | null;
  last_login_at: Date | null;
  last_login_ip: string | null;
  last_logout_at: Date | null;
  password_last_changed_at: Date | null;
  created_at: Date;
  updated_at: Date;
}

export interface ICreateSystemUser {
  email: string;
  password: string;
  name?: string;
  role?: SystemUserRole;
  status?: string;
  avatar?: string;
  phone?: string;
}

export interface IUpdateSystemUser {
  email?: string;
  name?: string;
  role?: SystemUserRole;
  status?: string;
  avatar?: string;
  phone?: string;
  mfa_enabled?: boolean;
}

export interface ISystemUserProfile {
  id: string;
  email: string;
  name: string | null;
  role: SystemUserRole;
  status: string;
  avatar: string | null;
  phone: string | null;
  mfa_enabled: boolean;
  last_login_at: Date | null;
  created_at: Date;
  updated_at: Date;
}
