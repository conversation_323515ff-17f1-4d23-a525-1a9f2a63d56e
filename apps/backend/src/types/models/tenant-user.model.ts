import { PrismaC<PERSON>, Prisma, Tenant<PERSON><PERSON><PERSON><PERSON>, TenantUserStatus } from '@prisma/client';

// TenantUser - extends base User model with tenant-specific fields
export interface ITenantUser {
  id: string;
  email: string;
  password: string;
  name: string | null;
  tenant_id: string;
  role: TenantUserRole;
  status: TenantUserStatus;
  avatar: string | null;
  phone: string | null;
  title: string | null;
  department: string | null;
  last_login_at: Date | null;
  last_login_ip: string | null;
  last_logout_at: Date | null;
  password_last_changed_at: Date | null;
  mfa_enabled: boolean;
  mfa_secret: string | null;
  invited_by: string | null;
  left_company_at: Date | null;
  left_company_reason: string | null;
  data_transfer_status: string | null;
  data_transfer_note: string | null;
  created_at: Date;
  updated_at: Date;
}

export interface ICreateTenantUser {
  email: string;
  password: string; // password will be set if it's a new user creation flow through tenant invitation acceptance
  name?: string;
  role?: TenantUserRole;
  status?: TenantUserStatus;
  avatar?: string;
  phone?: string;
  title?: string;
  department?: string;
  tenant_id: string;
  invited_by?: string;
}

export interface IUpdateTenantUser {
  email?: string;
  name?: string;
  role?: TenantUserRole;
  status?: TenantUserStatus;
  avatar?: string;
  phone?: string;
  title?: string;
  department?: string;
  mfa_enabled?: boolean;
  left_company_at?: Date | null;
  left_company_reason?: string | null;
  data_transfer_status?: string | null;
  data_transfer_note?: string | null;
}

// Simplified profile for tenant user, excluding sensitive or internal fields
export interface ITenantUserProfile {
  id: string;
  email: string;
  name: string | null;
  role: TenantUserRole;
  status: TenantUserStatus;
  avatar: string | null;
  phone: string | null;
  title: string | null;
  department: string | null;
  tenant_id: string;
  mfa_enabled: boolean;
  last_login_at: Date | null;
  created_at: Date;
  updated_at: Date;
}

// For tenant user invitation
export interface ITenantUserInvitation {
  email: string;
  name?: string;
  role?: TenantUserRole;
  tenant_id: string;
  invited_by: string;
}

// For searching/filtering tenant users
export interface ITenantUserFilters {
  status?: TenantUserStatus;
  role?: TenantUserRole;
  department?: string;
  mfa_enabled?: boolean;
  search?: string; // General search term for name, email, etc.
}

// For paginated responses
export interface PaginatedTenantUsers {
  users: ITenantUserProfile[];
  total: number;
  page: number;
  limit: number;
  total_pages: number;
}

// For bulk operations
export interface BulkTenantUserOperationDto {
  user_ids: string[];
  status?: TenantUserStatus; // Example: bulk activate/deactivate
  // Add other fields relevant for bulk updates if needed
}

export interface BulkTenantUserOperationResult {
  success: boolean;
  affected_count: number;
  errors?: Array<{ user_id: string; message: string }>;
}
