import type { ID, Status } from './index';
import {
  Prisma,
  system_users,
  tenant_users,
  SystemUserRole,
  TenantUserRole,
  TenantUserStatus,
} from '@prisma/client';
import { Exclude, Expose } from 'class-transformer';

export enum Role {
  SUPER_ADMIN = 'SUPER_ADMIN',
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  TENANT_ADMIN = 'TENANT_ADMIN',
  TENANT_USER = 'TENANT_USER',
}

export const USER_ROLES = {
  SUPER_ADMIN: 'SUPER_ADMIN' as UserRole,
  SYSTEM_ADMIN: 'SYSTEM_ADMIN' as UserRole,
  TENANT_ADMIN: 'TENANT_ADMIN' as User<PERSON><PERSON>,
  TENANT_USER: 'TENANT_USER' as UserRole,
  GUEST: 'guest' as UserRole,
} as const;

export type UserRole = 'SUPER_ADMIN' | 'SYSTEM_ADMIN' | 'TENANT_ADMIN' | 'TENANT_USER' | 'guest';

export type UserStatus = 'active' | 'inactive' | 'pending';

export interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  tenant_id?: string;
  created_at: string;
  updated_at: string;
  avatar?: string;
  status?: 'active' | 'inactive' | 'pending';
  last_login_at?: string;
}

export interface IUser {
  id: ID;
  name: string;
  email: string;
  role: UserRole;
  status: UserStatus;
  avatar?: string;
  tenant_id?: string;
  department?: string;
  position?: string;
  last_login_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface IUserProfile {
  id: string;
  email: string;
  name: string;
  role: Role;
  avatar?: string;
  phone?: string;
  title?: string;
  company?: string;
  department?: string;
  location?: string;
  bio?: string;
  social_links?: {
    linkedin?: string;
    twitter?: string;
    github?: string;
  };
  preferences?: {
    theme?: 'light' | 'dark' | 'system';
    language?: string;
    notifications?: {
      email?: boolean;
      push?: boolean;
    };
  };
  status: UserStatus;
  last_login_at?: string;
  created_at: string;
  updated_at: string;
}

export interface ISocialLink {
  platform: SocialPlatform;
  url: string;
}

export type SocialPlatform = 'linkedin' | 'twitter' | 'facebook' | 'github';

export interface IUserPreferences {
  theme: 'light' | 'dark' | 'system';
  language: string;
  notifications: INotificationPreferences;
  timezone: string;
}

export interface INotificationPreferences {
  email: boolean;
  push: boolean;
  desktop: boolean;
  mobile: boolean;
}

export interface IUpdateProfileData {
  name?: string;
  avatar?: File;
  phone?: string;
  title?: string;
  department?: string;
  company?: string;
  location?: string;
  bio?: string;
  social_links?: ISocialLink[];
}

export interface IUpdatePreferencesData {
  theme?: 'light' | 'dark' | 'system';
  language?: string;
  notifications?: Partial<INotificationPreferences>;
  timezone?: string;
}

// System User Class
export class SystemUser implements Omit<system_users, 'password'> {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  name: string | null;

  @Expose()
  role: SystemUserRole;

  @Expose()
  status: string;

  @Expose()
  created_at: Date;

  @Expose()
  updated_at: Date;

  @Expose()
  last_login_at: Date | null;

  @Expose()
  last_logout_at: Date | null;

  @Expose()
  avatar: string | null;

  @Expose()
  phone: string | null;

  @Expose()
  mfa_enabled: boolean;

  @Expose()
  mfa_secret: string | null;

  @Expose()
  last_login_ip: string | null;

  @Expose()
  password_last_changed_at: Date | null;

  @Exclude()
  password?: string;
}

// Tenant User Class
export class TenantUser implements Omit<tenant_users, 'password'> {
  @Expose()
  id: string;

  @Expose()
  email: string;

  @Expose()
  name: string | null;

  @Expose()
  tenant_id: string;

  @Expose()
  role: TenantUserRole;

  @Expose()
  status: TenantUserStatus;

  @Expose()
  department: string | null;

  @Expose()
  title: string | null;

  @Expose()
  created_at: Date;

  @Expose()
  updated_at: Date;

  @Expose()
  last_login_at: Date | null;

  @Expose()
  last_logout_at: Date | null;

  @Expose()
  avatar: string | null;

  @Expose()
  phone: string | null;

  @Expose()
  invited_by: string | null;

  @Expose()
  mfa_enabled: boolean;

  @Expose()
  mfa_secret: string | null;

  @Expose()
  last_login_ip: string | null;

  @Expose()
  password_last_changed_at: Date | null;

  @Expose()
  left_company_at: Date | null;

  @Expose()
  left_company_reason: string | null;

  @Expose()
  data_transfer_status: string | null;

  @Expose()
  data_transfer_note: string | null;

  @Exclude()
  password?: string;
}

// System User Interfaces
export interface ISystemUser extends Omit<SystemUser, 'password'> {}

// Tenant User Interfaces
export interface ITenantUser extends Omit<TenantUser, 'password'> {}

export interface ITenantUserProfile {
  id: string;
  email: string;
  name: string | null;
  role: string;
  status: string;
  avatar: string | null;
  phone: string | null;
  tenant_id: string;
  invited_by: string | null;
  created_at: Date;
  updated_at: Date;
}

// Create interfaces
export interface ICreateTenantUser {
  email: string;
  password?: string;
  name?: string;
  role?: string;
  status?: string;
  avatar?: string;
  phone?: string;
  tenant_id: string;
  invited_by?: string;
}

export interface ITenantUserInvitation {
  email: string;
  name?: string;
  role?: string;
  tenant_id: string;
  invited_by: string;
}
