import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  BadRequestException,
  ForbiddenException,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { ConfigService } from '@nestjs/config';
import { MailService } from '../../core/mail/mail.service';

import { v4 as uuidv4 } from 'uuid';
import { addDays } from 'date-fns';

@Injectable()
export class TenantInvitationsService {
  private readonly logger = new Logger(TenantInvitationsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly mailService: MailService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 建立新的租戶邀請
   * @param email 受邀者的電子郵件
   * @param tenant_id 租戶 ID
   * @param roleId 角色 ID
   * @param user_id 建立邀請的使用者 ID
   * @returns 建立的邀請資料
   */
  async create(email: string, tenantId: string, roleId: string, userId: string) {
    try {
      // 檢查是否已有待處理的邀請
      const existingInvitation = await this.prisma.tenant_invitations.findFirst({
        where: {
          email,
          tenant_id: tenantId,
          status: 'pending',
        },
      });

      if (existingInvitation) {
        throw new ConflictException('已有待處理的邀請');
      }

      // 建立邀請記錄
      const invitation = await this.prisma.tenant_invitations.create({
        data: {
          id: uuidv4(),
          email,
          tenant_id: tenantId,
          role_id: roleId,
          token: uuidv4(),
          expires_at: addDays(new Date(), 7),
          created_by_id: userId,
        },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
            },
          },
          roles: true,
        },
      });

      // 發送邀請郵件
      // 建立邀請連結
      const inviteLink = `${this.configService.get('FRONTEND_URL')}/auth/accept-invitation?token=${invitation.token}`;

      // 發送邀請郵件
      const creator = await this.prisma.system_users.findUnique({ where: { id: userId } });

      await this.mailService.sendTenantInvitation(
        invitation.email,
        inviteLink,
        invitation.tenants.name,
        creator?.name || '系統管理員',
      );

      return invitation;
    } catch (error) {
      this.logger.error(`建立邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 取得租戶的所有邀請
   * @param tenant_id 租戶 ID
   * @returns 租戶的邀請列表
   */
  async findAllByTenant(tenantId: string) {
    try {
      // 使用 $queryRaw 直接執行 SQL 查詢
      const invitations = await this.prisma.$queryRaw`
        SELECT ti.*, 
               t.id as tenantId, t.name as tenant_name, 
               r.id as role_id, r.name as role_name, r.display_name as role_display_name,
               u.id as created_by_id, u.name as created_by_name, u.email as created_by_email
        FROM tenant_invitations ti
        LEFT JOIN tenants t ON ti.tenantId = t.id
        LEFT JOIN roles r ON ti.role_id = r.id
        LEFT JOIN users u ON ti.created_by_id = u.id
        WHERE ti.tenant_id = ${tenantId} AND ti.status = 'pending'
      `;

      // 處理查詢結果，轉換為符合預期的格式
      const formattedInvitations = (invitations as any[]).map((inv) => ({
        id: inv.id,
        email: inv.email,
        tenantId: inv.tenantId,
        roleId: inv.role_id,
        status: inv.status,
        token: inv.token,
        created_at: inv.created_at,
        expiresAt: inv.expires_at,
        createdById: inv.created_by_id,
        acceptedById: inv.accepted_by_id,
        acceptedAt: inv.accepted_at,
        tenant: {
          id: inv.tenantId,
          name: inv.tenant_name,
        },
        role: {
          id: inv.role_id,
          name: inv.role_name,
          displayName: inv.role_display_name,
        },
        createdBy: inv.created_by_id
          ? {
              id: inv.created_by_id,
              name: inv.created_by_name,
              email: inv.created_by_email,
            }
          : null,
      }));

      return formattedInvitations;
    } catch (error) {
      this.logger.error(`讀取邀請列表失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 驗證邀請 token
   * @param token 邀請 token
   * @returns 邀請資料
   */
  async validateInvitationToken(token: string) {
    try {
      const invitation = await this.prisma.tenant_invitations.findUnique({
        where: { token },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: {
            select: {
              id: true,
              name: true,
              display_name: true,
            },
          },
        },
      });

      if (!invitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (invitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      if (invitation.expires_at < new Date()) {
        // 標記邀請為過期
        await this.prisma.tenant_invitations.update({
          where: { id: invitation.id },
          data: { status: 'expired' },
        });
        throw new BadRequestException('邀請已過期');
      }

      if (invitation.tenants.status !== 'active') {
        throw new BadRequestException('租戶狀態異常');
      }

      return invitation;
    } catch (error) {
      this.logger.error(`驗證邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 接受邀請
   * @param token 邀請 token
   * @param user_id 接受邀請的使用者 ID
   * @returns 更新後的邀請資料
   */
  async acceptInvitation(token: string, userId: string) {
    try {
      // 讀取完整的邀請資訊，包括角色
      const fullInvitation = await this.prisma.tenant_invitations.findUnique({
        where: { token },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: true,
        },
      });

      if (!fullInvitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (fullInvitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      if (fullInvitation.expires_at < new Date()) {
        throw new BadRequestException('邀請已過期');
      }

      // 更新邀請狀態
      await this.prisma.tenant_invitations.update({
        where: { id: fullInvitation.id },
        data: {
          status: 'accepted',
          accepted_by_id: userId,
          accepted_at: new Date(),
        },
      });

      return fullInvitation;
    } catch (error) {
      this.logger.error(`接受邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 重新發送邀請
   * @param invitationId 邀請 ID
   * @param user_id 執行操作的使用者 ID
   * @returns 更新後的邀請
   */
  async resendInvitation(invitationId: string, userId: string) {
    try {
      const invitation = await this.prisma.tenant_invitations.findUnique({
        where: { id: invitationId },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: true,
        },
      });

      if (!invitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (invitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      // 更新過期時間
      const updatedInvitation = await this.prisma.tenant_invitations.update({
        where: { id: invitationId },
        data: {
          expires_at: addDays(new Date(), 7),
        },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: true,
        },
      });

      // 重新發送邀請郵件
      // 建立邀請連結
      const inviteLink = `${this.configService.get('FRONTEND_URL')}/auth/accept-invitation?token=${updatedInvitation.token}`;

      // 發送邀請郵件
      const creator = await this.prisma.system_users.findUnique({ where: { id: userId } });

      await this.mailService.sendTenantInvitation(
        updatedInvitation.email,
        inviteLink,
        updatedInvitation.tenants.name,
        creator?.name || '系統管理員',
      );

      return updatedInvitation;
    } catch (error) {
      this.logger.error(`重新發送邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 取消邀請
   * @param invitationId 邀請 ID
   * @param user_id 執行操作的使用者 ID
   * @returns 操作結果
   */
  async cancelInvitation(invitationId: string, userId: string) {
    try {
      const invitation = await this.prisma.tenant_invitations.findUnique({
        where: { id: invitationId },
      });

      if (!invitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (invitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      // 刪除邀請
      await this.prisma.tenant_invitations.delete({
        where: { id: invitationId },
      });

      return { message: '邀請已取消' };
    } catch (error) {
      this.logger.error(`取消邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 批准邀請
   * @param invitationId 邀請 ID
   * @param user_id 批准邀請的使用者 ID（必須為租戶管理員）
   * @returns 操作結果
   */
  async approveInvitation(invitationId: string, userId: string) {
    try {
      // 首先查詢邀請資訊
      const invitation = await this.prisma.tenant_invitations.findUnique({
        where: { id: invitationId },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: {
            select: {
              id: true,
              name: true,
              display_name: true,
            },
          },
        },
      });

      if (!invitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (invitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      // 確認使用者是否為此租戶的管理員
      // 嘗試從租戶用戶中查找
      const currentUser = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { tenant_id: true },
      });

      if (!currentUser || currentUser.tenant_id !== invitation.tenant_id) {
        throw new ForbiddenException('您沒有權限拒絕此邀請');
      }

      // 檢查用戶是否有管理員角色 - 使用分離用戶架構
      const userAdminRole = await this.prisma.tenant_user_roles.findFirst({
        where: {
          tenant_user_id: userId,
          role: {
            name: 'TENANT_ADMIN',
          },
        },
      });

      if (!userAdminRole) {
        throw new ForbiddenException('您沒有權限批准此邀請');
      }

      // 取得批准者資訊
      const approver = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      // 更新邀請狀態
      const updatedInvitation = await this.prisma.tenant_invitations.update({
        where: { id: invitationId },
        data: {
          status: 'approved',
          accepted_by_id: userId,
          accepted_at: new Date(),
        },
      });

      // 取得租戶資訊
      const tenant = await this.prisma.tenants.findUnique({
        where: { id: invitation.tenant_id },
        select: { name: true },
      });

      // 發送邀請狀態更新郵件
      await this.mailService.sendInvitationStatusUpdate(
        invitation.email,
        tenant?.name || '未知租戶',
        'approved',
        approver?.name || '租戶管理員',
      );

      return {
        message: '邀請已批准',
        invitation: updatedInvitation,
      };
    } catch (error) {
      this.logger.error(`批准邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 拒絕邀請
   * @param invitationId 邀請 ID
   * @param user_id 拒絕邀請的使用者 ID（必須為租戶管理員）
   * @returns 操作結果
   */
  async rejectInvitation(invitationId: string, userId: string) {
    try {
      // 首先查詢邀請資訊
      const invitation = await this.prisma.tenant_invitations.findUnique({
        where: { id: invitationId },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
        },
      });

      if (!invitation) {
        throw new NotFoundException('邀請不存在');
      }

      if (invitation.status !== 'pending') {
        throw new BadRequestException('邀請已被處理');
      }

      // 確認使用者是否為此租戶的管理員
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { tenant_id: true },
      });

      if (!user || user.tenant_id !== invitation.tenant_id) {
        throw new ForbiddenException('您沒有權限批准此邀請');
      }

      // 檢查用戶是否有管理員角色 - 使用分離用戶架構
      const rejecterAdminRole = await this.prisma.tenant_user_roles.findFirst({
        where: {
          tenant_user_id: userId,
          role: {
            name: 'TENANT_ADMIN',
          },
        },
      });

      if (!rejecterAdminRole) {
        throw new ForbiddenException('您沒有權限拒絕此邀請');
      }

      // 取得拒絕者資訊
      const rejecter = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });

      // 更新邀請狀態
      const updatedInvitation = await this.prisma.tenant_invitations.update({
        where: { id: invitationId },
        data: {
          status: 'rejected',
          // rejected_by_id: userId,  // 這個欄位可能不存在於schema
          // rejected_at: new Date(),
        },
      });

      // 取得租戶資訊
      const tenant = await this.prisma.tenants.findUnique({
        where: { id: invitation.tenant_id },
        select: { name: true },
      });

      // 發送邀請狀態更新郵件
      await this.mailService.sendInvitationStatusUpdate(
        invitation.email,
        tenant?.name || '未知租戶',
        'rejected',
        rejecter?.name || '租戶管理員',
      );

      return {
        message: '邀請已拒絕',
        invitation: updatedInvitation,
      };
    } catch (error) {
      this.logger.error(`拒絕邀請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 建立加入租戶申請
   * @param tenant_id 租戶 ID
   * @param user_id 申請使用者 ID
   * @returns 申請結果
   */
  async createJoinRequest(tenantId: string, userId: string) {
    try {
      // 檢查租戶是否存在且狀態正常
      const tenant = await this.prisma.tenants.findUnique({
        where: { id: tenantId },
        select: { id: true, name: true, status: true },
      });

      if (!tenant) {
        throw new NotFoundException('租戶不存在');
      }

      if (tenant.status !== 'active') {
        throw new BadRequestException('租戶狀態異常，無法申請加入');
      }

      // 檢查使用者是否存在 - 先檢查系統用戶，再檢查租戶用戶
      let user: any = null;
      try {
        user = await this.prisma.system_users.findUnique({
          where: { id: userId },
          select: { id: true, email: true, name: true },
        });
        // 系統用戶沒有 tenant_id，可以申請加入租戶
        if (user) {
          user.tenant_id = null;
        }
      } catch (error) {
        // 如果系統用戶不存在，檢查租戶用戶
        try {
          user = await this.prisma.tenant_users.findUnique({
            where: { id: userId },
            select: { id: true, email: true, name: true, tenant_id: true },
          });
        } catch (tenantError) {
          // 兩種用戶都不存在
        }
      }

      if (!user) {
        throw new NotFoundException('使用者不存在');
      }

      // 檢查使用者是否已經歸屬於租戶
      if (user.tenant_id) {
        throw new BadRequestException('您已歸屬於其他租戶，無法申請加入');
      }

      // 檢查是否已有待處理的申請
      const existingInvite = await this.prisma.tenant_invitations.findFirst({
        where: {
          email: user.email,
          tenant_id: tenantId,
          status: 'pending',
        },
      });

      if (existingInvite) {
        throw new ConflictException('您已有待處理的申請');
      }

      // 為加入申請找到一個合適的預設角色 (TENANT_USER)
      const defaultRole = await this.prisma.roles.findFirst({
        where: {
          name: 'TENANT_USER',
          scope: 'TENANT',
        },
      });

      if (!defaultRole) {
        throw new BadRequestException('系統角色設定異常');
      }

      // 建立加入申請（實際上是建立一個邀請記錄）
      const joinRequest = await this.prisma.tenant_invitations.create({
        data: {
          id: uuidv4(),
          email: user.email,
          tenant_id: tenantId,
          role_id: defaultRole.id,
          token: uuidv4(),
          status: 'pending',
          expires_at: addDays(new Date(), 30), // 申請有效期 30 天
          created_by_id: userId, // 記錄是誰發起的申請
        },
        include: {
          tenants: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          roles: {
            select: {
              id: true,
              name: true,
              display_name: true,
            },
          },
        },
      });

      this.logger.log(`使用者 ${userId} 申請加入租戶 ${tenantId}`);

      return {
        id: joinRequest.id,
        message: '申請已提交，等待管理員審核',
        tenant: joinRequest.tenants,
        status: 'pending',
        created_at: joinRequest.created_at,
      };
    } catch (error) {
      this.logger.error(`建立加入申請失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 取得租戶的所有待處理邀請（供租戶管理員審核）
   * @param tenant_id 租戶 ID
   * @param user_id 當前使用者 ID
   * @returns 待處理的邀請列表
   */
  async findAllForTenantUser(tenantId: string, userId: string) {
    try {
      // 確認使用者是否為此租戶的管理員
      const tenantUser = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { tenant_id: true },
      });

      if (!tenantUser || tenantUser.tenant_id !== tenantId) {
        throw new ForbiddenException('您沒有權限查看此租戶的邀請列表');
      }

      // 檢查用戶是否有管理員角色 - 使用分離用戶架構
      const viewerAdminRole = await this.prisma.tenant_user_roles.findFirst({
        where: {
          tenant_user_id: userId,
          role: {
            name: 'TENANT_ADMIN',
          },
        },
      });

      if (!viewerAdminRole) {
        throw new ForbiddenException('您沒有權限查看此租戶的邀請列表');
      }

      // 獲取邀請列表
      return this.findAllByTenant(tenantId);
    } catch (error) {
      this.logger.error(`讀取租戶邀請列表失敗: ${error.message}`, error.stack);
      throw error;
    }
  }

  async markInvitationAsUsed(token: string, userId: string): Promise<any> {
    const invitation = await this.prisma.tenant_invitations.findUnique({
      where: { token },
    });
    if (!invitation) {
      throw new NotFoundException('邀請不存在');
    }
    return this.prisma.tenant_invitations.update({
      where: { id: invitation.id },
      data: {
        status: 'accepted',
        accepted_by_id: userId,
        accepted_at: new Date(),
      },
    });
  }

  /**
   * 驗證邀請 token
   * @param token 邀請 token
   * @returns 驗證結果
   */
  async verifyInvitation(token: string) {
    try {
      const invitation = await this.validateInvitationToken(token);
      return {
        valid: true,
        invitation: {
          email: invitation.email,
          tenant_name: invitation.tenants?.name || '未知租戶',
          role_name: invitation.roles?.display_name || invitation.roles?.name || '未知角色',
          expires_at: invitation.expires_at,
        },
      };
    } catch (error) {
      return {
        valid: false,
        message: error.message,
      };
    }
  }
}
