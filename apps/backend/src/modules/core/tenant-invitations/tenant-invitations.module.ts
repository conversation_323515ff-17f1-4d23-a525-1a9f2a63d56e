import { Modu<PERSON> } from '@nestjs/common';
import { TenantInvitationsService } from './tenant-invitations.service';
import { TenantInvitationsAdminController } from './controllers/tenant-invitations-admin.controller';
import { TenantInvitationsMemberController } from './controllers/tenant-invitations-member.controller';
import { PrismaModule } from '../prisma/prisma.module';
import { ConfigModule } from '@nestjs/config';
import { MailModule } from '../mail/mail.module';

@Module({
  imports: [PrismaModule, ConfigModule, MailModule],
  controllers: [TenantInvitationsAdminController, TenantInvitationsMemberController],
  providers: [TenantInvitationsService],
  exports: [TenantInvitationsService],
})
export class TenantInvitationsModule {}
