import { Controller, Get, Post, Param, UseGuards, Req, HttpCode, HttpStatus } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { TenantInvitationsService } from '../tenant-invitations.service';
import { JwtAuthGuard } from '../../auth/guards/auth.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../../common/enums/role.enum';
import { Request } from 'express';

@ApiTags('api/tenant-invitations')
@ApiBearerAuth()
@Controller('api/tenant-invitations')
@UseGuards(JwtAuthGuard)
export class TenantInvitationsMemberController {
  constructor(private readonly tenantInvitationsService: TenantInvitationsService) {}

  @Get()
  @Roles(Role.TENANT_ADMIN)
  @ApiOperation({ summary: '獲取租戶的所有待處理邀請' })
  @ApiResponse({ status: 200, description: '成功獲取邀請列表' })
  @ApiResponse({ status: 403, description: '沒有權限查看邀請列表' })
  async findAll(@Req() req: Request) {
    const userId = req.user && req.user['sub'];
    const tenantId = req.user && req.user['tenant_id'];

    if (!tenantId) {
      return { invitations: [] };
    }

    return {
      invitations: await this.tenantInvitationsService.findAllForTenantUser(tenantId, userId),
    };
  }

  @Post(':id/approve')
  @Roles(Role.TENANT_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '批准加入租戶的邀請' })
  @ApiParam({ name: 'id', description: '邀請 ID' })
  @ApiResponse({ status: 200, description: '邀請已批准' })
  @ApiResponse({ status: 400, description: '邀請已被處理或過期' })
  @ApiResponse({ status: 403, description: '沒有權限批准邀請' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  async approveInvitation(@Param('id') id: string, @Req() req: Request) {
    const userId = req.user && req.user['sub'];
    return this.tenantInvitationsService.approveInvitation(id, userId);
  }

  @Post(':id/reject')
  @Roles(Role.TENANT_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '拒絕加入租戶的邀請' })
  @ApiParam({ name: 'id', description: '邀請 ID' })
  @ApiResponse({ status: 200, description: '邀請已拒絕' })
  @ApiResponse({ status: 400, description: '邀請已被處理或過期' })
  @ApiResponse({ status: 403, description: '沒有權限拒絕邀請' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  async rejectInvitation(@Param('id') id: string, @Req() req: Request) {
    const userId = req.user && req.user['sub'];
    return this.tenantInvitationsService.rejectInvitation(id, userId);
  }
}
