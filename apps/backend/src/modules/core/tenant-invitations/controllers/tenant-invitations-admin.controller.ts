import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  UseGuards,
  Req,
  Query,
  HttpCode,
  HttpStatus,
  NotFoundException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { TenantInvitationsService } from '../tenant-invitations.service';
import { CreateInvitationDto } from '../dto/create-invitation.dto';
import { JwtAuthGuard } from '../../auth/guards/auth.guard';
import { Roles } from '../../auth/decorators/roles.decorator';
import { Role } from '../../../../common/enums/role.enum';
import { Request } from 'express';

@ApiTags('admin/tenant-invitations')
@ApiBearerAuth()
@Controller('admin/tenant-invitations')
@UseGuards(JwtAuthGuard)
export class TenantInvitationsAdminController {
  constructor(private readonly tenantInvitationsService: TenantInvitationsService) {}

  @Post()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '建立租戶邀請' })
  @ApiResponse({ status: 201, description: '租戶邀請建立成功' })
  @ApiResponse({ status: 400, description: '無效的請求資料' })
  @ApiResponse({ status: 409, description: '邀請已存在或使用者已是租戶成員' })
  create(@Body() createInvitationDto: CreateInvitationDto, @Req() req: Request) {
    const userId = req.user && req.user['sub'];
    const { email, tenantId, role } = createInvitationDto;
    // 從 Role 枚舉讀取 roleId
    const roleId = role;
    return this.tenantInvitationsService.create(email, tenantId, roleId, userId);
  }

  @Get('tenant/:tenant_id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '取得租戶的所有邀請' })
  @ApiResponse({ status: 200, description: '成功取得邀請列表' })
  findAllByTenant(@Param('tenant_id') tenantId: string) {
    return this.tenantInvitationsService.findAllByTenant(tenantId);
  }

  @Get('verify')
  @ApiOperation({ summary: '驗證邀請 token' })
  @ApiResponse({ status: 200, description: '邀請有效' })
  @ApiResponse({ status: 400, description: '邀請已被處理或已過期' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  verifyInvitation(@Query('token') token: string) {
    if (!token) {
      throw new NotFoundException('缺少 token 參數');
    }
    return this.tenantInvitationsService.verifyInvitation(token);
  }

  @Post('accept')
  @UseGuards(JwtAuthGuard)
  @ApiOperation({ summary: '接受邀請' })
  @ApiResponse({ status: 200, description: '邀請已成功接受' })
  @ApiResponse({ status: 400, description: '無效的請求或邀請已被處理' })
  @ApiResponse({ status: 404, description: '邀請或使用者不存在' })
  acceptInvitation(@Query('token') token: string, @Req() req: Request) {
    if (!token) {
      throw new NotFoundException('缺少 token 參數');
    }
    const userId = req.user && req.user['sub'];
    return this.tenantInvitationsService.acceptInvitation(token, userId);
  }

  @Post(':id/resend')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重新發送邀請' })
  @ApiResponse({ status: 200, description: '邀請已重新發送' })
  @ApiResponse({ status: 400, description: '只能重新發送待處理的邀請' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  resendInvitation(@Param('id') id: string, @Req() req: Request) {
    const userId = req.user && req.user['sub'];
    return this.tenantInvitationsService.resendInvitation(id, userId);
  }

  @Delete(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN, Role.TENANT_ADMIN)
  @ApiOperation({ summary: '取消邀請' })
  @ApiResponse({ status: 200, description: '邀請已取消' })
  @ApiResponse({ status: 400, description: '只能取消待處理的邀請' })
  @ApiResponse({ status: 404, description: '邀請不存在' })
  cancelInvitation(@Param('id') id: string, @Req() req: Request) {
    const userId = req.user && req.user['sub'];
    return this.tenantInvitationsService.cancelInvitation(id, userId);
  }
}
