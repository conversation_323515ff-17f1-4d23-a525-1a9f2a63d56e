import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { PrismaService } from './prisma.service';
import { ClsService } from 'nestjs-cls';
import { AppModule } from '../../../app.module';

/**
 * 租戶隔離集成測試
 * 測試實際的租戶數據隔離是否正確工作
 */
describe('Tenant Isolation (e2e)', () => {
  let app: INestApplication;
  let prismaService: PrismaService;
  let clsService: ClsService;

  const TENANT_A_ID = 'tenant-a-test-id';
  const TENANT_B_ID = 'tenant-b-test-id';

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    prismaService = moduleFixture.get<PrismaService>(PrismaService);
    clsService = moduleFixture.get<ClsService>(ClsService);
  });

  afterAll(async () => {
    // 清理測試數據
    await cleanupTestData();
    await app.close();
  });

  beforeEach(async () => {
    // 確保每個測試開始時沒有設置租戶上下文
    jest.spyOn(clsService, 'get').mockReturnValue(null);
  });

  afterEach(async () => {
    jest.restoreAllMocks();
  });

  describe('Project Isolation', () => {
    it('should isolate projects by tenant', async () => {
      // 創建測試租戶
      await createTestTenants();

      // 設置租戶 A 上下文
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);

      // 為租戶 A 創建項目
      const projectA = await prismaService.projects.create({
        data: {
          name: 'Project A',
          description: 'Project for tenant A',
          user_id: 'user-a',
          // tenant_id 應該自動注入
        },
      });

      // 切換到租戶 B 上下文
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_B_ID);

      // 為租戶 B 創建項目
      const projectB = await prismaService.projects.create({
        data: {
          name: 'Project B',
          description: 'Project for tenant B',
          user_id: 'user-b',
          // tenant_id 應該自動注入
        },
      });

      // 驗證租戶 A 只能看到自己的項目
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);
      const tenantAProjects = await prismaService.projects.findMany({});
      expect(tenantAProjects).toHaveLength(1);
      expect(tenantAProjects[0].name).toBe('Project A');
      expect(tenantAProjects[0].tenant_id).toBe(TENANT_A_ID);

      // 驗證租戶 B 只能看到自己的項目
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_B_ID);
      const tenantBProjects = await prismaService.projects.findMany({});
      expect(tenantBProjects).toHaveLength(1);
      expect(tenantBProjects[0].name).toBe('Project B');
      expect(tenantBProjects[0].tenant_id).toBe(TENANT_B_ID);

      // 清理
      await cleanupTestProjects();
    });

    it('should prevent cross-tenant project access', async () => {
      await createTestTenants();

      // 設置租戶 A 上下文並創建項目
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);
      const projectA = await prismaService.projects.create({
        data: {
          name: 'Secret Project A',
          user_id: 'user-a',
        },
      });

      // 切換到租戶 B 上下文
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_B_ID);

      // 嘗試訪問租戶 A 的項目應該失敗
      const result = await prismaService.projects.findFirst({
        where: { id: projectA.id },
      });

      expect(result).toBeNull(); // 應該找不到，因為租戶隔離

      await cleanupTestProjects();
    });

    it('should prevent explicit cross-tenant access attempts', async () => {
      await createTestTenants();

      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);

      // 嘗試明確指定不同的 tenant_id 應該拋出錯誤
      await expect(
        prismaService.projects.findMany({
          where: {
            tenant_id: TENANT_B_ID, // 嘗試訪問租戶 B 的數據
          },
        }),
      ).rejects.toThrow('Tenant isolation violation');
    });
  });

  describe('Task Isolation', () => {
    it('should isolate tasks by tenant', async () => {
      await createTestTenants();

      // 設置租戶 A 上下文
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);

      const projectA = await prismaService.projects.create({
        data: {
          name: 'Project A',
          user_id: 'user-a',
        },
      });

      const taskA = await prismaService.tasks.create({
        data: {
          title: 'Task A',
          project_id: projectA.id,
          created_by_id: 'user-a',
        },
      });

      // 切換到租戶 B 上下文
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_B_ID);

      const projectB = await prismaService.projects.create({
        data: {
          name: 'Project B',
          user_id: 'user-b',
        },
      });

      const taskB = await prismaService.tasks.create({
        data: {
          title: 'Task B',
          project_id: projectB.id,
          created_by_id: 'user-b',
        },
      });

      // 驗證租戶隔離
      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);
      const tenantATasks = await prismaService.tasks.findMany({});
      expect(tenantATasks).toHaveLength(1);
      expect(tenantATasks[0].title).toBe('Task A');

      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_B_ID);
      const tenantBTasks = await prismaService.tasks.findMany({});
      expect(tenantBTasks).toHaveLength(1);
      expect(tenantBTasks[0].title).toBe('Task B');

      await cleanupTestTasks();
      await cleanupTestProjects();
    });
  });

  describe('CreateMany and Batch Operations', () => {
    it('should handle createMany with automatic tenant_id injection', async () => {
      await createTestTenants();

      jest.spyOn(clsService, 'get').mockReturnValue(TENANT_A_ID);

      const project = await prismaService.projects.create({
        data: {
          name: 'Batch Project',
          user_id: 'user-a',
        },
      });

      // 批量創建任務
      await prismaService.tasks.createMany({
        data: [
          {
            title: 'Batch Task 1',
            project_id: project.id,
            created_by_id: 'user-a',
          },
          {
            title: 'Batch Task 2',
            project_id: project.id,
            created_by_id: 'user-a',
          },
        ],
      });

      const tasks = await prismaService.tasks.findMany({
        where: { project_id: project.id },
      });

      expect(tasks).toHaveLength(2);
      expect(tasks.every((task) => task.tenant_id === TENANT_A_ID)).toBe(true);

      await cleanupTestTasks();
      await cleanupTestProjects();
    });
  });

  describe('System Level Operations', () => {
    it('should allow system operations without tenant context', async () => {
      // 確保沒有租戶上下文
      jest.spyOn(clsService, 'get').mockReturnValue(null);

      // 系統級模型操作應該正常工作
      const keys = await prismaService.ai_keys.findMany({
        where: { provider: 'openai' },
      });

      // 這應該不會拋出錯誤，因為 ai_keys 不需要租戶隔離
      expect(Array.isArray(keys)).toBe(true);
    });
  });

  // Helper functions
  async function createTestTenants() {
    // 清理可能存在的測試租戶
    jest.spyOn(clsService, 'get').mockReturnValue(null);

    await prismaService.tenants.deleteMany({
      where: {
        id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });

    // 創建測試租戶
    await prismaService.tenants.createMany({
      data: [
        {
          id: TENANT_A_ID,
          name: 'Test Tenant A',
        },
        {
          id: TENANT_B_ID,
          name: 'Test Tenant B',
        },
      ],
    });
  }

  async function cleanupTestData() {
    jest.spyOn(clsService, 'get').mockReturnValue(null);

    await prismaService.tasks.deleteMany({
      where: {
        tenant_id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });

    await prismaService.projects.deleteMany({
      where: {
        tenant_id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });

    await prismaService.tenants.deleteMany({
      where: {
        id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });
  }

  async function cleanupTestProjects() {
    jest.spyOn(clsService, 'get').mockReturnValue(null);
    await prismaService.projects.deleteMany({
      where: {
        tenant_id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });
  }

  async function cleanupTestTasks() {
    jest.spyOn(clsService, 'get').mockReturnValue(null);
    await prismaService.tasks.deleteMany({
      where: {
        tenant_id: { in: [TENANT_A_ID, TENANT_B_ID] },
      },
    });
  }
}); 