import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { ClsService } from 'nestjs-cls';
import './prisma-helpers';

/**
 * 擴展的 Prisma 服務，提供對數據庫的訪問
 * 包含了對原 PrismaClient 的擴展，添加了缺失模型的代理
 * 實現了完整的租戶數據隔離中間件
 */
@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  constructor(private readonly cls: ClsService) {
    super({
      log: ['error', 'warn'],
    });

    // 租戶隔離中間件 - 自動為所有支持的操作添加 tenant_id 過濾
    this.$use(async (params, next) => {
      const tenant_id = this.cls.get('tenant_id');

      // 如果沒有租戶 ID，跳過隔離（系統級操作）
      if (!tenant_id) {
        return next(params);
      }

      // 所有需要租戶隔離的模型（必填 tenant_id）
      const models_with_required_tenant_id = [
        'ai_photo_analysis_results',
        'albums',
        'orders',
        'photos',
        'projects',
        'tasks',
        'progress_entries',
        'project_milestones',
        'progress_reports',
        'tenant_credit_purchases',
        'tenant_invitations',
        'tenant_users',
        'workspaces',
        'workspace_members',
        'workspace_activity_logs',
        'ai_workflows',
        'workflow_nodes',
        'workflow_executions',
        'tenant_lifecycle_events',
        'comments',
        'comment_reactions',
        'comment_mentions',
        'shared_files',
        'file_permissions',
        'file_shares',
        'file_access_logs',
        'message_conversations',
        'messages',
        'message_files',
        'file_attachments',
        'dashboard_charts',
        'dashboard_settings',
        'notifications',
        'notification_settings',
        'ai_bots',
        'ai_usage_logs',
        'line_bots',
        'line_group_verifications',
        'line_message_logs',
      ];

      // 可選 tenant_id 但仍需隔離的模型
      const models_with_optional_tenant_id = ['roles', 'subscriptions', 'system_logs'];

      const all_tenant_models = [
        ...models_with_required_tenant_id,
        ...models_with_optional_tenant_id,
      ];

      // 需要租戶隔離的操作
      const actions_requiring_tenant_isolation = [
        'findUnique',
        'findFirst',
        'findMany',
        'update',
        'updateMany',
        'delete',
        'deleteMany',
        'count',
        'aggregate',
        'groupBy',
      ];

      const model = params.model;

      if (
        model &&
        all_tenant_models.includes(model) &&
        actions_requiring_tenant_isolation.includes(params.action)
      ) {
        // 為查詢添加 tenant_id 過濾
        if (params.args.where) {
          // 如果已經有 tenant_id 條件，確保它匹配當前租戶
          if (params.args.where.tenant_id && params.args.where.tenant_id !== tenant_id) {
            throw new Error(
              `Tenant isolation violation: Attempted to access data from different tenant`,
            );
          }

          params.args.where = {
            ...params.args.where,
            tenant_id: tenant_id,
          };
        } else {
          params.args.where = {
            tenant_id: tenant_id,
          };
        }

        // 對於創建操作，自動注入 tenant_id
        if (params.action === 'create' && models_with_required_tenant_id.includes(model)) {
          if (!params.args.data) {
            params.args.data = {};
          }

          // 確保創建時包含正確的 tenant_id
          if (params.args.data.tenant_id && params.args.data.tenant_id !== tenant_id) {
            throw new Error(
              `Tenant isolation violation: Attempted to create data for different tenant`,
            );
          }

          params.args.data.tenant_id = tenant_id;
        }

        // 對於 createMany 操作
        if (params.action === 'createMany' && models_with_required_tenant_id.includes(model)) {
          if (params.args.data && Array.isArray(params.args.data)) {
            params.args.data = params.args.data.map((item: any) => ({
              ...item,
              tenant_id: tenant_id,
            }));
          }
        }

        // 對於 upsert 操作
        if (params.action === 'upsert' && models_with_required_tenant_id.includes(model)) {
          if (params.args.where) {
            params.args.where = {
              ...params.args.where,
              tenant_id: tenant_id,
            };
          }

          if (params.args.create) {
            params.args.create.tenant_id = tenant_id;
          }

          if (params.args.update && typeof params.args.update === 'object') {
            // 防止更新操作修改 tenant_id
            delete params.args.update.tenant_id;
          }
        }
      }

      return next(params);
    });
  }

  async onModuleInit() {
    await this.$connect();
  }

  async onModuleDestroy() {
    await this.$disconnect();
  }
}
// 注意：Prisma 會根據 schema.prisma 中的 @@map 指令自動映射模型名稱
