import { Injectable, Logger } from '@nestjs/common';
import { SendGridConfig } from './mail.model';
// 使用 CommonJS 引入 SendGrid Mail 模組，支援 default export
const sgMailModule = require('@sendgrid/mail');
const sgMail = sgMailModule.default || sgMailModule;

/**
 * SendGrid 發信服務
 * 單一職責：僅處理 SendGrid 發信相關邏輯
 */
@Injectable()
export class SendGridMailService {
  private readonly logger = new Logger(SendGridMailService.name);

  /**
   * 發送郵件（支援模板與動態參數）
   * @param config SendGridConfig
   * @returns 發送成功回傳 true，否則丟出錯誤
   */
  async sendMail(config: SendGridConfig): Promise<boolean> {
    try {
      if (!config.apiKey) {
        throw new Error('缺少 SendGrid API Key，請在設定中提供有效的 API Key');
      }
      sgMail.setApiKey(config.apiKey);
      let fromEmail = config.from;
      let fromName = '';
      const emailRegex = /(.*)\s*<(.+@.+)>/;
      const matches = config.from.match(emailRegex);
      if (matches && matches.length === 3) {
        fromName = matches[1].trim().replace(/"/g, '');
        fromEmail = matches[2].trim();
      }
      const msg: any = {
        to: config.to,
        from: fromName ? { email: fromEmail, name: fromName } : fromEmail,
      };
      if (config.templateId) {
        this.logger.debug(`使用 SendGrid 模板發送郵件，模板ID: ${config.templateId}`);
        msg.templateId = config.templateId;
        if (config.dynamicTemplateData) {
          msg.dynamicTemplateData = config.dynamicTemplateData;
          if (!msg.dynamicTemplateData.subject) {
            msg.dynamicTemplateData.subject = '【HorizAI 系統郵件】';
            this.logger.debug('動態模板未提供主旨，使用預設主旨');
          }
          this.logger.debug(`動態模板數據: ${JSON.stringify(config.dynamicTemplateData)}`);
        } else {
          msg.dynamicTemplateData = { subject: '【HorizAI 系統郵件】' };
          this.logger.debug('未提供動態模板數據，使用基本主旨');
        }
      } else {
        this.logger.debug(`使用 SendGrid 普通郵件發送，主題: ${config.subject}`);
        msg.subject = config.subject || '【HorizAI 系統郵件】';
        msg.html = config.html;
      }
      this.logger.debug(`嘗試發送 SendGrid 郵件: ${JSON.stringify(msg, null, 2)}`);
      const response = await sgMail.send(msg);
      this.logger.debug(
        `SendGrid 郵件已成功發送至 ${config.to}，狀態碼: ${response[0]?.statusCode}`,
      );
      return true;
    } catch (error) {
      this.logger.error(`SendGrid 發送郵件失敗: ${error.message}`);
      if (error.response) {
        this.logger.error(
          `SendGrid API 錯誤詳情: ${JSON.stringify({
            statusCode: error.response.statusCode,
            body: error.response.body,
            headers: error.response.headers,
          })}`,
        );
      }
      throw new Error(`SendGrid 郵件發送失敗: ${error.message}`);
    }
  }
}
