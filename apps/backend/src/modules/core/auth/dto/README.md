# Auth DTO 重構說明

## 新的目錄結構

```
dto/
├── common/                    # 共用基礎類
│   ├── base-email.dto.ts     # 基礎電子郵件驗證
│   ├── base-password.dto.ts  # 基礎密碼驗證
│   └── index.ts
├── requests/                  # 請求 DTO
│   ├── auth/                 # 認證相關請求
│   │   ├── login.dto.ts
│   │   ├── refresh-token.dto.ts
│   │   ├── impersonate-tenant.dto.ts
│   │   └── index.ts
│   ├── account/              # 帳戶管理請求
│   │   ├── forgot-password.dto.ts
│   │   ├── reset-password.dto.ts
│   │   ├── change-password.dto.ts
│   │   ├── update-profile.dto.ts
│   │   └── index.ts
│   ├── register/             # 註冊相關請求
│   │   ├── stage-one-register.dto.ts
│   │   ├── stage-two-register.dto.ts
│   │   ├── join-tenant.dto.ts
│   │   ├── accept-invitation.dto.ts
│   │   ├── check-tenant-uniqueness.dto.ts
│   │   └── index.ts
│   └── index.ts
├── responses/                 # 響應 DTO
│   ├── auth/                 # 認證相關響應
│   │   ├── login-response.dto.ts
│   │   ├── refresh-token-response.dto.ts
│   │   └── index.ts
│   ├── register/             # 註冊相關響應
│   │   ├── stage-one-register-response.dto.ts
│   │   ├── complete-registration-response.dto.ts
│   │   ├── tenant-uniqueness-response.dto.ts
│   │   └── index.ts
│   ├── common/               # 共用響應類型
│   │   ├── jwt-user.dto.ts
│   │   └── index.ts
│   └── index.ts
├── validation/               # 自定義驗證器（預留）
└── index.ts                  # 主導出文件
```

## 重構優勢

### 1. 清晰的職責分離

- **requests/**: 所有輸入 DTO，按功能模組分類
- **responses/**: 所有輸出 DTO，按功能模組分類
- **common/**: 可重用的基礎類和共用邏輯

### 2. 基礎類重用

- `BaseEmailDto`: 提供標準電子郵件驗證
- `BasePasswordDto`: 提供基礎密碼驗證（6字元）
- `BaseStrongPasswordDto`: 提供強密碼驗證（8字元）

### 3. 向後兼容性

主 `index.ts` 文件提供別名導出，確保現有代碼不會中斷：

```typescript
export { LoginRequestDto as LoginDto } from './requests/auth/login.dto';
```

### 4. 一致的命名規範

- 請求 DTO: `*RequestDto`
- 響應 DTO: `*ResponseDto`
- 基礎類: `Base*Dto`

## 使用方式

### 新的導入方式（推薦）

```typescript
import { LoginRequestDto, LoginResponseDto } from '@/modules/core/auth/dto';
```

### 舊的導入方式（仍然支援）

```typescript
import { LoginDto } from '@/modules/core/auth/dto';
```

### 按類別導入

```typescript
// 只導入請求 DTO
import { LoginRequestDto } from '@/modules/core/auth/dto/requests';

// 只導入響應 DTO
import { LoginResponseDto } from '@/modules/core/auth/dto/responses';

// 導入基礎類
import { BaseEmailDto } from '@/modules/core/auth/dto/common';
```

## 遷移指南

1. **逐步遷移**: 可以逐步將現有代碼遷移到新的 DTO 名稱
2. **測試驗證**: 確保所有現有功能正常運作
3. **清理舊文件**: 確認遷移完成後，可以刪除舊的 DTO 文件

## 下一步

1. 更新 Controller 和 Service 中的 DTO 引用
2. 更新 API 文檔
3. 添加單元測試
4. 考慮添加自定義驗證器到 `validation/` 目錄
