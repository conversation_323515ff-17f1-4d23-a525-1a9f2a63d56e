import { ApiProperty } from '@nestjs/swagger';

/**
 * 租戶資訊 DTO
 */
export class TenantInfoDto {
  @ApiProperty({ description: '租戶 ID' })
  id: string;

  @ApiProperty({ description: '租戶名稱' })
  name: string;

  @ApiProperty({ description: '租戶域名' })
  domain: string;

  @ApiProperty({ description: '租戶子域名', required: false })
  subdomain?: string;

  @ApiProperty({ description: '租戶狀態' })
  status: string;
}

/**
 * 完整註冊響應 DTO
 * 用於返回完整註冊成功後的資訊
 */
export class CompleteRegistrationResponseDto {
  @ApiProperty({ description: '用戶資訊' })
  user: {
    id: string;
    email: string;
    name: string;
    user_type: 'system' | 'tenant';
    tenant_id: string | null;
    role: string;
  };

  @ApiProperty({ description: '租戶資訊', type: TenantInfoDto })
  tenant: TenantInfoDto;

  @ApiProperty({ description: '存取令牌' })
  access_token: string;

  @ApiProperty({ description: '註冊完成訊息' })
  message: string;
}
