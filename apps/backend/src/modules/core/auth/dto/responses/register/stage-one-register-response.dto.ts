import { ApiProperty } from '@nestjs/swagger';

/**
 * 第一階段註冊響應 DTO
 * 用於返回第一階段註冊成功後的資訊
 */
export class StageOneRegisterResponseDto {
  @ApiProperty({ description: '第一階段 token，用於第二階段註冊' })
  stage_one_token: string;

  @ApiProperty({ description: '使用者 ID' })
  user_id: string;

  @ApiProperty({ description: '使用者信箱' })
  email: string;

  @ApiProperty({ description: '使用者名稱' })
  name: string;

  @ApiProperty({ description: '下一步指示' })
  message: string;
}
