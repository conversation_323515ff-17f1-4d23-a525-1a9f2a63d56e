import { ApiProperty } from '@nestjs/swagger';

/**
 * 租戶唯一性檢查響應 DTO
 * 用於返回租戶唯一性檢查結果
 */
export class TenantUniquenessResponseDto {
  @ApiProperty({ description: '是否可用' })
  available: boolean;

  @ApiProperty({ description: '檢查結果訊息' })
  message: string;

  @ApiProperty({ description: '衝突的欄位', required: false })
  conflicts?: string[];

  @ApiProperty({ description: '建議的替代方案', required: false })
  suggestions?: {
    company_name?: string[];
    subdomain?: string[];
  };
}
