import { IsDefined, IsString, MinLength } from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';

/**
 * 基礎密碼 DTO
 * 提供密碼驗證的共用基礎類
 */
export abstract class BasePasswordDto {
  @ApiProperty({ example: 'password123' })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必須是字串' })
  @MinLength(6, { message: '密碼至少6個字元' })
  password: string;
}

/**
 * 基礎密碼 DTO (8字元版本)
 * 提供更嚴格密碼驗證的共用基礎類
 */
export abstract class BaseStrongPasswordDto {
  @ApiProperty({
    example: 'password123',
    description: '密碼（至少8個字元）',
  })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必須是字串' })
  @MinLength(8, { message: '密碼至少8個字元' })
  password: string;
}
