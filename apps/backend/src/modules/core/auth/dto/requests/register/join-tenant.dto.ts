import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, IsUUID } from 'class-validator';

/**
 * 申請加入租戶請求 DTO
 * 用於處理使用者申請加入現有租戶
 */
export class JoinTenantRequestDto {
  @ApiProperty({
    description: '第一階段註冊產生的使用者 Token',
    example: 'stage-one-token-uuid',
  })
  @IsString({ message: 'Stage One Token 必須是字串' })
  @IsNotEmpty({ message: 'Stage One Token 不能為空' })
  stage_one_token: string;

  @ApiProperty({
    description: '要申請加入的租戶 ID',
    example: 'tenant-uuid-example',
  })
  @IsString({ message: '租戶 ID 必須是字串' })
  @IsUUID(4, { message: '租戶 ID 必須是有效的 UUID' })
  @IsNotEmpty({ message: '租戶 ID 不能為空' })
  tenant_id: string;

  @ApiProperty({
    description: '申請訊息',
    example: '我希望加入貴公司的 HorizAI 平台，我有相關的工作經驗...',
    required: false,
  })
  @IsString({ message: '申請訊息必須是字串' })
  @IsOptional()
  message?: string;

  @ApiProperty({
    description: '申請人部門',
    example: '資訊部',
    required: false,
  })
  @IsString({ message: '部門必須是字串' })
  @IsOptional()
  department?: string;

  @ApiProperty({
    description: '申請人職稱',
    example: '軟體工程師',
    required: false,
  })
  @IsString({ message: '職稱必須是字串' })
  @IsOptional()
  title?: string;
}
