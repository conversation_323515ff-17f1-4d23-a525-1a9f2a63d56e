import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty } from 'class-validator';

/**
 * 檢查租戶唯一性請求 DTO
 * 用於檢查公司名稱和網域是否已被使用
 */
export class CheckTenantUniquenessRequestDto {
  @ApiProperty({
    description: '公司名稱',
    example: 'HorizAI 科技股份有限公司',
  })
  @IsString({ message: '公司名稱必須是字串' })
  @IsNotEmpty({ message: '公司名稱不能為空' })
  company_name: string;

  @ApiProperty({
    description: '公司網域',
    example: 'horizai',
    required: false,
  })
  @IsString({ message: '公司網域必須是字串' })
  @IsOptional()
  domain?: string;

  @ApiProperty({
    description: '國家/地區代碼',
    example: 'TW',
    default: 'TW',
    required: false,
  })
  @IsString({ message: '國家代碼必須是字串' })
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: 'Email 網域（用於檢查是否與現有公司衝突）',
    example: 'horizai.com',
    required: false,
  })
  @IsString({ message: 'Email 網域必須是字串' })
  @IsOptional()
  email_domain?: string;
}
