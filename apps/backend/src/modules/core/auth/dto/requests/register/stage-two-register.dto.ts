import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional, IsNotEmpty, IsUUID } from 'class-validator';

/**
 * 第二階段註冊請求 DTO
 * 用於處理公司資訊設定和租戶建立
 */
export class StageTwoRegisterRequestDto {
  @ApiProperty({
    description: '第一階段註冊產生的使用者 Token',
    example: 'stage-one-token-uuid',
  })
  @IsString({ message: 'Stage One Token 必須是字串' })
  @IsNotEmpty({ message: 'Stage One Token 不能為空' })
  stageOneToken: string;

  @ApiProperty({
    description: '公司名稱',
    example: 'HorizAI 科技股份有限公司',
  })
  @IsString({ message: '公司名稱必須是字串' })
  @IsNotEmpty({ message: '公司名稱不能為空' })
  companyName: string;

  @ApiProperty({
    description: '公司網域',
    example: 'horizai',
  })
  @IsString({ message: '公司網域必須是字串' })
  @IsNotEmpty({ message: '公司網域不能為空' })
  companyDomain: string;

  @ApiProperty({
    description: '公司規模',
    enum: ['1-10', '11-50', '51-200', '201-500', '500+'],
    example: '11-50',
    required: false,
  })
  @IsString({ message: '公司規模必須是字串' })
  @IsOptional()
  companySize?: string;

  @ApiProperty({
    description: '產業類型',
    example: '科技業',
    required: false,
  })
  @IsString({ message: '產業類型必須是字串' })
  @IsOptional()
  industry?: string;

  @ApiProperty({
    description: '國家/地區代碼',
    example: 'TW',
    default: 'TW',
    required: false,
  })
  @IsString({ message: '國家代碼必須是字串' })
  @IsOptional()
  country?: string;

  @ApiProperty({
    description: '部門',
    example: '資訊部',
    required: false,
  })
  @IsString({ message: '部門必須是字串' })
  @IsOptional()
  department?: string;

  @ApiProperty({
    description: '職稱',
    example: '系統管理員',
    required: false,
  })
  @IsString({ message: '職稱必須是字串' })
  @IsOptional()
  title?: string;
}
