import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsNotEmpty } from 'class-validator';
import { BaseStrongPasswordDto } from '../../common';

/**
 * 接受邀請請求 DTO
 * 用於處理用戶接受邀請的請求
 */
export class AcceptInvitationRequestDto extends BaseStrongPasswordDto {
  @ApiProperty({ description: '邀請 token' })
  @IsString()
  @IsNotEmpty()
  token: string;

  @ApiProperty({ description: '使用者名稱' })
  @IsString()
  @IsNotEmpty()
  name: string;
}
