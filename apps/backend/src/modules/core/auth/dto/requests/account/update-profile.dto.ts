import {
  IsString,
  IsOptional,
  IsObject,
  IsArray,
  ValidateNested,
  IsUrl,
  IsEmail,
  MinLength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { Express } from 'express';

/**
 * 社交連結 DTO
 */
export class SocialLinkDto {
  @ApiProperty({ description: '社交平台名稱' })
  @IsString()
  platform: string;

  @ApiProperty({ description: '社交平台連結' })
  @IsUrl()
  url: string;
}

/**
 * 更新個人資料請求 DTO
 * 用於處理用戶更新個人資料的請求
 */
export class UpdateProfileRequestDto {
  @ApiProperty({ description: '使用者名稱', required: false })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: '電子郵件', required: false })
  @IsEmail()
  @IsOptional()
  email?: string;

  @ApiProperty({ description: '電話號碼', required: false })
  @IsString()
  @IsOptional()
  phone?: string;

  @ApiProperty({ description: '職稱', required: false })
  @IsString()
  @IsOptional()
  title?: string;

  @ApiProperty({ description: '公司', required: false })
  @IsString()
  @IsOptional()
  company?: string;

  @ApiProperty({ description: '部門', required: false })
  @IsString()
  @IsOptional()
  department?: string;

  @ApiProperty({ description: '地點', required: false })
  @IsString()
  @IsOptional()
  location?: string;

  @ApiProperty({ description: '個人簡介', required: false })
  @IsString()
  @IsOptional()
  bio?: string;

  @ApiProperty({ description: '社交連結', required: false, type: [SocialLinkDto] })
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SocialLinkDto)
  @IsOptional()
  social_links?: SocialLinkDto[];

  @ApiProperty({ description: '個人偏好設定', required: false })
  @IsObject()
  @IsOptional()
  preferences?: Record<string, any>;

  @ApiProperty({ description: '頭像檔案', required: false, type: 'string', format: 'binary' })
  @IsOptional()
  avatar?: Express.Multer.File;

  @ApiPropertyOptional({ example: 'oldPassword123', description: '目前密碼', required: false })
  @IsString()
  @IsOptional()
  current_password?: string;

  @ApiPropertyOptional({ example: 'newPassword123', description: '新密碼', required: false })
  @IsString()
  @MinLength(8)
  @IsOptional()
  new_password?: string;
}
