import { ApiProperty } from '@nestjs/swagger';
import { IsEmail, IsString, MinLength, Matches, IsOptional } from 'class-validator';
import { BaseEmailDto } from '../../common/base-email.dto';
import { BasePasswordDto } from '../../common/base-password.dto';

/**
 * 第一階段註冊請求 DTO
 * 用於處理使用者個人帳號建立
 */
export class StageOneRegisterRequestDto extends BaseEmailDto {
  @ApiProperty({
    description: '使用者姓名',
    example: '王小明',
  })
  @IsString({ message: '姓名必須是字串' })
  @MinLength(1, { message: '姓名不能為空' })
  name: string;

  @ApiProperty({
    description: '租戶/公司名稱',
    example: 'HorizAI Tech',
  })
  @IsString({ message: '租戶名稱必須是字串' })
  @MinLength(1, { message: '租戶名稱不能為空' })
  tenant_name: string;

  @ApiProperty({
    description: '密碼',
    example: 'Password123!',
  })
  @IsString({ message: '密碼必須是字串' })
  @MinLength(8, { message: '密碼至少需要 8 個字元' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: '密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字元',
  })
  password: string;

  @ApiProperty({
    description: '確認密碼',
    example: 'Password123!',
  })
  @IsString({ message: '確認密碼必須是字串' })
  confirm_password: string;

  @ApiProperty({
    description: '同意服務條款',
    example: true,
    required: false,
  })
  @IsOptional()
  agree_to_terms?: boolean;
}
