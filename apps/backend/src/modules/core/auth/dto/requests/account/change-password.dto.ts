import { ApiProperty } from '@nestjs/swagger';
import { IsString, MinLength, Matches } from 'class-validator';
import { BasePasswordDto } from '../../common/base-password.dto';

/**
 * 修改密碼請求 DTO
 * 用於處理使用者修改密碼的請求
 */
export class ChangePasswordRequestDto extends BasePasswordDto {
  @ApiProperty({
    description: '目前密碼',
    example: 'currentPassword123!',
  })
  @IsString({ message: '目前密碼必須是字串' })
  @MinLength(8, { message: '密碼至少需要 8 個字元' })
  currentPassword: string;

  @ApiProperty({
    description: '新密碼',
    example: 'newPassword123!',
  })
  @IsString({ message: '新密碼必須是字串' })
  @MinLength(8, { message: '新密碼至少需要 8 個字元' })
  @Matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, {
    message: '新密碼必須包含至少一個大寫字母、一個小寫字母、一個數字和一個特殊字元',
  })
  newPassword: string;

  @ApiProperty({
    description: '確認新密碼',
    example: 'newPassword123!',
  })
  @IsString({ message: '確認密碼必須是字串' })
  confirmPassword: string;
}
