import { IsBoolean, IsIn, IsOptional, IsDefined, IsString, MinLength } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { BaseEmailDto } from '../../common';

/**
 * 登入請求 DTO
 * 用於處理用戶登入請求
 */
export class LoginRequestDto extends BaseEmailDto {
  @ApiProperty({ example: 'password123' })
  @IsDefined({ message: '密碼必填' })
  @IsString({ message: '密碼必須是字串' })
  @MinLength(6, { message: '密碼至少6個字元' })
  password: string;

  @ApiPropertyOptional({ example: false })
  @IsOptional()
  @IsBoolean({ message: 'remember_me 必須是布林值' })
  remember_me?: boolean;

  @ApiPropertyOptional({
    description: '使用者類型（可選，如果不提供則自動判斷）',
    enum: ['system', 'tenant'],
    example: 'tenant',
  })
  @IsOptional()
  @IsIn(['system', 'tenant'])
  user_type?: 'system' | 'tenant';
}
