import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { AppAbility, Actions, Subjects } from '../../../types/models/casl.model';
import { createMongoAbility } from '@casl/ability';
import { RoleHierarchyService } from '../../admin/roles/services/role-hierarchy.service';

/**
 * 使用者類型枚舉
 */
enum UserType {
  SYSTEM = 'system',
  TENANT = 'tenant',
}

/**
 * 能力工廠 - 專注於建立使用者的 CASL Ability 物件
 * 移植自 CaslAbilityFactory，但更加專注於 Auth 流程
 */
@Injectable()
export class AbilityFactory {
  private readonly logger = new Logger(AbilityFactory.name);

  constructor(
    private prisma: PrismaService,
    private roleHierarchyService: RoleHierarchyService,
  ) {}

  /**
   * 為使用者建立 CASL Ability 物件
   * 流程：
   * 1. 獲取使用者角色
   * 2. 獲取角色的權限 (含繼承權限)
   * 3. 轉換為 CASL 規則
   * 4. 建立 Ability 物件
   */
  async createForUser(userPayload: {
    id: string;
    tenant_id?: string | null;
    userType?: string;
  }): Promise<AppAbility> {
    try {
      // 1. 確定使用者類型
      const userType = userPayload.userType || (await this.determineUserType(userPayload.id));
      if (!userType) {
        this.logger.warn(`使用者 ${userPayload.id} 類型不明確，建立空白權限`);
        return this.createEmptyAbility();
      }

      // 2. 獲取使用者的角色
      const userRoles = await this.getUserRoles(userPayload.id, userType as any);
      if (!userRoles.length) {
        this.logger.warn(`使用者 ${userPayload.id} 無角色，建立空白權限`);
        return this.createEmptyAbility();
      }

      // 3. 獲取所有角色的權限
      const roleIds = userRoles.map((role) => role.id);
      const permissions = await this.getEffectivePermissions(roleIds);

      // 4. 建立 CASL 規則
      const rules = permissions.map((perm) => ({
        action: perm.action as Actions,
        subject: perm.subject as Subjects,
        conditions: this.processConditions(perm.conditions, userPayload),
      }));

      // 5. 建立並返回 Ability 物件
      return createMongoAbility<[Actions, Subjects]>(rules);
    } catch (error) {
      this.logger.error(
        `為使用者 ${userPayload.id} 建立權限物件時發生錯誤: ${error.message}`,
        error.stack,
      );
      return this.createEmptyAbility();
    }
  }

  /**
   * 確定使用者類型 (系統或租戶)
   */
  private async determineUserType(userId: string): Promise<UserType | null> {
    try {
      // 先嘗試查找系統使用者
      const systemUser = await this.prisma.system_users.findUnique({
        where: { id: userId },
      });

      if (systemUser) {
        return UserType.SYSTEM;
      }

      // 再嘗試查找租戶使用者
      const tenantUser = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
      });

      if (tenantUser) {
        return UserType.TENANT;
      }

      this.logger.warn(`無法確定使用者 ${userId} 的類型`);
      return null;
    } catch (error) {
      this.logger.error(`確定使用者 ${userId} 類型時發生錯誤`, error.stack);
      return null;
    }
  }

  /**
   * 獲取使用者角色
   */
  private async getUserRoles(userId: string, userType: UserType): Promise<any[]> {
    try {
      if (userType === UserType.SYSTEM) {
        // 系統使用者角色
        const systemUserRoles = await this.prisma.system_user_roles.findMany({
          where: { system_user_id: userId },
          include: { role: true },
        });
        return systemUserRoles.map((ur) => ur.role);
      } else {
        // 租戶使用者角色
        const tenantUserRoles = await this.prisma.tenant_user_roles.findMany({
          where: { tenant_user_id: userId },
          include: { role: true },
        });
        return tenantUserRoles.map((ur) => ur.role);
      }
    } catch (error) {
      this.logger.error(`獲取使用者 ${userId} 角色時發生錯誤`, error.stack);
      return [];
    }
  }

  /**
   * 獲取角色的有效權限 (包含繼承權限)
   */
  private async getEffectivePermissions(roleIds: string[]): Promise<any[]> {
    try {
      // 對每個角色獲取有效權限
      const allPermissionsPromises = roleIds.map(async (roleId) => {
        const effectivePermissionIds =
          await this.roleHierarchyService.getEffectivePermissions(roleId);
        return this.prisma.permissions.findMany({
          where: { id: { in: effectivePermissionIds } },
        });
      });

      // 合併所有權限並去重
      const allPermissions = await Promise.all(allPermissionsPromises);
      const flattenedPermissions = allPermissions.flat();

      // 使用 Map 去重
      const uniquePermissions = new Map();
      flattenedPermissions.forEach((perm) => {
        const key = `${perm.action}:${perm.subject}`;
        uniquePermissions.set(key, perm);
      });

      return Array.from(uniquePermissions.values());
    } catch (error) {
      this.logger.error(`獲取角色權限時發生錯誤`, error.stack);
      return [];
    }
  }

  /**
   * 處理權限條件中的變數
   */
  private processConditions(
    conditions: any,
    userContext: { id: string; tenant_id?: string | null },
  ): any {
    if (!conditions) {
      return undefined;
    }

    const processed = { ...conditions };

    // 替換常見變數
    for (const key in processed) {
      if (typeof processed[key] === 'string') {
        if (processed[key] === '${user.tenant_id}') {
          processed[key] = userContext.tenant_id;
        } else if (processed[key] === '${user.id}') {
          processed[key] = userContext.id;
        }
      }
    }

    return processed;
  }

  /**
   * 創建空白權限物件
   */
  private createEmptyAbility(): AppAbility {
    return createMongoAbility<[Actions, Subjects]>([]);
  }
}
