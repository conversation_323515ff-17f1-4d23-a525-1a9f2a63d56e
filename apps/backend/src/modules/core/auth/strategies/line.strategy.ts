import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-line-auth';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';
import { OAuthUserInfo } from '../interfaces/oauth-user-info.interface';

@Injectable()
export class LineStrategy extends PassportStrategy(Strategy, 'line') {
  private readonly logger = new Logger(LineStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      channelID: configService.get<string>('LINE_CLIENT_ID') || '',
      channelSecret: configService.get<string>('LINE_CLIENT_SECRET') || '',
      callbackURL: configService.get<string>('LINE_REDIRECT_URI') || '',
      scope: ['profile', 'openid', 'email'],
      botPrompt: 'normal',
      uiLocales: 'zh-TW',
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string,
    params: any,
    profile: any,
    done: any,
  ): Promise<any> {
    this.logger.debug(
      `LINE profile received: ${profile.id} - ${profile.displayName || 'No display name'}`,
    );

    try {
      const oauthUserInfo: OAuthUserInfo = {
        provider: 'line',
        providerId: profile.id,
        email: profile.email,
        name: profile.displayName || `LINE User ${profile.id.substring(0, 5)}`,
        avatar: profile.pictureUrl || undefined,
      };

      if (!oauthUserInfo.email) {
        // Handle case where email is not provided by LINE
        // This might require a different flow, e.g., asking user to enter email
        // For now, we will throw an error.
        const err = new Error(
          'Email not provided by LINE. Please ensure email permission is granted.',
        );
        this.logger.error(err.message);
        return done(err, false);
      }

      const { user, userType } = await this.authService.findOrCreateUserFromOAuth(oauthUserInfo);

      const payload = {
        userType: userType,
        ...user,
      };

      done(null, payload);
    } catch (err) {
      this.logger.error('Error during LINE OAuth user validation', err);
      done(err, false);
    }
  }
}
