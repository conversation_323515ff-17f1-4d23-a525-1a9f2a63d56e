import {
  Injectable,
  UnauthorizedException,
  Logger,
  InternalServerErrorException,
} from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { ConfigService } from '@nestjs/config';
import { PrismaService } from '../../prisma/prisma.service';
import { Request } from 'express';
import { JwtUser } from '../../../../types/jwt-user.type';
import { Prisma, TenantUserStatus } from '@prisma/client';
import { CaslAbilityFactory } from '../../../../casl/ability/casl-ability.factory';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy, 'jwt') {
  private readonly logger = new Logger(JwtStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
    private readonly abilityFactory: CaslAbilityFactory,
  ) {
    const jwtAccessSecret = configService.get<string>('JWT_ACCESS_SECRET');
    if (!jwtAccessSecret) {
      throw new Error('JWT_ACCESS_SECRET is not defined in the environment');
    }

    super({
      jwtFromRequest: ExtractJwt.fromExtractors([
        (req: Request) => {
          const token = req.cookies?.auth_token;
          if (token) return token;
          return null;
        },
        ExtractJwt.fromAuthHeaderAsBearerToken(),
      ]),
      ignoreExpiration: false,
      secretOrKey: jwtAccessSecret,
      passReqToCallback: true,
    });
  }

  async validate(req: Request, payload: JwtUser): Promise<any> {
    this.logger.debug(`JWT validate payload: ${JSON.stringify(payload)}`);

    const userId = payload.sub;
    if (!userId) {
      throw new UnauthorizedException('Invalid token payload: missing subject');
    }

    const userType = payload.user_type;
    if (!userType || (userType !== 'system' && userType !== 'tenant')) {
      throw new UnauthorizedException('Invalid token payload: missing or invalid user_type');
    }

    let user: any;
    let tenantId: string | null = null;

    try {
      if (userType === 'system') {
        user = await this.prisma.system_users.findUnique({ where: { id: userId } });
        if (!user || user.status !== 'active') {
          throw new UnauthorizedException('System user not found or is inactive.');
        }
      } else {
        // userType is 'tenant'
        const tenantUser = await this.prisma.tenant_users.findUnique({ where: { id: userId } });
        if (!tenantUser || tenantUser.status !== TenantUserStatus.ACTIVE) {
          throw new UnauthorizedException('Tenant user not found or is inactive.');
        }
        user = tenantUser;
        tenantId = tenantUser.tenant_id;
      }

      // Ensure password is not returned and add JWT fields
      const { password, ...userWithoutPassword } = user;
      
      req.ability = await this.abilityFactory.createForUser({
        user_id: userId,
        tenant_id: tenantId,
        user_type: userType,
      });
      this.logger.debug(`Successfully created and attached ability for user ${userId}`);

      // Return user object that matches JwtUser interface
      return {
        ...userWithoutPassword,
        sub: userId, // Add sub field from JWT payload
        user_type: userType, // Ensure user_type is included
        tenant_id: tenantId,
        iat: payload.iat,
        exp: payload.exp,
      };
    } catch (error) {
      this.logger.error(`Error during user validation or ability creation for user ${userId}:`, error);
      if (error instanceof UnauthorizedException) {
        throw error;
      }
      throw new InternalServerErrorException('An error occurred during authentication.');
    }
  }
}

@Injectable()
export class JwtRefreshStrategy extends PassportStrategy(Strategy, 'jwt-refresh') {
  constructor(private readonly configService: ConfigService) {
    const jwtAccessSecret = configService.get<string>('JWT_ACCESS_SECRET');
    if (!jwtAccessSecret) {
      throw new Error('JWT_ACCESS_SECRET is not defined in the environment');
    }

    super({
      jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
      ignoreExpiration: true, // IMPORTANT: Only for refresh token validation
      secretOrKey: jwtAccessSecret,
      passReqToCallback: false,
    });
  }

  async validate(payload: JwtUser) {
    // Passport automatically attaches the payload as `req.user`
    return payload;
  }
}
