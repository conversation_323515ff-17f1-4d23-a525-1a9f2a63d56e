import { PassportStrategy } from '@nestjs/passport';
import { Strategy, VerifyCallback, Profile } from 'passport-google-oauth20';
import { Injectable, Logger, UnauthorizedException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { AuthService } from '../auth.service';

@Injectable()
export class GoogleStrategy extends PassportStrategy(Strategy, 'google') {
  private readonly logger = new Logger(GoogleStrategy.name);

  constructor(
    private readonly configService: ConfigService,
    private readonly authService: AuthService,
  ) {
    super({
      clientID: configService.get<string>('GOOGLE_CLIENT_ID') || '',
      clientSecret: configService.get<string>('GOOGLE_CLIENT_SECRET') || '',
      callbackURL: configService.get<string>('GOOGLE_CALLBACK_URL') || '',
      scope: ['email', 'profile'],
      passReqToCallback: false,
    });
  }

  async validate(
    accessToken: string,
    refreshToken: string | undefined,
    profile: Profile,
    done: VerifyCallback,
  ): Promise<any> {
    this.logger.debug(`Google profile received: ${profile.id} - ${profile.displayName}`);
    const { id, name, emails, photos } = profile;

    if (!emails || emails.length === 0) {
      const err = new Error('Google profile does not contain an email address.');
      this.logger.error(err.message);
      return done(err, false);
    }

    try {
      const oauthUserInfo = {
        provider: 'google',
        providerId: id,
        email: emails[0].value,
        name: name ? `${name.givenName} ${name.familyName}` : profile.displayName,
        avatar: photos && photos.length > 0 ? photos[0].value : undefined,
      };

      const { user, userType } = await this.authService.findOrCreateUserFromOAuth(oauthUserInfo);

      const payload = {
        userType: userType,
        ...user,
      };

      done(null, payload);
    } catch (err) {
      this.logger.error('Error during Google OAuth user validation', err);
      done(err, false);
    }
  }
}
