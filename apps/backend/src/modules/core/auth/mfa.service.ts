import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { randomBytes, createHmac } from 'crypto';

/**
 * Minimal TOTP implementation without external dependencies.
 */
function generateTotp(secret: string, timeStep = 30, digits = 6): string {
  const key = Buffer.from(secret, 'hex');
  const counter = Math.floor(Date.now() / 1000 / timeStep);
  const buf = Buffer.alloc(8);
  buf.writeUInt32BE(0, 0);
  buf.writeUInt32BE(counter, 4);
  const hmac = createHmac('sha1', key).update(buf).digest();
  const offset = hmac[hmac.length - 1] & 0xf;
  const code =
    ((hmac[offset] & 0x7f) << 24) |
    ((hmac[offset + 1] & 0xff) << 16) |
    ((hmac[offset + 2] & 0xff) << 8) |
    (hmac[offset + 3] & 0xff);
  const otp = code % 10 ** digits;
  return otp.toString().padStart(digits, '0');
}

function verifyTotp(token: string, secret: string, window = 1, timeStep = 30, digits = 6): boolean {
  const key = Buffer.from(secret, 'hex');
  const current = Math.floor(Date.now() / 1000 / timeStep);
  for (let i = -window; i <= window; i++) {
    const buf = Buffer.alloc(8);
    buf.writeUInt32BE(0, 0);
    buf.writeUInt32BE(current + i, 4);
    const hmac = createHmac('sha1', key).update(buf).digest();
    const offset = hmac[hmac.length - 1] & 0xf;
    const code =
      ((hmac[offset] & 0x7f) << 24) |
      ((hmac[offset + 1] & 0xff) << 16) |
      ((hmac[offset + 2] & 0xff) << 8) |
      (hmac[offset + 3] & 0xff);
    const otp = code % 10 ** digits;
    if (otp.toString().padStart(digits, '0') === token) return true;
  }
  return false;
}

@Injectable()
export class MfaService {
  constructor(private readonly prisma: PrismaService) {}

  // TODO: 為新的分離用戶架構 (SystemUser/TenantUser) 重新實現 MFA 功能
  // 當前所有 MFA 功能都暫時禁用，直到新架構完成

  /**
   * 暫時返回 false，表示 MFA 功能未啟用
   */
  async isMfaEnabled(userId: string, userType: 'system' | 'tenant'): Promise<boolean> {
    return false;
  }

  /**
   * 暫時返回 false，表示無需 MFA 驗證
   */
  async requiresMfaVerification(userId: string, userType: 'system' | 'tenant'): Promise<boolean> {
    return false;
  }

  /**
   * 產生 MFA 秘鑰的存根方法
   */
  async generateSecret(userId: string): Promise<string> {
    // TODO: 根據新架構實作，暫時回傳隨機字串
    return randomBytes(20).toString('hex');
  }

  /**
   * 啟用 MFA 的存根方法
   */
  async enableMfa(userId: string, token: string): Promise<boolean> {
    // TODO: 根據新架構實作 MFA 啟用
    return false;
  }

  /**
   * 停用 MFA 的存根方法
   */
  async disableMfa(userId: string): Promise<boolean> {
    // TODO: 根據新架構實作 MFA 停用
    return false;
  }
}

export { generateTotp, verifyTotp };
