import { Module, forwardRef } from '@nestjs/common';
import { AuthService } from './auth.service';
import { AuthController } from './auth.controller';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { JwtStrategy, JwtRefreshStrategy } from './strategies/jwt.strategy';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { PrismaModule } from '../prisma/prisma.module';
import { CaslModule } from '../../../casl/casl.module';
import { WorkspaceUsersModule } from '../../workspace/users/users.module';
import { GoogleStrategy } from './strategies/google.strategy';
import { LineStrategy } from './strategies/line.strategy';
import { CaslAbilityFactory } from '../../../casl/ability/casl-ability.factory';
import { SystemUserManagementModule } from '../system-user-management/system-user-management.module';
import { TenantUserManagementModule } from '../tenant-user-management/tenant-user-management.module';
import { TenantInvitationsModule } from '../tenant-invitations/tenant-invitations.module';
import { MailModule } from '../mail/mail.module';
import { MfaService } from './mfa.service';
import { CommonModule } from '../../../common/common.module';
import { UserManagementModule } from '../user-management/user-management.module';
import { StorageModule } from '../storage/storage.module';
import { TenantsModule } from '../../admin/tenants/tenants.module';
import { RolesModule } from '../../admin/roles/roles.module';
import { SystemUsersModule } from '../../admin/system-users/system-users.module';
import { TenantUsersModule } from '../../admin/tenant-users/tenant-users.module';
import { APP_GUARD } from '@nestjs/core';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';

@Module({
  imports: [
    PassportModule.register({ defaultStrategy: 'jwt' }),
    JwtModule.registerAsync({
      imports: [ConfigModule],
      useFactory: async (configService: ConfigService) => {
        const secret = configService.get<string>('JWT_ACCESS_SECRET');
        if (!secret) {
          throw new Error('JWT_ACCESS_SECRET is not defined in environment variables.');
        }
        return {
          secret,
          signOptions: {
            expiresIn: configService.get<string | number>('JWT_ACCESS_EXPIRATION_TIME') || '1h',
          },
        };
      },
      inject: [ConfigService],
    }),
    PrismaModule,
    CaslModule,
    WorkspaceUsersModule,
    SystemUserManagementModule,
    TenantUserManagementModule,
    TenantInvitationsModule,
    MailModule,
    CommonModule,
    UserManagementModule,
    StorageModule,
    TenantsModule,
    RolesModule,
    forwardRef(() => SystemUsersModule),
    forwardRef(() => TenantUsersModule),
  ],
  controllers: [AuthController],
  providers: [
    AuthService,
    JwtStrategy,
    JwtRefreshStrategy,
    {
      provide: GoogleStrategy,
      useFactory: (configService: ConfigService, authService: AuthService) => {
        const clientId = configService.get<string>('GOOGLE_CLIENT_ID');
        const clientSecret = configService.get<string>('GOOGLE_CLIENT_SECRET');

        if (clientId && clientSecret) {
          return new GoogleStrategy(configService, authService);
        }
        return null;
      },
      inject: [ConfigService, AuthService],
    },
    {
      provide: LineStrategy,
      useFactory: (configService: ConfigService, authService: AuthService) => {
        const clientId = configService.get<string>('LINE_CLIENT_ID');
        const clientSecret = configService.get<string>('LINE_CLIENT_SECRET');

        if (clientId && clientSecret) {
          return new LineStrategy(configService, authService);
        }
        return null;
      },
      inject: [ConfigService, AuthService],
    },
    CaslAbilityFactory,
    MfaService,
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PoliciesGuard,
    },
  ],
  exports: [AuthService, JwtModule, CaslAbilityFactory, MfaService],
})
export class AuthModule {}
