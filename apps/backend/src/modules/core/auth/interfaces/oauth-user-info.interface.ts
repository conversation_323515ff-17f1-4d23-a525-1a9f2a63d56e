export interface OAuthUserInfo {
  provider: string;
  providerId: string;
  email: string;
  name?: string;
  avatar?: string;
  locale?: string;
  [key: string]: any; // 允許額外的提供者特定屬性
}

export interface GoogleOAuthProfile {
  id: string;
  name?: string;
  email?: string;
  picture?: string;
  locale?: string;
  verified_email?: boolean;
}

export interface LineOAuthProfile {
  userId: string;
  displayName?: string;
  pictureUrl?: string;
  statusMessage?: string;
}
