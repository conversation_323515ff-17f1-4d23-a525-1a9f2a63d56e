import {
  Injectable,
  Logger,
  UnauthorizedException,
  BadRequestException,
  InternalServerErrorException,
  NotFoundException,
  ConflictException,
  ForbiddenException,
  Inject,
  forwardRef,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { createHmac, createHash } from 'crypto';
import { addDays } from 'date-fns';
import { Prisma, TenantUserStatus } from '@prisma/client';
import { SystemUser, TenantUser, ISystemUser, ITenantUser } from '../../../types/models/user.model';
import { plainToInstance } from 'class-transformer';
import { Response } from 'express';

import { PrismaService } from '../prisma/prisma.service';
import { CaslAbilityFactory } from '../../../casl/ability/casl-ability.factory';
import {
  LoginDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  ChangePasswordRequestDto as ChangePasswordDto,
  AcceptInvitationDto,
  StageOneRegisterDto,
  CheckTenantUniquenessRequestDto,
  StageTwoRegisterRequestDto,
  JoinTenantRequestDto,
} from './dto';
import { SystemUsersService } from '../../../modules/admin/system-users/system-users.service';
import { TenantUsersService } from '../../../modules/admin/tenant-users/tenant-users.service';
import { TenantInvitationsService } from '../tenant-invitations/tenant-invitations.service';
import { LoginResponseDto, UserInfoDto } from './dto/responses/auth/login-response.dto';

export type AuthResponse = {
  accessToken: string;
  refreshToken: string;
  userType: 'system' | 'tenant';
  user: ISystemUser | ITenantUser;
};

export interface OAuthUserInfo {
  provider: string;
  providerId: string;
  email: string;
  name?: string;
  avatar?: string;
}

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly caslAbilityFactory: CaslAbilityFactory,
    @Inject(forwardRef(() => SystemUsersService))
    private readonly systemUserService: SystemUsersService,
    @Inject(forwardRef(() => TenantUsersService))
    private readonly tenantUserService: TenantUsersService,
    private readonly tenantInvitationsService: TenantInvitationsService,
  ) {
    this.stageOneRegister = this.stageOneRegister.bind(this);
  }

  private _hashRefreshToken(token: string): string {
    const salt = this.configService.get<string>('REFRESH_TOKEN_SALT') || 'default-refresh-token-salt-key';
    return createHmac('sha256', salt).update(token).digest('hex');
  }

  async validateUnifiedUser(
    email: string,
    pass: string,
  ): Promise<{ user: any; userType: 'system' | 'tenant' } | null> {
    const systemUser = await this.prisma.system_users.findUnique({
      where: { email },
    });
    
    if (systemUser) {
      const isPasswordValid = await bcrypt.compare(pass, systemUser.password);
      
      if (isPasswordValid) {
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { password, ...result } = systemUser;
        return { user: result, userType: 'system' };
      }
    }

    const tenantUser = await this.prisma.tenant_users.findUnique({
      where: { email },
    });
    
    if (tenantUser) {
      const isPasswordValid = await bcrypt.compare(pass, tenantUser.password);
      
      if (isPasswordValid) {
        if (tenantUser.status !== TenantUserStatus.ACTIVE) {
          throw new UnauthorizedException(
            `Tenant account is not active. Status: ${tenantUser.status}`,
          );
        }
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        const { password, ...result } = tenantUser;
        return { user: result, userType: 'tenant' };
      }
    }

    return null;
  }

  async login(
    loginDto: LoginDto,
    res: Response,
    deviceInfo?: string,
  ): Promise<LoginResponseDto> {
    const validated = await this.validateUnifiedUser(loginDto.email, loginDto.password);
    if (!validated) {
      throw new UnauthorizedException('Invalid credentials.');
    }

    const { user, userType } = validated;
    const { accessToken, refreshToken } = await this.generateTokensForUser(
      user,
      userType,
      deviceInfo,
    );

    this.setAuthCookies(res, accessToken, refreshToken, loginDto.remember_me);

    let userInstance: SystemUser | TenantUser;
    if (userType === 'system') {
      userInstance = plainToInstance(SystemUser, user, {
        excludeExtraneousValues: true,
      });
    } else {
      userInstance = plainToInstance(TenantUser, user, {
        excludeExtraneousValues: true,
      });
    }

    // 生成用戶權限規則
    let abilityRules: any[] = [];
    try {
      const ability = await this.caslAbilityFactory.createForUser({
        user_id: user.id,
        user_type: userType,
        tenant_id: userType === 'tenant' ? (user as any).tenant_id : undefined,
      });
      abilityRules = ability.rules || [];
    } catch (error) {
      this.logger.warn(`Failed to generate ability rules for user ${user.id}:`, error);
    }

    const responseDto = new LoginResponseDto();
    responseDto.access_token = accessToken;
    responseDto.refresh_token = refreshToken;
    responseDto.user = plainToInstance(UserInfoDto, {
      ...userInstance,
      user_type: userType,
    });
    responseDto.user_type = userType;
    responseDto.ability_rules = abilityRules;

    return responseDto;
  }

  private setAuthCookies(
    res: Response,
    accessToken: string,
    refreshToken: string,
    rememberMe?: boolean,
  ) {
    const accessCookieOptions = {
      httpOnly: true,
      secure: this.configService.get('NODE_ENV') === 'production',
      path: '/',
      sameSite: 'lax' as const,
    };

    res.cookie('auth_token', accessToken, accessCookieOptions);

    if (rememberMe) {
      const refreshCookieOptions = {
        ...accessCookieOptions,
        expires: new Date(
          Date.now() +
            this.configService.get<number>('JWT_REFRESH_EXPIRATION_DAYS', 7) *
              24 *
              60 *
              60 *
              1000,
        ),
      };
      res.cookie('refresh_token', refreshToken, refreshCookieOptions);
    }
  }

  async generateTokensForUser(
    user: SystemUser | TenantUser,
    userType: 'system' | 'tenant',
    deviceInfo?: string,
  ): Promise<{ accessToken: string; refreshToken: string }> {
    const payload = {
      sub: user.id,
      email: user.email,
      role: user.role,
      user_type: userType,
      tenant_id: userType === 'tenant' ? (user as TenantUser).tenant_id : undefined,
    };

    const accessToken = await this.jwtService.signAsync(payload, {
      expiresIn: this.configService.get<string>('JWT_ACCESS_EXPIRATION_TIME') || '1h',
      secret: this.configService.get<string>('JWT_ACCESS_SECRET'),
    });

    const refreshToken = uuidv4();
    const hashedRefreshToken = this._hashRefreshToken(refreshToken);
    const refreshTokenExpiresAt = addDays(
      new Date(),
      this.configService.get<number>('JWT_REFRESH_EXPIRATION_DAYS', 7),
    );

    const commonTokenData = {
      token: hashedRefreshToken,
      expires_at: refreshTokenExpiresAt,
    };

    if (userType === 'system') {
      await this.prisma.refresh_tokens.create({
        data: {
          ...commonTokenData,
          system_user_id: user.id,
          user_type: 'system',
          device_info: deviceInfo,
        },
      });
    } else {
      await this.prisma.refresh_tokens.create({
        data: {
          ...commonTokenData,
          tenant_user_id: user.id,
          user_type: 'tenant',
          device_info: deviceInfo,
        },
      });
    }

    return {
      accessToken,
      refreshToken,
    };
  }

  async refreshTokens(refreshToken: string): Promise<AuthResponse> {
    try {
      if (!refreshToken) {
        throw new ForbiddenException('Refresh token missing');
      }

      const hashedRefreshToken = this._hashRefreshToken(refreshToken);

      const storedToken = await this.prisma.refresh_tokens.findFirst({
        where: {
          token: hashedRefreshToken,
          is_valid: true,
          expires_at: {
            gt: new Date(),
          },
        },
      });

      if (!storedToken) {
        throw new ForbiddenException('Refresh token is invalid, expired, or has been revoked.');
      }
      
      const { system_user_id, tenant_user_id, user_type } = storedToken;
      const userId = system_user_id || tenant_user_id;

      if (!userId) {
        throw new ForbiddenException('Invalid refresh token payload.');
      }

      let user: SystemUser | TenantUser | null = null;
      if (user_type === 'system') {
        user = await this.systemUserService.findOne(userId) as SystemUser;
      } else {
        user = await this.tenantUserService.findOne(userId) as TenantUser;
      }

      if (!user) {
        throw new ForbiddenException('User not found.');
      }

      // Invalidate the old refresh token
      await this.prisma.refresh_tokens.update({
        where: { id: storedToken.id },
        data: { is_valid: false, revoked_at: new Date() },
      });

      // Generate new tokens
      const tokens = await this.generateTokensForUser(user, user_type as 'system' | 'tenant');

      const { password, ...userWithoutPassword } = user;

      return {
        ...tokens,
        userType: user_type as 'system' | 'tenant',
        user: userWithoutPassword,
      };
    } catch (error) {
      if (error instanceof ForbiddenException) {
        throw error;
      }
      this.logger.error('Error refreshing token:', error);
      throw new ForbiddenException('Invalid refresh token');
    }
  }

  async revokeAllRefreshTokensForUser(userId: string): Promise<void> {
    this.logger.log(`Revoking all refresh tokens for user ${userId}`);
    await this.prisma.refresh_tokens.updateMany({
      where: {
        OR: [{ system_user_id: userId }, { tenant_user_id: userId }],
        is_valid: true,
      },
      data: { is_valid: false, revoked_at: new Date() },
    });
  }

  async logout(userId: string): Promise<void> {
    await this.revokeAllRefreshTokensForUser(userId);
  }

  async getUnifiedUserPermissions(userId: string): Promise<string[]> {
    this.logger.debug(`Getting unified permissions for user: ${userId}`);

    try {
      let tenantId: string | null = null;
      let userType: 'system' | 'tenant' | null = null;

      const systemUser = await this.prisma.system_users.findUnique({ where: { id: userId } });
      if (systemUser) {
        userType = 'system';
      } else {
        const tenantUser = await this.prisma.tenant_users.findUnique({
          where: { id: userId },
          select: { tenant_id: true },
        });

        if (tenantUser) {
          userType = 'tenant';
          tenantId = tenantUser.tenant_id;
        }
      }

      if (!userType) {
        throw new NotFoundException('User not found when getting permissions.');
      }

      const ability = await this.caslAbilityFactory.createForUser({
        user_id: userId,
        user_type: userType,
        tenant_id: tenantId,
      });

      const permissionStrings = ability.rules
        .filter((rule) => !rule.inverted)
        .map((rule) => `${rule.action}:${rule.subject}`);

      this.logger.debug(
        `Generated ${permissionStrings.length} permissions for user ${userId} using CaslAbilityFactory`,
      );

      return permissionStrings;
    } catch (error) {
      this.logger.error(`Error getting permissions for user ${userId}:`, error);
      return [];
    }
  }

  // =================================================================
  // ==                     密碼與帳戶管理                          ==
  // =================================================================

  async forgotPassword(dto: ForgotPasswordDto): Promise<void> {
    const { email } = dto;
    let user: ISystemUser | ITenantUser | null = null;
    let userType: 'system' | 'tenant' | null = null;

    user = await this.prisma.system_users.findUnique({ where: { email } });
    if (user) {
      userType = 'system';
    } else {
      user = await this.prisma.tenant_users.findUnique({ where: { email } });
      if (user) {
        userType = 'tenant';
      }
    }

    if (!user || !userType) {
      this.logger.warn(`Password reset requested for non-existent user: ${email}`);
      return; // Do not reveal if user exists
    }

    const token = uuidv4();
    const expires = new Date(Date.now() + 3600000); // 1 hour
    const hashedToken = createHash('sha256').update(token).digest('hex');

    const commonTokenData = {
      email: user.email,
      token: hashedToken,
      expires_at: expires,
      user_type: userType,
    };

    if (userType === 'system') {
      await this.prisma.password_reset_tokens.create({
        data: { ...commonTokenData, system_user_id: user.id },
      });
    } else {
      await this.prisma.password_reset_tokens.create({
        data: { ...commonTokenData, tenant_user_id: user.id },
      });
    }

    // TODO: Send email with the unhashed `token`
    this.logger.log(`Password reset token generated for ${email}. Token: ${token}`);
  }

  async resetPassword(dto: ResetPasswordDto): Promise<void> {
    const { token, password } = dto;
    this.logger.debug(`Attempting to reset password with token`);

    if (!token) {
      throw new BadRequestException('Reset token is missing.');
    }

    const hashedToken = createHash('sha256').update(token).digest('hex');

    const tokenRecord = await this.prisma.password_reset_tokens.findFirst({
      where: {
        token: hashedToken,
        used_at: null,
        expires_at: { gt: new Date() },
      },
    });

    if (!tokenRecord) {
      throw new UnauthorizedException('Invalid or expired password reset token.');
    }

    const { user_type: userType, system_user_id, tenant_user_id } = tokenRecord;
    const userId = system_user_id || tenant_user_id;
    if (!userId) {
      throw new InternalServerErrorException('Token record is invalid.');
    }

    const hashedPassword = await bcrypt.hash(password, 10);

    const userModel = userType === 'system' ? this.prisma.system_users : this.prisma.tenant_users;

    // @ts-expect-error Prisma client cannot infer model type dynamically here
    await userModel.update({
      where: { id: userId },
      data: { password: hashedPassword },
    });

    await this.prisma.password_reset_tokens.update({
      where: { id: tokenRecord.id },
      data: { used_at: new Date() },
    });

    await this.revokeAllRefreshTokensForUser(userId);
  }

  async changePassword(
    userId: string,
    userType: 'system' | 'tenant',
    dto: ChangePasswordDto,
  ): Promise<{ message: string }> {
    const { currentPassword, newPassword } = dto;
    this.logger.debug(`Password change attempt for ${userType} user: ${userId}`);

    let user;
    if (userType === 'system') {
      user = await this.prisma.system_users.findUnique({ where: { id: userId } });
    } else {
      user = await this.prisma.tenant_users.findUnique({ where: { id: userId } });
    }

    if (!user) {
      throw new NotFoundException('User not found.');
    }

    const isOldPasswordValid = await bcrypt.compare(currentPassword, user.password);
    if (!isOldPasswordValid) {
      throw new UnauthorizedException('Invalid old password.');
    }

    if (currentPassword === newPassword) {
      throw new BadRequestException('New password cannot be the same as the old password.');
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 12);

    const userModel = userType === 'system' ? this.prisma.system_users : this.prisma.tenant_users;

    // @ts-expect-error Prisma client cannot infer model type dynamically here
    await userModel.update({
      where: { id: userId },
      data: { password: hashedNewPassword, password_last_changed_at: new Date() },
    });

    // For security, revoke all active refresh tokens upon password change
    await this.revokeAllRefreshTokensForUser(userId);

    this.logger.log(`Password changed successfully for ${userType} user ID ${userId}`);
    return { message: 'Password changed successfully.' };
  }

  async updateProfile(
    userId: string,
    userType: 'system' | 'tenant',
    updateData: {
      name?: string;
      phone?: string;
      title?: string;
      department?: string;
      avatar?: any;
    },
  ): Promise<{ success: boolean; user: any }> {
    this.logger.debug(`Updating profile for ${userType} user: ${userId}`, updateData);

    try {
      let userProfile: any;
      const updatePayload: any = {};

      if (updateData.name !== undefined) updatePayload.name = updateData.name;
      if (updateData.phone !== undefined) updatePayload.phone = updateData.phone;
      if (updateData.avatar !== undefined) updatePayload.avatar = updateData.avatar.path;

      if (userType === 'tenant') {
        if (updateData.title !== undefined) updatePayload.title = updateData.title;
        if (updateData.department !== undefined) updatePayload.department = updateData.department;
      }

      if (userType === 'system') {
        userProfile = await this.systemUserService.update(userId, updatePayload);
      } else {
        userProfile = await this.tenantUserService.update(userId, updatePayload);
      }

      this.logger.log(`Profile updated for ${userType} user: ${userId}`);
      return {
        success: true,
        user: userProfile,
      };
    } catch (error) {
      this.logger.error(`Error updating profile for ${userId}:`, error);
      throw new InternalServerErrorException('更新個人資料失敗');
    }
  }

  async acceptInvitation(dto: AcceptInvitationDto) {
    const { token, password, name } = dto;
    const invitation = await this.tenantInvitationsService.validateInvitationToken(token);

    const hashedPassword = await bcrypt.hash(password, 10);

    const user = await this.prisma.tenant_users.create({
      data: {
        id: uuidv4(),
        email: invitation.email,
        password: hashedPassword,
        name: name,
        tenant_id: invitation.tenant_id,
        status: 'ACTIVE',
        role: (invitation as any).role.name as any,
        invited_by: (invitation as any).createdBy,
      },
    });

    await this.tenantInvitationsService.markInvitationAsUsed(token, user.id);

    const { password: _password, ...result } = user;
    return result;
  }

  async stageOneRegister(dto: StageOneRegisterDto): Promise<{ success: boolean; message: string }> {
    const { email, password, name } = dto;
    // 使用email前綴或name作為預設租戶名稱
    const tenantName = dto.tenant_name || `${name || email.split('@')[0]}-tenant`;

    this.logger.debug(`Stage one registration for email: ${email} with tenant: ${tenantName}`);

    const existingTenant = await this.prisma.tenants.findUnique({
      where: { name: tenantName },
    });
    if (existingTenant) {
      throw new ConflictException('Tenant name is already taken.');
    }

    const existingSystemUser = await this.prisma.system_users.findUnique({
      where: { email },
    });
    const existingTenantUser = await this.prisma.tenant_users.findUnique({
      where: { email },
    });
    if (existingSystemUser || existingTenantUser) {
      throw new ConflictException('Email is already in use.');
    }

    const hashedPassword = await bcrypt.hash(password, 10);
    const tenantId = uuidv4();
    await this.prisma.tenants.create({
      data: {
        id: tenantId,
        name: tenantName,
        display_name: tenantName,
        tenant_users: {
          create: {
            id: uuidv4(),
            email: email,
            password: hashedPassword,
            name: name,
            role: 'TENANT_ADMIN',
            status: 'ACTIVE',
          },
        },
      },
    });

    return {
      success: true,
      message: 'Tenant and admin user created successfully.',
    };
  }

  async checkTenantUniqueness(
    dto: CheckTenantUniquenessRequestDto,
  ): Promise<{ isAvailable: boolean; message: string }> {
    const { company_name, domain } = dto;

    if (domain) {
      const existingTenantByDomain = await this.prisma.tenants.findUnique({
        where: { domain },
      });
      if (existingTenantByDomain) {
        return { isAvailable: false, message: 'Domain is already in use.' };
      }
    }

    const existingTenantByName = await this.prisma.tenants.findUnique({
      where: { name: company_name },
    });
    if (existingTenantByName) {
      return { isAvailable: false, message: 'Company name is already in use.' };
    }

    return { isAvailable: true, message: 'Tenant name and domain are available.' };
  }

  async stageTwoRegister(dto: StageTwoRegisterRequestDto): Promise<AuthResponse> {
    const { stageOneToken, companyName, companyDomain, companySize, industry } = dto;

    // Step 1: Validate the stage one token
    let systemUserId: string;
    try {
      const decodedToken = this.jwtService.decode(stageOneToken) as { sub: string };
      if (!decodedToken || !decodedToken.sub) {
        throw new BadRequestException('Invalid or expired stage one token.');
      }
      systemUserId = decodedToken.sub;
    } catch (error) {
      throw new BadRequestException('Invalid or expired stage one token.');
    }

    // Step 2: Check tenant uniqueness
    const uniquenessCheck = await this.checkTenantUniqueness({
      company_name: companyName,
      domain: companyDomain,
    });
    if (!uniquenessCheck.isAvailable) {
      throw new ConflictException(uniquenessCheck.message);
    }

    // Step 3: Find the original system_user record
    const systemUser = await this.prisma.system_users.findUnique({
      where: { id: systemUserId },
    });
    if (!systemUser) {
      throw new NotFoundException('Original user account not found.');
    }

    // Step 4: Use a transaction to create tenant, user, and roles
    return this.prisma.$transaction(async (tx) => {
      // Step 4a: Create the tenant
      const newTenant = await tx.tenants.create({
        data: {
          id: uuidv4(),
          name: companyName,
          domain: companyDomain,
          display_name: companyName,
          company_size: companySize,
          industry: industry,
          status: 'active',
        },
      });

      // Step 4b: Find the TENANT_ADMIN role for this new tenant
      // This assumes a default role exists or is created via another mechanism.
      // A more robust implementation might create the role if it doesn't exist.
      const adminRole = await tx.roles.findFirst({
        where: { name: 'TENANT_ADMIN' }, // Assuming a global TENANT_ADMIN role name
      });
      if (!adminRole) {
        throw new InternalServerErrorException('Default user role TENANT_ADMIN not found.');
      }

      // Step 4c: Create the new tenant_user based on the system_user
      const newTenantUser = await tx.tenant_users.create({
        data: {
          id: uuidv4(),
          // Reusing systemUser's data
          email: systemUser.email,
          password: systemUser.password, // Password should already be hashed
          name: systemUser.name,
          avatar: systemUser.avatar,
          tenant_id: newTenant.id,
          role: 'TENANT_ADMIN', // Assigning the enum role
          status: 'ACTIVE',
        },
      });

      // Step 4d: Create the association in tenant_user_roles
      await tx.tenant_user_roles.create({
        data: {
          id: uuidv4(),
          tenant_user_id: newTenantUser.id,
          role_id: adminRole.id,
        },
      });

      // Step 4e: Delete the old system_user
      await tx.system_users.delete({
        where: { id: systemUserId },
      });

      // Step 4f: Generate tokens for the new tenant user
      const tokens = await this.generateTokensForUser(newTenantUser, 'tenant');

      return {
        ...tokens,
        userType: 'tenant',
        user: newTenantUser,
      };
    });
  }

  async joinTenant(dto: JoinTenantRequestDto): Promise<{ message: string }> {
    const { stage_one_token, tenant_id } = dto;

    // Step 1: Validate the stage one token (mock validation)
    let systemUserId: string;
    try {
      const decodedToken = this.jwtService.decode(stage_one_token) as { sub: string };
      if (!decodedToken || !decodedToken.sub) {
        throw new BadRequestException('Invalid or expired stage one token.');
      }
      systemUserId = decodedToken.sub;
    } catch (error) {
      throw new BadRequestException('Invalid or expired stage one token.');
    }

    // Step 2: Check if the user and tenant exist
    const systemUser = await this.prisma.system_users.findUnique({ where: { id: systemUserId } });
    if (!systemUser) {
      throw new NotFoundException('User account not found.');
    }
    const tenant = await this.prisma.tenants.findUnique({ where: { id: tenant_id } });
    if (!tenant) {
      throw new NotFoundException('Target company not found.');
    }
    if (tenant.status !== 'active') {
      throw new BadRequestException('Target company is not currently active.');
    }

    // Step 3: Create an invitation record for the admin to approve
    try {
      const defaultRole = await this.prisma.roles.findFirst({
        where: { name: 'TENANT_USER', scope: 'TENANT' },
      });
      if (!defaultRole) {
        throw new InternalServerErrorException('Default user role not found.');
      }

      await this.prisma.tenant_invitations.create({
        data: {
          id: uuidv4(),
          email: systemUser.email,
          tenant_id: tenant_id,
          role_id: defaultRole.id,
          status: 'pending_approval', // A new status to indicate it's a join request
          token: uuidv4(),
          expires_at: addDays(new Date(), 30), // Request expires in 30 days
          created_by_id: systemUserId, // The user created the request themselves
        },
      });

      // TODO: Notify tenant admins about the new join request

      return {
        message: 'Your request to join the company has been submitted and is pending approval.',
      };
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        throw new ConflictException('You have already submitted a request to join this company.');
      }
      this.logger.error(
        `Failed to create join tenant request for user ${systemUserId} to tenant ${tenant_id}`,
        error.stack,
      );
      throw new InternalServerErrorException('Could not submit your request to join the company.');
    }
  }

  // =================================================================
  // ==                      OAuth 相關流程                         ==
  // =================================================================

  async createTenantUserFromOAuth(oauthUserInfo: OAuthUserInfo): Promise<ITenantUser> {
    this.logger.debug(`Creating new tenant user from OAuth: ${oauthUserInfo.email}`);

    const randomPassword = await bcrypt.hash(uuidv4(), 10);

    try {
      const defaultTenant = await this.prisma.tenants.findFirst({
        where: { is_default: true },
        orderBy: { created_at: 'asc' },
      });

      if (!defaultTenant) {
        this.logger.error('No default tenant found for OAuth user creation.');
        throw new InternalServerErrorException(
          'Cannot create user account due to system configuration issue.',
        );
      }

      const newUser = await this.prisma.tenant_users.create({
        data: {
          id: uuidv4(),
          email: oauthUserInfo.email,
          password: randomPassword,
          name: oauthUserInfo.name || 'OAuth User',
          role: 'TENANT_USER',
          status: 'ACTIVE',
          tenant_id: defaultTenant.id,
          oauth_accounts: {
            create: {
              provider: oauthUserInfo.provider,
              provider_id: oauthUserInfo.providerId,
              user_type: 'tenant',
              last_login: new Date(),
            },
          },
        },
      });

      this.logger.log(`Successfully created new tenant user ${newUser.id} from OAuth.`);
      return newUser;
    } catch (error) {
      this.logger.error(`Error creating tenant user from OAuth: ${error.message}`, error.stack);
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2002') {
        throw new ConflictException('An account with this email already exists.');
      }
      throw new InternalServerErrorException('Could not create user account.');
    }
  }

  async findOrCreateUserFromOAuth(
    oauthUserInfo: OAuthUserInfo,
  ): Promise<{ user: ISystemUser | ITenantUser; userType: 'system' | 'tenant' }> {
    this.logger.debug(`Finding or creating user from OAuth for email: ${oauthUserInfo.email}`);

    const oauthAccount = await this.findUserByProviderId(
      oauthUserInfo.provider,
      oauthUserInfo.providerId,
    );
    if (oauthAccount) {
      this.logger.debug(`Found existing OAuth account for user ${oauthAccount.user.id}`);
      return oauthAccount;
    }

    const tenantUserByEmail = await this.prisma.tenant_users.findUnique({
      where: { email: oauthUserInfo.email },
    });
    if (tenantUserByEmail) {
      this.logger.debug(`Found existing tenant user by email, linking OAuth account.`);
      await this.prisma.oauth_accounts.create({
        data: {
          provider: oauthUserInfo.provider,
          provider_id: oauthUserInfo.providerId,
          user_type: 'tenant',
          tenant_user_id: tenantUserByEmail.id,
          last_login: new Date(),
        },
      });
      return { user: tenantUserByEmail, userType: 'tenant' };
    }

    const systemUserByEmail = await this.prisma.system_users.findUnique({
      where: { email: oauthUserInfo.email },
    });
    if (systemUserByEmail) {
      this.logger.debug(`Found existing system user by email, linking OAuth account.`);
      await this.prisma.oauth_accounts.create({
        data: {
          provider: oauthUserInfo.provider,
          provider_id: oauthUserInfo.providerId,
          user_type: 'system',
          system_user_id: systemUserByEmail.id,
          last_login: new Date(),
        },
      });
      return { user: systemUserByEmail, userType: 'system' };
    }

    const newTenantUser = await this.createTenantUserFromOAuth(oauthUserInfo);
    return { user: newTenantUser, userType: 'tenant' };
  }

  private async findUserByProviderId(
    provider: string,
    providerId: string,
  ): Promise<{ user: ISystemUser | ITenantUser; userType: 'system' | 'tenant' } | null> {
    const oauthAccount = await this.prisma.oauth_accounts.findUnique({
      where: {
        provider_provider_id: {
          provider,
          provider_id: providerId,
        },
      },
      include: {
        system_user: true,
        tenant_user: true,
      },
    });

    if (!oauthAccount) {
      return null;
    }

    if (oauthAccount.user_type === 'system' && oauthAccount.system_user) {
      return { user: oauthAccount.system_user as ISystemUser, userType: 'system' };
    }

    if (oauthAccount.user_type === 'tenant' && oauthAccount.tenant_user) {
      return { user: oauthAccount.tenant_user as ITenantUser, userType: 'tenant' };
    }

    this.logger.warn(`Orphaned oauth_account record found: ${oauthAccount.id}`);
    return null;
  }
}
