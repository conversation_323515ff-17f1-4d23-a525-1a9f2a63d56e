import {
  Controller,
  Post,
  Body,
  Get,
  Put,
  UseGuards,
  Req,
  UseInterceptors,
  UploadedFile,
  HttpCode,
  HttpStatus,
  Query,
  UnauthorizedException,
  Res,
  BadRequestException,
  Logger,
  Patch,
  Param,
} from '@nestjs/common';
import { AuthService, OAuthUserInfo, AuthResponse } from './auth.service';
import {
  LoginDto,
  StageOneRegisterDto,
  StageTwoRegisterRequestDto,
  JoinTenantRequestDto,
  AcceptInvitationDto,
  CheckTenantUniquenessRequestDto,
  ForgotPasswordDto,
  ResetPasswordDto,
  ChangePasswordRequestDto,
  ImpersonateTenantDto,
  LoginResponseDto,
} from './dto';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiConsumes } from '@nestjs/swagger';
import { Public } from '../../../common/decorators/public.decorator';
import { JwtAuthGuard } from './guards/auth.guard';
import { CurrentUser } from './decorators/current-user.decorator';
import { Request, Response } from 'express';
import { FileInterceptor } from '@nestjs/platform-express';
import { diskStorage } from 'multer';
import { JwtUser } from '../../../types/jwt-user.type';
import { ConfigService } from '@nestjs/config';
import { MfaService } from './mfa.service';
import { SystemLogService } from '@/common/services/system-log.service';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { AuthGuard } from '@nestjs/passport';
import { ISystemUser, ITenantUser } from '../../../types/models/user.model';
import { JwtService } from '@nestjs/jwt';

interface UpdateProfileDto {
  name?: string;
  phone?: string;
  title?: string;
  department?: string;
  avatar?: Express.Multer.File;
}

@ApiTags('core/auth')
@Controller('auth')
export class AuthController {
  private readonly logger = new Logger(AuthController.name);

  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly mfaService: MfaService,
    private readonly systemLogService: SystemLogService,
  ) {}

  private getCookieOptions(maxAge: number) {
    return {
      httpOnly: true,
      secure: this.configService.get('NODE_ENV') === 'production',
      sameSite: 'strict' as const,
      maxAge: maxAge,
    };
  }

  @Public()
  @Post('login')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '統一登入(系統或租戶)' })
  @ApiResponse({
    status: 200,
    description: '登入成功',
    type: LoginResponseDto,
  })
  async login(
    @Body() loginDto: LoginDto,
    @Res({ passthrough: true }) res: Response,
    @Req() req: Request,
  ) {
    const deviceInfo = req.headers['user-agent'] || 'Unknown Device';
    return this.authService.login(loginDto, res, deviceInfo);
  }

  @Get('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '獲取當前使用者資訊' })
  async getProfile(@CurrentUser() user: JwtUser, @Req() req: Request) {
    // 從請求中獲取 ability 規則
    const ability = (req as any).ability;
    const abilityRules = ability ? ability.rules : [];

    return {
      user: user,
      ability_rules: abilityRules,
    };
  }

  @Patch('me')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @ApiOperation({ summary: '更新當前使用者資訊' })
  async updateProfile(@CurrentUser() user: JwtUser, @Body() updateData: any) {
    return this.authService.updateProfile(user.id, user.user_type, updateData);
  }

  @Post('logout')
  @UseGuards(JwtAuthGuard)
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '使用者登出' })
  @ApiBearerAuth()
  async logout(
    @CurrentUser() user: JwtUser,
    @Res({ passthrough: true }) response: Response,
  ): Promise<{ message: string }> {
    await this.authService.logout(user.id);
    response.clearCookie('auth_token');
    response.clearCookie('refresh_token');
    return { message: 'Successfully logged out' };
  }

  @Public()
  @Post('refresh-token')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '刷新 Access Token' })
  async refreshToken(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    const oldRefreshToken = req.cookies?.refresh_token;
    if (!oldRefreshToken) {
      throw new UnauthorizedException('Refresh token not found.');
    }

    const tokens = await this.authService.refreshTokens(oldRefreshToken);

    response.cookie(
      'auth_token',
      tokens.accessToken,
      this.getCookieOptions(1 * 24 * 60 * 60 * 1000),
    );
    response.cookie(
      'refresh_token',
      tokens.refreshToken,
      this.getCookieOptions(7 * 24 * 60 * 60 * 1000),
    );

    return { message: 'Tokens refreshed successfully.' };
  }

  @Post('forgot-password')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '忘記密碼' })
  async forgotPassword(@Body() dto: ForgotPasswordDto): Promise<{ message: string }> {
    await this.authService.forgotPassword(dto);
    return {
      message: 'If an account with that email exists, a password reset link has been sent.',
    };
  }

  @Post('reset-password')
  @Public()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '重設密碼' })
  async resetPassword(@Body() dto: ResetPasswordDto): Promise<{ message: string }> {
    await this.authService.resetPassword(dto);
    return { message: 'Password has been reset successfully.' };
  }

  @Post('change-password')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '變更密碼' })
  async changePassword(
    @CurrentUser() user: JwtUser,
    @Body() dto: ChangePasswordRequestDto,
  ): Promise<{ message: string }> {
    return this.authService.changePassword(user.id, user.user_type, dto);
  }

  // --- Registration Flow ---
  @Public()
  @Post('register/stage-one')
  async stageOneRegister(@Body() registerDto: StageOneRegisterDto) {
    return this.authService.stageOneRegister(registerDto);
  }

  @Public()
  @Post('register/check-uniqueness')
  async checkTenantUniqueness(@Body() dto: CheckTenantUniquenessRequestDto) {
    return this.authService.checkTenantUniqueness(dto);
  }

  @Public()
  @Post('register/stage-two')
  async stageTwoRegister(
    @Body() dto: StageTwoRegisterRequestDto,
    @Res({ passthrough: true }) response: Response,
  ) {
    const loginResult = await this.authService.stageTwoRegister(dto);
    response.cookie(
      'auth_token',
      loginResult.accessToken,
      this.getCookieOptions(1 * 24 * 60 * 60 * 1000),
    );
    response.cookie(
      'refresh_token',
      loginResult.refreshToken,
      this.getCookieOptions(7 * 24 * 60 * 60 * 1000),
    );
    return loginResult;
  }

  @Post('join-tenant')
  @UseGuards(JwtAuthGuard)
  @ApiBearerAuth()
  @HttpCode(HttpStatus.OK)
  @ApiOperation({ summary: '使用者請求加入租戶' })
  async joinTenant(@Body() dto: JoinTenantRequestDto) {
    return this.authService.joinTenant(dto);
  }

  @Post('accept-invitation')
  @Public()
  @Post('invitations/accept')
  async acceptInvitation(@Body() acceptInvitationDto: AcceptInvitationDto) {
    return this.authService.acceptInvitation(acceptInvitationDto);
  }

  @Public()
  @Get('google/callback')
  @UseGuards(AuthGuard('google'))
  async googleAuthRedirect(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    if (!req.user) {
      this.logger.error('Google OAuth callback received no user from strategy.');
      const frontendUrl = this.configService.get<string>('FRONTEND_URL') || '/';
      return response.redirect(`${frontendUrl}/login?error=google_oauth_failed`);
    }

    const { user, userType } = req.user as {
      user: ISystemUser | ITenantUser;
      userType: 'system' | 'tenant';
    };

    const deviceInfo = req.headers['user-agent'] || 'Google OAuth';
    const tokens = await this.authService.generateTokensForUser(user as any, userType, deviceInfo);

    response.cookie(
      'auth_token',
      tokens.accessToken,
      this.getCookieOptions(1 * 24 * 60 * 60 * 1000),
    );
    response.cookie(
      'refresh_token',
      tokens.refreshToken,
      this.getCookieOptions(7 * 24 * 60 * 60 * 1000),
    );

    const frontendSuccessRedirect =
      this.configService.get<string>('FRONTEND_GOOGLE_SUCCESS_REDIRECT_URL') || '/';
    response.redirect(frontendSuccessRedirect);
  }

  @Public()
  @Get('line')
  @UseGuards(AuthGuard('line'))
  async lineAuth() {
    // Guard will redirect
  }

  @Public()
  @Get('line/callback')
  @UseGuards(AuthGuard('line'))
  async lineAuthRedirect(@Req() req: Request, @Res({ passthrough: true }) response: Response) {
    if (!req.user) {
      this.logger.error('LINE OAuth callback received no user from strategy.');
      const frontendUrl = this.configService.get<string>('FRONTEND_URL') || '/';
      return response.redirect(`${frontendUrl}/login?error=line_oauth_failed`);
    }

    const { user, userType } = req.user as {
      user: ISystemUser | ITenantUser;
      userType: 'system' | 'tenant';
    };

    const deviceInfo = req.headers['user-agent'] || 'LINE OAuth';
    const tokens = await this.authService.generateTokensForUser(user as any, userType, deviceInfo);

    response.cookie(
      'auth_token',
      tokens.accessToken,
      this.getCookieOptions(1 * 24 * 60 * 60 * 1000),
    );
    response.cookie(
      'refresh_token',
      tokens.refreshToken,
      this.getCookieOptions(7 * 24 * 60 * 60 * 1000),
    );

    const frontendSuccessRedirect =
      this.configService.get<string>('FRONTEND_LINE_SUCCESS_REDIRECT_URL') || '/';
    response.redirect(frontendSuccessRedirect);
  }
}
