import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '../prisma/prisma.module';
import { SystemUserService } from '../system-user-management/system-user.service';
import { TenantUserService } from '../tenant-user-management/tenant-user.service';
import { TenantInvitationsService } from '../tenant-invitations/tenant-invitations.service';
import { MailModule } from '../mail/mail.module';
import { ConfigModule } from '@nestjs/config';

@Module({
  imports: [PrismaModule, MailModule, ConfigModule],
  providers: [SystemUserService, TenantUserService, TenantInvitationsService],
  exports: [SystemUserService, TenantUserService, TenantInvitationsService],
})
export class UserManagementModule {}
