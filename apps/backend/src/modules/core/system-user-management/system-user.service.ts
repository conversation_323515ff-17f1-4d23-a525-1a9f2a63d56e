import { Injectable, ConflictException, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import * as bcrypt from 'bcryptjs';
import { SystemUserRole } from '@prisma/client';
import {
  ISystemUser,
  ICreateSystemUser,
  IUpdateSystemUser,
  ISystemUserProfile,
} from '../../../types/models/system-user.model';
import { Prisma } from '@prisma/client';

@Injectable()
export class SystemUserService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 建立系統使用者
   */
  async createSystemUser(data: ICreateSystemUser): Promise<ISystemUserProfile> {
    // 檢查 email 是否已存在
    const existingUser = await this.findByEmail(data.email);
    if (existingUser) {
      throw new ConflictException('Email already exists');
    }

    // 加密密碼
    const hashedPassword = await bcrypt.hash(data.password, 12);

    const systemUser = await this.prisma.system_users.create({
      data: {
        email: data.email,
        password: hashedPassword,
        name: data.name,
        role: data.role || SystemUserRole.SYSTEM_ADMIN,
        status: 'active',
        avatar: data.avatar,
        phone: data.phone,
      },
    });

    return this.toProfile(systemUser);
  }

  /**
   * 根據 ID 查找系統使用者
   */
  async findById(id: string): Promise<ISystemUser | null> {
    const user = await this.prisma.system_users.findUnique({
      where: { id },
    });

    if (!user) return null;

    return user as any; // 直接返回原始用戶對象
  }

  /**
   * 根據 email 查找系統使用者
   */
  async findByEmail(email: string): Promise<ISystemUser | null> {
    const user = await this.prisma.system_users.findUnique({
      where: { email },
    });

    if (!user) return null;

    return user as any; // 直接返回原始用戶對象
  }

  /**
   * 獲取系統使用者資料（不含密碼）
   */
  async getProfile(id: string): Promise<ISystemUserProfile> {
    const user = await this.findById(id);
    if (!user) {
      throw new NotFoundException('System user not found');
    }
    return this.toProfile(user);
  }

  /**
   * 更新系統使用者
   */
  async updateSystemUser(id: string, data: IUpdateSystemUser): Promise<ISystemUserProfile> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException('System user not found');
    }

    // 如果更新 email，檢查是否與其他使用者衝突
    if (data.email && data.email !== existingUser.email) {
      const emailExists = await this.findByEmail(data.email);
      if (emailExists) {
        throw new ConflictException('Email already exists');
      }
    }

    const updatedUser = await this.prisma.system_users.update({
      where: { id },
      data,
    });

    return this.toProfile(updatedUser);
  }

  /**
   * 刪除系統使用者
   */
  async deleteSystemUser(id: string): Promise<void> {
    const existingUser = await this.findById(id);
    if (!existingUser) {
      throw new NotFoundException('System user not found');
    }

    await this.prisma.system_users.delete({
      where: { id },
    });
  }

  /**
   * 獲取所有系統使用者列表
   */
  async getAllSystemUsers(): Promise<ISystemUserProfile[]> {
    const users = await this.prisma.system_users.findMany({
      orderBy: { created_at: 'desc' },
    });

    return users.map((user) => this.toProfile(user));
  }

  /**
   * 驗證密碼
   */
  async validatePassword(user: ISystemUser, password: string): Promise<boolean> {
    return bcrypt.compare(password, user.password);
  }

  /**
   * 更新密碼
   */
  async updatePassword(id: string, newPassword: string): Promise<void> {
    const hashedPassword = await bcrypt.hash(newPassword, 12);

    await this.prisma.system_users.update({
      where: { id },
      data: {
        password: hashedPassword,
        password_last_changed_at: new Date(),
      },
    });
  }

  /**
   * 更新最後登入時間
   */
  async updateLastLogin(id: string, ip?: string): Promise<void> {
    await this.prisma.system_users.update({
      where: { id },
      data: {
        last_login_at: new Date(),
        last_login_ip: ip,
      },
    });
  }

  /**
   * 更新最後登出時間
   */
  async updateLastLogout(id: string): Promise<void> {
    await this.prisma.system_users.update({
      where: { id },
      data: {
        last_logout_at: new Date(),
      },
    });
  }

  /**
   * 轉換為 Profile 格式（移除敏感資訊）
   */
  private toProfile(user: any): ISystemUserProfile {
    // 將 Prisma 對象轉換為符合 ISystemUser 介面的對象
    const systemUser: ISystemUser = {
      id: user.id,
      role: user.role,
      email: user.email,
      password: user.password,
      name: user.name,
      status: user.status,
      avatar: user.avatar,
      phone: user.phone,
      mfa_enabled: user.mfa_enabled,
      mfa_secret: user.mfa_secret,
      last_login_at: user.last_login_at,
      last_login_ip: user.last_login_ip,
      last_logout_at: user.last_logout_at,
      password_last_changed_at: user.password_last_changed_at,
      created_at: user.created_at,
      updated_at: user.updated_at,
    };

    const { password, mfa_secret, ...profile } = systemUser;
    return profile;
  }
}
