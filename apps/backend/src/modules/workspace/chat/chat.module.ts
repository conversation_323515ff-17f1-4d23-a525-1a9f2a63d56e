import { Modu<PERSON> } from '@nestjs/common';
import { JwtModule } from '@nestjs/jwt';
import { ChatController } from './controllers/chat.controller';
import { ConversationController } from './controllers/conversation.controller';
import { MessageController } from './controllers/message.controller';
import { ChatService } from './services/chat.service';
import { ConversationService } from './services/conversation.service';
import { MessageService } from './services/message.service';
import { ChatGateway } from './gateways/chat.gateway';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { AuthModule } from '../../core/auth/auth.module';
import { WebSocketModule } from '../websocket/websocket.module';
import { WorkspacesModule } from '../../admin/workspaces/workspaces.module';

@Module({
  imports: [
    JwtModule.register({
      secret: process.env.JWT_SECRET || 'your-secret-key',
      signOptions: { expiresIn: '24h' },
    }),
    WorkspacesModule,
    PrismaModule,
    AuthModule,
    WebSocketModule,
  ],
  controllers: [ChatController, ConversationController, MessageController],
  providers: [ChatService, ConversationService, MessageService, ChatGateway],
  exports: [ChatService, ConversationService, MessageService],
})
export class ChatModule {}
