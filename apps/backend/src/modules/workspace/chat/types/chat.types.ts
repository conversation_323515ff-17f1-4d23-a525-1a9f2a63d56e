import { Prisma } from '@prisma/client';

// 包含回覆訊息的訊息類型
export type MessageWithReplies = Prisma.messagesGetPayload<{
  include: {
    reply_to_message: true;
    replies: true;
    attachments: true;
    reactions: true;
  };
}>;

// 包含訊息的對話類型
export type ConversationWithMessages = Prisma.conversationsGetPayload<{
  include: {
    last_message: true;
    participants: true;
    messages: {
      include: {
        reply_to_message: true;
        attachments: true;
        reactions: true;
      };
    };
  };
}>;

// 創建訊息的 DTO
export interface CreateMessageDto {
  content: string;
  conversation_id: string;
  sender_id: string;
  reply_to_message_id?: string;
  attachments?: Array<{
    name: string;
    url: string;
    type: string;
    size: number;
  }>;
}

// 創建對話的 DTO
export interface CreateConversationDto {
  type: 'DIRECT' | 'GROUP' | 'ANNOUNCEMENT' | 'SYSTEM';
  name?: string;
  description?: string;
  participant_ids: string[];
  avatar_url?: string;
  is_private?: boolean;
}

// 更新對話的 DTO
export interface UpdateConversationDto {
  name?: string;
  description?: string;
}

// 更新訊息的 DTO
export interface UpdateMessageDto {
  content?: string;
} 