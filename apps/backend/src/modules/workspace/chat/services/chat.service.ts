import { Injectable } from '@nestjs/common';
import { ConversationService } from './conversation.service';
import { MessageService } from './message.service';
import { RealtimeEventsService } from '../../websocket/realtime-events.service';
import { CreateConversationDto } from '../dto/create-conversation.dto';
import { UpdateConversationDto } from '../dto/update-conversation.dto';
import { SendMessageDto } from '../dto/send-message.dto';

@Injectable()
export class ChatService {
  constructor(
    private conversationService: ConversationService,
    private messageService: MessageService,
    private realtimeEventsService: RealtimeEventsService,
  ) {}

  // 對話相關方法
  async createConversation(workspaceId: string, userId: string, createDto: CreateConversationDto) {
    const conversation = await this.conversationService.createConversation(
      workspaceId,
      userId,
      createDto,
    );

    // 通知相關用戶新對話創建
    await this.realtimeEventsService.notifyConversationUpdate(
      conversation.id,
      { type: 'created', conversation },
      userId,
    );

    return conversation;
  }

  async getConversation(conversationId: string, userId: string) {
    return this.conversationService.findConversationById(conversationId, userId);
  }

  async getUserConversations(workspaceId: string, userId: string, page?: number, limit?: number) {
    return this.conversationService.findUserConversations(workspaceId, userId, page, limit);
  }

  async updateConversation(
    conversationId: string,
    userId: string,
    updateDto: UpdateConversationDto,
  ) {
    const conversation = await this.conversationService.updateConversation(
      conversationId,
      userId,
      updateDto,
    );

    // 通知對話更新
    await this.realtimeEventsService.notifyConversationUpdate(
      conversationId,
      { type: 'updated', changes: updateDto },
      userId,
    );

    return conversation;
  }

  async deleteConversation(conversationId: string, userId: string) {
    const result = await this.conversationService.deleteConversation(conversationId, userId);

    // 通知對話刪除
    await this.realtimeEventsService.notifyConversationUpdate(
      conversationId,
      { type: 'deleted' },
      userId,
    );

    return result;
  }

  async addParticipant(conversationId: string, userId: string, targetUserId: string) {
    const result = await this.conversationService.addParticipant(conversationId, targetUserId);

    // 通知參與者變更
    await this.realtimeEventsService.notifyConversationUpdate(
      conversationId,
      { type: 'participant_added', targetUserId },
      userId,
    );

    return result;
  }

  async removeParticipant(conversationId: string, userId: string, targetUserId: string) {
    const result = await this.conversationService.removeParticipant(
      conversationId,
      userId,
      targetUserId,
    );

    // 通知參與者變更
    await this.realtimeEventsService.notifyConversationUpdate(
      conversationId,
      { type: 'participant_removed', targetUserId },
      userId,
    );

    return result;
  }

  async markConversationAsRead(conversationId: string, userId: string, messageId?: string) {
    return this.conversationService.markAsRead(conversationId, userId, messageId);
  }

  // 訊息相關方法
  async sendMessage(conversationId: string, senderId: string, sendMessageDto: SendMessageDto) {
    const message = await this.messageService.sendMessage(conversationId, senderId, sendMessageDto);

    // 發送即時通知
    await this.realtimeEventsService.notifyNewChatMessage(conversationId, message, senderId);

    return message;
  }

  async getConversationMessages(
    conversationId: string,
    userId: string,
    page?: number,
    limit?: number,
    beforeMessageId?: string,
  ) {
    return this.messageService.getMessages(conversationId, userId, page, limit);
  }

  async getMessage(messageId: string) {
    // 由於我們移除了 findMessageById 方法，這裡需要重新實現
    // 或者移除這個方法，因為通常不需要單獨獲取訊息
    throw new Error('Method not implemented');
  }

  async updateMessage(messageId: string, userId: string, content: string) {
    const message = await this.messageService.updateMessage(messageId, userId, content);

    // 通知訊息更新
    if (message) {
      await this.realtimeEventsService.notifyNewChatMessage(
        message.conversation_id,
        { ...message, type: 'updated' },
        userId,
      );
    }

    return message;
  }

  async deleteMessage(messageId: string, userId: string) {
    const result = await this.messageService.deleteMessage(messageId, userId);

    // 這裡可以添加訊息刪除的即時通知
    // await this.realtimeEventsService.notifyMessageDeleted(messageId, userId);

    return result;
  }

  async addReaction(messageId: string, userId: string, emoji: string) {
    const reaction = await this.messageService.addReaction(messageId, userId, emoji);

    // 通知訊息反應
    if (reaction) {
      // 需要獲取訊息的對話ID
      // 這裡可能需要修改messageService.addReaction返回更多信息
      // await this.realtimeEventsService.notifyMessageReaction(conversationId, messageId, reaction, userId);
    }

    return reaction;
  }

  async removeReaction(messageId: string, userId: string, emoji: string) {
    // addReaction 方法已經處理了移除邏輯（如果已存在則移除）
    return this.messageService.addReaction(messageId, userId, emoji);
  }

  async markMessagesAsRead(conversationId: string, userId: string, messageIds: string[]) {
    // 使用 ConversationService 的 markAsRead 方法
    const lastMessageId = messageIds.length > 0 ? messageIds[messageIds.length - 1] : undefined;
    return this.conversationService.markAsRead(conversationId, userId, lastMessageId);
  }

  async searchMessages(
    conversationId: string,
    userId: string,
    query: string,
    page?: number,
    limit?: number,
  ) {
    return this.messageService.searchMessages(conversationId, userId, query, page, limit);
  }

  async getMessageStatistics(conversationId: string) {
    // 簡化的統計實現
    return {
      totalMessages: 0,
      messagesByType: {},
      messagesByUser: {},
    };
  }

  // 輸入狀態相關方法
  async startTyping(conversationId: string, userId: string) {
    await this.realtimeEventsService.notifyUserTyping(conversationId, userId, true);
  }

  async stopTyping(conversationId: string, userId: string) {
    await this.realtimeEventsService.notifyUserTyping(conversationId, userId, false);
  }

  // 綜合方法
  async getChatOverview(workspaceId: string, userId: string) {
    const { conversations, total } = await this.getUserConversations(workspaceId, userId, 1, 10);

    // 計算未讀訊息總數
    const totalUnreadCount = 0;
    for (const conversation of conversations) {
      // 這裡可以添加計算每個對話未讀數的邏輯
      // totalUnreadCount += conversation.unreadCount || 0;
    }

    return {
      recentConversations: conversations,
      totalConversations: total,
      totalUnreadCount,
    };
  }

  async getWorkspaceChatStatistics(workspaceId: string) {
    // 這裡可以添加工作區聊天統計的邏輯
    // 例如：總對話數、總訊息數、活躍用戶數等
    return {
      totalConversations: 0,
      totalMessages: 0,
      activeUsers: 0,
      messagesByDay: [],
    };
  }
}
