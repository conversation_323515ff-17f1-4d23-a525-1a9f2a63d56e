import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { ConversationWithMessages } from '../types/chat.types';
import { CreateConversationDto } from '../dto/create-conversation.dto';
import { UpdateConversationDto } from '../dto/update-conversation.dto';

@Injectable()
export class ConversationService {
  constructor(private readonly prisma: PrismaService) {}

  async createConversation(workspaceId: string, userId: string, createDto: CreateConversationDto) {
    const { type, name, description, participant_ids, avatar_url, is_private } = createDto;

    // 驗證參與者數量
    if (type === 'DIRECT' && participant_ids.length !== 1) {
      throw new BadRequestException('直接對話必須包含一個參與者');
    }

    if (type === 'GROUP' && participant_ids.length < 2) {
      throw new BadRequestException('群組對話至少需要兩個參與者');
    }

    // 檢查是否已存在直接對話
    if (type === 'DIRECT') {
      const existingConversation = await this.findDirectConversation(
        workspaceId,
        userId,
        participant_ids[0],
      );
      if (existingConversation) {
        return existingConversation;
      }
    }

    // 創建對話
    const conversation = await this.prisma.conversations.create({
      data: {
        type,
        name,
        description,
        avatar: avatar_url,
        is_private: is_private || false,
        workspace_id: workspaceId,
        created_by: userId,
        last_activity_at: new Date(),
        participants: {
          create: [
            // 添加創建者為擁有者
            {
              user_id: userId,
              role: 'OWNER',
              status: 'ACTIVE',
            },
            // 添加其他參與者
            ...participant_ids
              .filter((id) => id !== userId)
              .map((participantId) => ({
                user_id: participantId,
                role: 'MEMBER' as const,
                status: 'ACTIVE' as const,
              })),
          ],
        },
      },
      include: {
        participants: true,
        last_message: true,
      },
    });

    return conversation;
  }

  async findConversationById(conversationId: string, userId: string) {
    const conversation = await this.prisma.conversations.findFirst({
      where: {
        id: conversationId,
        participants: {
          some: {
            user_id: userId,
            status: 'ACTIVE',
          },
        },
      },
      include: {
        participants: true,
        last_message: true,
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    if (!conversation) {
      throw new NotFoundException('對話不存在或您沒有權限訪問');
    }

    return conversation;
  }

  async findUserConversations(
    workspaceId: string,
    userId: string,
    page: number = 1,
    limit: number = 20,
  ) {
    const skip = (page - 1) * limit;

    const [conversations, total] = await Promise.all([
      this.prisma.conversations.findMany({
        where: {
          workspace_id: workspaceId,
          participants: {
            some: {
              user_id: userId,
              status: 'ACTIVE',
            },
          },
          is_archived: false,
        },
        include: {
          participants: true,
          last_message: true,
          _count: {
            select: {
              messages: true,
            },
          },
        },
        orderBy: {
          last_activity_at: 'desc',
        },
        skip,
        take: limit,
      }),
      this.prisma.conversations.count({
        where: {
          workspace_id: workspaceId,
          participants: {
            some: {
              user_id: userId,
              status: 'ACTIVE',
            },
          },
          is_archived: false,
        },
      }),
    ]);

    return {
      conversations,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async updateConversation(
    conversationId: string,
    userId: string,
    updateDto: UpdateConversationDto,
  ) {
    // 檢查權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        role: {
          in: ['OWNER', 'ADMIN'],
        },
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限修改此對話');
    }

    const conversation = await this.prisma.conversations.update({
      where: { id: conversationId },
      data: updateDto,
      include: {
        participants: true,
        last_message: true,
        _count: {
          select: {
            messages: true,
          },
        },
      },
    });

    return conversation;
  }

  async deleteConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        role: 'OWNER',
      },
    });

    if (!participant) {
      throw new ForbiddenException('只有對話擁有者可以刪除對話');
    }

    await this.prisma.conversations.delete({
      where: { id: conversationId },
    });
  }

  async addParticipant(conversationId: string, userId: string, role: string = 'MEMBER') {
    const participant = await this.prisma.conversation_participants.create({
      data: {
        conversation_id: conversationId,
        user_id: userId,
        role: role as any,
        status: 'ACTIVE',
      },
    });

    return participant;
  }

  async removeParticipant(
    conversationId: string,
    userId: string,
    targetUserId: string,
  ): Promise<void> {
    // 檢查權限
    const requesterParticipant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        role: {
          in: ['OWNER', 'ADMIN'],
        },
      },
    });

    if (!requesterParticipant) {
      throw new ForbiddenException('您沒有權限移除參與者');
    }

    const targetParticipant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: targetUserId,
      },
    });

    if (!targetParticipant) {
      throw new NotFoundException('目標用戶不在此對話中');
    }

    // 更新狀態為已離開
    await this.prisma.conversation_participants.update({
      where: { id: targetParticipant.id },
      data: {
        status: 'LEFT',
        left_at: new Date(),
      },
    });
  }

  async markAsRead(conversationId: string, userId: string, messageId?: string): Promise<void> {
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
      },
    });

    if (!participant) {
      throw new NotFoundException('您不是此對話的參與者');
    }

    await this.prisma.conversation_participants.update({
      where: { id: participant.id },
      data: {
        last_read_message_id: messageId,
        last_read_at: new Date(),
      },
    });
  }

  async updateLastActivity(conversationId: string): Promise<void> {
    await this.prisma.conversations.update({
      where: { id: conversationId },
      data: { last_activity_at: new Date() },
    });
  }

  async pinConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶是否為對話參與者
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您不是此對話的參與者');
    }

    // 更新參與者設定
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        is_pinned: true,
      },
    });
  }

  async unpinConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶是否為對話參與者
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您不是此對話的參與者');
    }

    // 更新參與者設定
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        is_pinned: false,
      },
    });
  }

  async muteConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶是否為對話參與者
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您不是此對話的參與者');
    }

    // 更新參與者設定
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        is_muted: true,
      },
    });
  }

  async unmuteConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶是否為對話參與者
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您不是此對話的參與者');
    }

    // 更新參與者設定
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        is_muted: false,
      },
    });
  }

  async archiveConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
        status: 'ACTIVE',
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限歸檔此對話');
    }

    // 更新參與者狀態為非活躍（代表歸檔）
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        status: 'INACTIVE',
      },
    });
  }

  async unarchiveConversation(conversationId: string, userId: string): Promise<void> {
    // 檢查用戶權限
    const participant = await this.prisma.conversation_participants.findFirst({
      where: {
        conversation_id: conversationId,
        user_id: userId,
      },
    });

    if (!participant) {
      throw new ForbiddenException('您沒有權限取消歸檔此對話');
    }

    // 更新參與者狀態為活躍
    await this.prisma.conversation_participants.update({
      where: {
        conversation_id_user_id: {
          conversation_id: conversationId,
          user_id: userId,
        },
      },
      data: {
        status: 'ACTIVE',
      },
    });
  }

  private async findDirectConversation(
    workspaceId: string,
    firstUserId: string,
    secondUserId: string,
  ) {
    const conversation = await this.prisma.conversations.findFirst({
      where: {
        workspace_id: workspaceId,
        type: 'DIRECT',
        participants: {
          every: {
            OR: [
              { user_id: firstUserId, status: 'ACTIVE' },
              { user_id: secondUserId, status: 'ACTIVE' },
            ],
          },
        },
      },
      include: {
        participants: true,
        last_message: true,
      },
    });

    return conversation;
  }

  async findByWorkspace(workspaceId: string): Promise<ConversationWithMessages[]> {
    return this.prisma.conversations.findMany({
      where: { workspace_id: workspaceId },
      include: {
        messages: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
          orderBy: { created_at: 'asc' },
        },
        last_message: true,
        participants: true,
      },
      orderBy: { updated_at: 'desc' },
    });
  }

  async findById(id: string): Promise<ConversationWithMessages | null> {
    return this.prisma.conversations.findUnique({
      where: { id },
      include: {
        messages: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
          orderBy: { created_at: 'asc' },
        },
        last_message: true,
        participants: true,
      },
    });
  }

  async updateLastMessage(conversationId: string, messageId: string): Promise<void> {
    await this.prisma.conversations.update({
      where: { id: conversationId },
      data: { last_message_id: messageId },
    });
  }

  async create(
    workspaceId: string, 
    userId: string, 
    data: CreateConversationDto
  ): Promise<ConversationWithMessages> {
    const conversation = await this.prisma.conversations.create({
      data: {
        type: data.type,
        name: data.name,
        description: data.description,
        avatar: data.avatar_url,
        is_private: data.is_private || false,
        workspace_id: workspaceId,
        created_by: userId,
        participants: {
          create: [
            // 添加創建者為擁有者
            {
              user_id: userId,
              role: 'OWNER',
              status: 'ACTIVE',
            },
            // 添加其他參與者
            ...data.participant_ids
              .filter((id) => id !== userId)
              .map((participantId) => ({
                user_id: participantId,
                role: 'MEMBER' as const,
                status: 'ACTIVE' as const,
              })),
          ],
        },
      },
      include: {
        messages: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
        },
        last_message: true,
        participants: true,
      },
    });

    return conversation;
  }

  async update(id: string, data: UpdateConversationDto): Promise<ConversationWithMessages> {
    return this.prisma.conversations.update({
      where: { id },
      data,
      include: {
        messages: {
          include: {
            reply_to_message: true,
            attachments: true,
            reactions: true,
          },
          orderBy: { created_at: 'asc' },
        },
        last_message: true,
        participants: true,
      },
    });
  }
}
