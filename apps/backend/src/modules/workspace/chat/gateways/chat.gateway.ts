import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ChatService } from '../services/chat.service';
import { SendMessageDto } from '../dto/send-message.dto';

interface AuthenticatedSocket extends Socket {
  userId?: string;
  workspaceId?: string;
}

@WebSocketGateway({
  namespace: '/chat',
  cors: {
    origin: process.env.FRONTEND_URL || 'http://localhost:3000',
    credentials: true,
  },
})
export class ChatGateway implements OnGatewayConnection, OnGatewayDisconnect {
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(ChatGateway.name);
  private connectedUsers = new Map<string, Set<string>>(); // user_id -> Set of socketIds
  private userTyping = new Map<string, Set<string>>(); // conversationId -> Set of userIds

  constructor(
    private jwtService: JwtService,
    private chatService: ChatService,
  ) {}

  async handleConnection(client: AuthenticatedSocket) {
    try {
      const token =
        client.handshake.auth?.token ||
        client.handshake.headers?.authorization?.replace('Bearer ', '');

      if (!token) {
        client.disconnect();
        return;
      }

      const payload = this.jwtService.verify(token);
      client.userId = payload.sub;
      client.workspaceId = client.handshake.query.workspace_id as string;

      if (!client.workspaceId) {
        client.disconnect();
        return;
      }

      // 記錄用戶連接
      if (!this.connectedUsers.has(client.userId!)) {
        this.connectedUsers.set(client.userId!, new Set());
      }
      this.connectedUsers.get(client.userId!)!.add(client.id);

      // 加入用戶的個人房間
      await client.join(`user:${client.userId}`);
      await client.join(`workspace:${client.workspaceId}`);

      // 獲取用戶的對話並加入對應房間
      const { conversations } = await this.chatService.getUserConversations(
        client.workspaceId!,
        client.userId!,
        1,
        100,
      );

      for (const conversation of conversations) {
        await client.join(`conversation:${conversation.id}`);
      }

      // 通知其他用戶該用戶上線
      this.server.to(`workspace:${client.workspaceId}`).emit('user:online', {
        userId: client.userId,
        timestamp: new Date(),
      });

      this.logger.log(`User ${client.userId} connected to workspace ${client.workspaceId}`);
    } catch (error) {
      this.logger.error('Connection authentication failed:', error);
      client.disconnect();
    }
  }

  async handleDisconnect(client: AuthenticatedSocket) {
    if (client.userId) {
      const userSockets = this.connectedUsers.get(client.userId);
      if (userSockets) {
        userSockets.delete(client.id);

        // 如果用戶沒有其他連接，標記為離線
        if (userSockets.size === 0) {
          this.connectedUsers.delete(client.userId);

          if (client.workspaceId) {
            this.server.to(`workspace:${client.workspaceId}`).emit('user:offline', {
              userId: client.userId,
              timestamp: new Date(),
            });
          }
        }
      }

      this.logger.log(`User ${client.userId} disconnected`);
    }
  }

  @SubscribeMessage('message:send')
  async handleSendMessage(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { conversation_id: string; message: SendMessageDto },
  ) {
    try {
      const { conversation_id, message } = data;

      if (!client.userId) {
        return { error: 'Unauthorized' };
      }

      // 發送訊息
      const sentMessage = await this.chatService.sendMessage(
        conversation_id,
        client.userId,
        message,
      );

      // 廣播訊息給對話中的所有用戶
      this.server.to(`conversation:${conversation_id}`).emit('message:new', {
        message: sentMessage,
        conversationId: conversation_id,
      });

      // 停止輸入狀態
      this.handleStopTyping(client, { conversation_id });

      return { success: true, message: sentMessage };
    } catch (error) {
      this.logger.error('Error sending message:', error);
      return { error: error.message };
    }
  }

  @SubscribeMessage('typing:start')
  async handleStartTyping(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { conversation_id: string },
  ) {
    const { conversation_id } = data;

    if (!client.userId) {
      return;
    }

    if (!this.userTyping.has(conversation_id)) {
      this.userTyping.set(conversation_id, new Set());
    }

    this.userTyping.get(conversation_id)!.add(client.userId);

    // 通知其他用戶
    client.to(`conversation:${conversation_id}`).emit('typing:start', {
      userId: client.userId,
      conversationId: conversation_id,
    });
  }

  @SubscribeMessage('typing:stop')
  async handleStopTyping(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { conversation_id: string },
  ) {
    const { conversation_id } = data;

    if (!client.userId) {
      return;
    }

    const typingUsers = this.userTyping.get(conversation_id);
    if (typingUsers) {
      typingUsers.delete(client.userId);

      if (typingUsers.size === 0) {
        this.userTyping.delete(conversation_id);
      }
    }

    // 通知其他用戶
    client.to(`conversation:${conversation_id}`).emit('typing:stop', {
      userId: client.userId,
      conversationId: conversation_id,
    });
  }

  @SubscribeMessage('conversation:join')
  async handleJoinConversation(
    @ConnectedSocket() client: AuthenticatedSocket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const { conversation_id } = data;

      if (!client.userId) {
        return { error: 'Unauthorized' };
      }

      // 驗證用戶是否有權限加入對話
      await this.chatService.getConversation(conversation_id, client.userId);

      // 加入對話房間
      await client.join(`conversation:${conversation_id}`);

      return { success: true };
    } catch (error) {
      this.logger.error('Error joining conversation:', error);
      return { error: error.message };
    }
  }

  // 輔助方法：獲取在線用戶
  getOnlineUsers(workspaceId: string): string[] {
    const onlineUsers: string[] = [];

    for (const [userId, sockets] of this.connectedUsers.entries()) {
      if (sockets.size > 0) {
        // 檢查用戶是否在指定工作區
        for (const socketId of sockets) {
          const socket = this.server.sockets.sockets.get(socketId) as AuthenticatedSocket;
          if (socket?.workspaceId === workspaceId) {
            onlineUsers.push(userId);
            break;
          }
        }
      }
    }

    return onlineUsers;
  }

  // 輔助方法：向特定用戶發送通知
  sendNotificationToUser(userId: string, notification: any) {
    this.server.to(`user:${userId}`).emit('notification', notification);
  }
}
