import {
  IsEnum,
  IsOptional,
  IsString,
  <PERSON><PERSON>rray,
  <PERSON>U<PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ength,
} from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
// 定義對話類型枚舉
enum ConversationType {
  DIRECT = 'DIRECT',
  GROUP = 'GROUP',
  ANNOUNCEMENT = 'ANNOUNCEMENT',
  SYSTEM = 'SYSTEM',
}

export class CreateConversationDto {
  @ApiProperty({
    description: '對話類型',
    enum: ConversationType,
    example: ConversationType.DIRECT,
  })
  @IsEnum(ConversationType)
  type: 'DIRECT' | 'GROUP' | 'ANNOUNCEMENT' | 'SYSTEM';

  @ApiPropertyOptional({
    description: '對話名稱（群組對話必填）',
    example: '專案討論群組',
    minLength: 1,
    maxLength: 100,
  })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({
    description: '對話描述',
    example: '討論專案相關事宜',
    maxLength: 500,
  })
  @IsOptional()
  @IsString()
  @MaxLength(500)
  description?: string;

  @ApiProperty({
    description: '參與者用戶 ID 列表',
    type: [String],
    example: ['user-1', 'user-2'],
  })
  @IsArray()
  @IsUUID('4', { each: true })
  participant_ids: string[];

  @ApiPropertyOptional({
    description: '對話頭像 URL',
    example: 'https://example.com/avatar.jpg',
  })
  @IsOptional()
  @IsString()
  avatar_url?: string;

  @ApiPropertyOptional({
    description: '是否為私人對話',
    example: false,
  })
  @IsOptional()
  is_private?: boolean;
}
