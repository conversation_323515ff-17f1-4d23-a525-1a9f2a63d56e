import {
  IsString,
  IsOptional,
  IsDateString,
  <PERSON>Enum,
  <PERSON>U<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'class-validator';
import { ApiProperty } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { MessageType } from '@prisma/client';

export class SearchMessagesDto {
  @ApiProperty({
    description: '搜尋關鍵字',
    example: 'hello world',
    required: false,
  })
  @IsOptional()
  @IsString()
  query?: string;

  @ApiProperty({
    description: '對話 ID',
    example: 'clxxxxx',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  conversation_id?: string;

  @ApiProperty({
    description: '訊息類型',
    enum: MessageType,
    required: false,
  })
  @IsOptional()
  @IsEnum(MessageType)
  message_type?: MessageType;

  @ApiProperty({
    description: '發送者 ID',
    example: 'clxxxxx',
    required: false,
  })
  @IsOptional()
  @IsUUID()
  sender_id?: string;

  @ApiProperty({
    description: '開始日期 (ISO 8601)',
    example: '2024-01-01T00:00:00.000Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  start_date?: string;

  @ApiProperty({
    description: '結束日期 (ISO 8601)',
    example: '2024-12-31T23:59:59.999Z',
    required: false,
  })
  @IsOptional()
  @IsDateString()
  end_date?: string;

  @ApiProperty({
    description: '頁碼',
    example: 1,
    minimum: 1,
    default: 1,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty({
    description: '每頁數量',
    example: 20,
    minimum: 1,
    maximum: 100,
    default: 20,
    required: false,
  })
  @IsOptional()
  @Transform(({ value }) => parseInt(value))
  @IsInt()
  @Min(1)
  @Max(100)
  limit?: number = 20;
}
