import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { RealtimeEventsService } from '../../../workspace/websocket/realtime-events.service';
import {
  CreateConversationDto,
  UpdateConversationDto,
  ConversationResponseDto,
  SendMessageDto,
  UpdateMessageDto,
  MessageResponseDto,
  CreateNotificationDto,
  UpdateNotificationDto,
  NotificationResponseDto,
  MessageAttachmentDto,
} from '../dto';
import { Prisma, ConversationType, MessageType } from '@prisma/client';

@Injectable()
export class MessageCenterService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly realtimeEventsService: RealtimeEventsService,
  ) {}

  // 對話相關方法
  async createConversation(
    dto: CreateConversationDto,
    tenant_id: string,
    created_by: string,
  ): Promise<ConversationResponseDto> {
    const conversation = await this.prisma.message_conversations.create({
      data: {
        title: dto.title,
        type: dto.type || ConversationType.DIRECT,
        participant_ids: dto.participantIds,
        tenant_id,
        workspace_id: dto.workspaceId,
        created_by,
      },
    });

    const response = this.formatConversationResponse(conversation);

    // 發送即時通知給參與者
    if (Array.isArray(dto.participantIds)) {
      for (const participant_id of dto.participantIds) {
        if (participant_id !== created_by) {
          await this.realtimeEventsService.notifyNewNotification(participant_id, {
            type: 'conversation_created',
            title: '新對話',
            message: `您被添加到新對話: ${dto.title}`,
            entity_type: 'conversation',
            entity_id: conversation.id,
          });
        }
      }
    }

    return response;
  }

  async getConversations(
    tenantId: string,
    workspaceId?: string,
    userId?: string,
  ): Promise<ConversationResponseDto[]> {
    const where: Prisma.message_conversationsWhereInput = {
      tenant_id: tenantId,
      is_active: true,
      ...(workspaceId && { workspace_id: workspaceId }),
      ...(userId && {
        participant_ids: {
          array_contains: [userId],
        },
      }),
    };

    const conversations = await this.prisma.message_conversations.findMany({
      where,
      orderBy: {
        last_message_at: 'desc',
      },
    });

    const conversationsWithUnread = await Promise.all(
      conversations.map(async (conv) => {
        const unreadCount = await this.getUnreadMessageCount(conv.id, userId);
        return this.formatConversationResponse(conv, unreadCount);
      }),
    );

    return conversationsWithUnread;
  }

  async getConversation(
    id: string,
    tenant_id: string,
    user_id?: string,
  ): Promise<ConversationResponseDto> {
    const conversation = await this.prisma.message_conversations.findFirst({
      where: {
        id,
        tenant_id,
        is_active: true,
        ...(user_id && {
          participant_ids: {
            array_contains: [user_id],
          },
        }),
      },
    });

    if (!conversation) {
      throw new NotFoundException('對話不存在');
    }

    const unread_count = await this.getUnreadMessageCount(id, user_id);
    return this.formatConversationResponse(conversation, unread_count);
  }

  async updateConversation(
    id: string,
    dto: UpdateConversationDto,
    tenant_id: string,
    user_id?: string,
  ): Promise<ConversationResponseDto> {
    // 檢查權限
    const conversation = await this.getConversation(id, tenant_id, user_id);

    const updated = await this.prisma.message_conversations.update({
      where: { id },
      data: dto,
    });

    const response = this.formatConversationResponse(updated);

    // 通知對話更新
    await this.realtimeEventsService.notifyNewMessageCenterMessage(
      id,
      {
        type: 'conversation_updated',
        content: '對話已更新',
        changes: dto,
      },
      user_id || 'system',
    );

    return response;
  }

  async deleteConversation(id: string, tenant_id: string, user_id?: string): Promise<void> {
    // 檢查權限
    await this.getConversation(id, tenant_id, user_id);

    await this.prisma.message_conversations.update({
      where: { id },
      data: { is_active: false },
    });

    // 通知對話刪除
    await this.realtimeEventsService.notifyNewMessageCenterMessage(
      id,
      {
        type: 'conversation_deleted',
        content: '對話已刪除',
      },
      user_id || 'system',
    );
  }

  // 訊息相關方法
  async sendMessage(
    dto: SendMessageDto,
    tenant_id: string,
    sender_id: string,
    sender_type: string,
    sender_name: string,
    attachments?: MessageAttachmentDto[],
  ): Promise<MessageResponseDto> {
    // 檢查對話權限
    await this.getConversation(dto.conversationId, tenant_id, sender_id);

    const message = await this.prisma.message_center_messages.create({
      data: {
        conversation_id: dto.conversationId,
        content: dto.content,
        content_type: dto.contentType || MessageType.TEXT,
        sender_id: sender_id,
        sender_type: sender_type,
        sender_name: sender_name,
        reply_to_message_id: dto.replyToMessageId,
        attachments: attachments as any,
        tenant_id,
      },
    });

    // 更新對話的最後訊息時間
    await this.prisma.message_conversations.update({
      where: { id: dto.conversationId },
      data: {
        last_message_id: message.id,
        last_message_at: message.sent_at,
      },
    });

    const response = this.formatMessageResponse(message);

    // 發送即時通知
    await this.realtimeEventsService.notifyNewMessageCenterMessage(
      dto.conversationId,
      response,
      sender_id,
    );

    // 更新未讀計數給其他參與者
    const conversation = await this.prisma.message_conversations.findUnique({
      where: { id: dto.conversationId },
    });

    if (conversation?.participant_ids && Array.isArray(conversation.participant_ids)) {
      for (const participant_id of conversation.participant_ids as string[]) {
        if (participant_id !== sender_id) {
          const unread_count = await this.getUnreadMessageCount(dto.conversationId, participant_id);
          await this.realtimeEventsService.notifyUnreadCountUpdate(participant_id, unread_count);
        }
      }
    }

    return response;
  }

  async getMessages(
    conversation_id: string,
    tenant_id: string,
    user_id?: string,
    limit = 50,
    offset = 0,
  ): Promise<MessageResponseDto[]> {
    // 檢查對話權限
    await this.getConversation(conversation_id, tenant_id, user_id);

    const messages = await this.prisma.message_center_messages.findMany({
      where: {
        conversation_id,
        tenant_id,
        is_deleted: false,
      },
      orderBy: {
        sent_at: 'desc',
      },
      take: limit,
      skip: offset,
    });

    return messages.map((message) => this.formatMessageResponse(message));
  }

  async markMessageAsRead(message_id: string, tenant_id: string, user_id?: string): Promise<void> {
    const message = await this.prisma.message_center_messages.findFirst({
      where: {
        id: message_id,
        tenant_id,
        is_deleted: false,
      },
    });

    if (!message) {
      throw new NotFoundException('訊息不存在');
    }

    // 這裡可以實作已讀狀態的邏輯
    // 例如在 message_read_status 表中記錄

    // 通知已讀狀態更新
    if (user_id) {
      await this.realtimeEventsService.notifyUnreadCountUpdate(
        user_id,
        await this.getUnreadMessageCount(message.conversation_id, user_id),
      );
    }
  }

  // 通知相關方法
  async createNotification(
    dto: CreateNotificationDto,
    tenant_id: string,
  ): Promise<NotificationResponseDto> {
    const notification = await this.prisma.message_center_notifications.create({
      data: {
        title: dto.title,
        message: dto.message,
        type: dto.type,
        priority: dto.priority,
        recipient_id: dto.recipient_id,
        recipient_type: dto.recipient_type,
        entity_type: dto.entity_type,
        entity_id: dto.entity_id,
        action_url: dto.action_url,
        tenant_id,
        workspace_id: dto.workspace_id,
        expires_at: dto.expires_at,
      },
    });

    const response = this.formatNotificationResponse(notification);

    // 發送即時通知
    await this.realtimeEventsService.notifyNewNotification(dto.recipient_id, response);

    return response;
  }

  async getNotifications(
    tenant_id: string,
    recipient_id?: string,
    workspace_id?: string,
    limit = 50,
    offset = 0,
  ): Promise<NotificationResponseDto[]> {
    const where: Prisma.message_center_notificationsWhereInput = {
      tenant_id,
      ...(recipient_id && { recipient_id: recipient_id }),
      ...(workspace_id && { workspace_id: workspace_id }),
    };

    const notifications = await this.prisma.message_center_notifications.findMany({
      where,
      orderBy: {
        created_at: 'desc',
      },
      take: limit,
      skip: offset,
    });

    return notifications.map((notification) => this.formatNotificationResponse(notification));
  }

  async markNotificationAsRead(
    notification_id: string,
    tenant_id: string,
    recipient_id?: string,
  ): Promise<void> {
    const where: Prisma.message_center_notificationsWhereInput = {
      id: notification_id,
      tenant_id,
      ...(recipient_id && { recipient_id: recipient_id }),
    };

    const notification = await this.prisma.message_center_notifications.findFirst({
      where,
    });

    if (!notification) {
      throw new NotFoundException('通知不存在');
    }

    await this.prisma.message_center_notifications.update({
      where: { id: notification_id },
      data: {
        is_read: true,
        read_at: new Date(),
      },
    });

    // 通知已讀狀態更新
    if (recipient_id) {
      await this.realtimeEventsService.notifyNotificationRead(recipient_id, notification_id);
    }
  }

  async markAllNotificationsAsRead(
    tenant_id: string,
    recipient_id?: string,
    workspace_id?: string,
  ): Promise<void> {
    const where: Prisma.message_center_notificationsWhereInput = {
      tenant_id,
      is_read: false,
      ...(recipient_id && { recipient_id: recipient_id }),
      ...(workspace_id && { workspace_id: workspace_id }),
    };

    await this.prisma.message_center_notifications.updateMany({
      where,
      data: {
        is_read: true,
        read_at: new Date(),
      },
    });

    // 通知批量已讀
    if (recipient_id) {
      await this.realtimeEventsService.notifyUnreadCountUpdate(recipient_id, 0);
    }
  }

  // 私有輔助方法
  private async getUnreadMessageCount(conversation_id: string, user_id?: string): Promise<number> {
    if (!user_id) return 0;

    // 這裡需要實作未讀訊息計數邏輯
    // 可能需要一個 message_read_status 表來追蹤已讀狀態
    return 0;
  }

  private formatConversationResponse(conversation: any, unreadCount = 0): ConversationResponseDto {
    return {
      id: conversation.id,
      title: conversation.title,
      type: conversation.type,
      participant_ids: conversation.participant_ids,
      last_message_id: conversation.last_message_id,
      last_message_at: conversation.last_message_at,
      unread_count: unreadCount,
      tenant_id: conversation.tenant_id,
      workspace_id: conversation.workspace_id,
      created_by: conversation.created_by,
      created_at: conversation.created_at,
      updated_at: conversation.updated_at,
      is_active: conversation.is_active,
      is_archived: conversation.is_archived || false,
    };
  }

  private formatMessageResponse(message: any): MessageResponseDto {
    return {
      id: message.id,
      conversation_id: message.conversation_id,
      content: message.content,
      content_type: message.content_type,
      sender_id: message.sender_id,
      sender_type: message.sender_type,
      sender_name: message.sender_name,
      reply_to_message_id: message.reply_to_message_id,
      attachments: message.attachments || [],
      is_read: message.is_read || false,
      is_edited: message.is_edited || false,
      is_deleted: message.is_deleted || false,
      tenant_id: message.tenant_id,
      sent_at: message.sent_at,
      edited_at: message.edited_at,
      read_at: message.read_at,
      deleted_at: message.deleted_at,
    };
  }

  private formatNotificationResponse(notification: any): NotificationResponseDto {
    return {
      id: notification.id,
      title: notification.title,
      message: notification.message,
      type: notification.type,
      priority: notification.priority,
      recipient_id: notification.recipient_id,
      recipient_type: notification.recipient_type,
      entity_type: notification.entity_type,
      entity_id: notification.entity_id,
      action_url: notification.action_url,
      is_read: notification.is_read,
      is_archived: notification.is_archived,
      tenant_id: notification.tenant_id,
      workspace_id: notification.workspace_id,
      created_at: notification.created_at,
      read_at: notification.read_at,
      archived_at: notification.archived_at,
      expires_at: notification.expires_at,
    };
  }
}
