import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsArray, IsEnum, IsBoolean } from 'class-validator';
import { ConversationType } from '@prisma/client';
import { Transform } from 'class-transformer';

export class CreateConversationDto {
  @ApiProperty({ description: '對話標題' })
  @IsString()
  title: string;

  @ApiPropertyOptional({
    description: '對話類型',
    enum: ConversationType,
    default: ConversationType.DIRECT,
  })
  @IsOptional()
  @IsEnum(ConversationType)
  type?: ConversationType;

  @ApiProperty({ description: '參與者 ID 列表' })
  @IsArray()
  @IsString({ each: true })
  @Transform(({ value }) => value, { toPlainOnly: true })
  participantIds: string[];

  @ApiPropertyOptional({ description: '工作區 ID' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value, { toPlainOnly: true })
  workspaceId?: string;
}

export class UpdateConversationDto {
  @ApiPropertyOptional({ description: '對話標題' })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiPropertyOptional({ description: '是否歸檔' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_archived?: boolean;
}

export class ConversationResponseDto {
  @ApiProperty({ description: '對話 ID' })
  id: string;

  @ApiProperty({ description: '對話標題' })
  title: string;

  @ApiProperty({ description: '對話類型' })
  type: ConversationType;

  @ApiProperty({ description: '參與者 ID 列表' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  participant_ids: string[];

  @ApiProperty({ description: '最後訊息 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  last_message_id?: string;

  @ApiProperty({ description: '最後訊息時間' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  last_message_at?: Date;

  @ApiProperty({ description: '未讀訊息數量' })
  unread_count: number;

  @ApiProperty({ description: '租戶 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  tenant_id: string;

  @ApiProperty({ description: '工作區 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  workspace_id?: string;

  @ApiProperty({ description: '是否活躍' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_active: boolean;

  @ApiProperty({ description: '是否歸檔' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_archived: boolean;

  @ApiProperty({ description: '創建時間' })
  created_at: Date;

  @ApiProperty({ description: '更新時間' })
  updated_at: Date;

  @ApiProperty({ description: '創建者 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  created_by: string;
}
