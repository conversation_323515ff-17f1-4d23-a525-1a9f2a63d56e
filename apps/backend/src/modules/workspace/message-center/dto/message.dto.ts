import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsArray } from 'class-validator';
import { MessageType } from '@prisma/client';
import { Transform } from 'class-transformer';

export class SendMessageDto {
  @ApiProperty({ description: '對話 ID' })
  @IsString()
  @Transform(({ value }) => value, { toPlainOnly: true })
  conversationId: string;

  @ApiProperty({ description: '訊息內容' })
  @IsString()
  content: string;

  @ApiPropertyOptional({
    description: '內容類型',
    enum: MessageType,
    default: MessageType.TEXT,
  })
  @IsOptional()
  @IsEnum(MessageType)
  @Transform(({ value }) => value, { toPlainOnly: true })
  contentType?: MessageType;

  @ApiPropertyOptional({ description: '回覆的訊息 ID' })
  @IsOptional()
  @IsString()
  @Transform(({ value }) => value, { toPlainOnly: true })
  replyToMessageId?: string;

  @ApiPropertyOptional({ description: '附件' })
  @IsOptional()
  @IsArray()
  attachments?: File[];
}

export class UpdateMessageDto {
  @ApiPropertyOptional({ description: '訊息內容' })
  @IsOptional()
  @IsString()
  content?: string;

  @ApiPropertyOptional({ description: '附件' })
  @IsOptional()
  @IsArray()
  attachments?: any[];
}

export class MessageAttachmentDto {
  @ApiProperty({ description: '檔案名稱' })
  filename: string;

  @ApiProperty({ description: '檔案大小' })
  size: number;

  @ApiProperty({ description: '檔案類型' })
  mime_type: string;

  @ApiProperty({ description: '檔案 URL' })
  url: string;
}

export class MessageResponseDto {
  @ApiProperty({ description: '訊息 ID' })
  id: string;

  @ApiProperty({ description: '對話 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  conversation_id: string;

  @ApiProperty({ description: '訊息內容' })
  content: string;

  @ApiProperty({ description: '內容類型' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  content_type: MessageType;

  @ApiProperty({ description: '發送者 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  sender_id: string;

  @ApiProperty({ description: '發送者類型' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  sender_type: string;

  @ApiProperty({ description: '發送者名稱' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  sender_name: string;

  @ApiProperty({ description: '回覆的訊息 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  reply_to_message_id?: string;

  @ApiProperty({ description: '附件', type: [MessageAttachmentDto] })
  attachments?: MessageAttachmentDto[];

  @ApiProperty({ description: '是否已讀' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_read: boolean;

  @ApiProperty({ description: '是否已編輯' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_edited: boolean;

  @ApiProperty({ description: '是否已刪除' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  is_deleted: boolean;

  @ApiProperty({ description: '租戶 ID' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  tenant_id: string;

  @ApiProperty({ description: '發送時間' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  sent_at: Date;

  @ApiProperty({ description: '編輯時間' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  edited_at?: Date;

  @ApiProperty({ description: '閱讀時間' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  read_at?: Date;

  @ApiProperty({ description: '刪除時間' })
  @Transform(({ value }) => value, { toPlainOnly: true })
  deleted_at?: Date;
}
