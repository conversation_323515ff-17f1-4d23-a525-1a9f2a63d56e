import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsEnum, IsBoolean } from 'class-validator';
import { NotificationType, NotificationPriority } from '@prisma/client';

export class CreateNotificationDto {
  @ApiProperty({ description: '通知標題' })
  @IsString()
  title: string;

  @ApiProperty({ description: '通知訊息' })
  @IsString()
  message: string;

  @ApiPropertyOptional({
    description: '通知類型',
    enum: NotificationType,
    default: NotificationType.INFO,
  })
  @IsOptional()
  @IsEnum(NotificationType)
  type?: NotificationType;

  @ApiPropertyOptional({
    description: '通知優先級',
    enum: NotificationPriority,
    default: NotificationPriority.NORMAL,
  })
  @IsOptional()
  @IsEnum(NotificationPriority)
  priority?: NotificationPriority;

  @ApiProperty({ description: '接收者 ID' })
  @IsString()
  recipient_id: string;

  @ApiProperty({ description: '接收者類型' })
  @IsString()
  recipient_type: string;

  @ApiPropertyOptional({ description: '實體類型' })
  @IsOptional()
  @IsString()
  entity_type?: string;

  @ApiPropertyOptional({ description: '實體 ID' })
  @IsOptional()
  @IsString()
  entity_id?: string;

  @ApiPropertyOptional({ description: '動作 URL' })
  @IsOptional()
  @IsString()
  action_url?: string;

  @ApiPropertyOptional({ description: '工作區 ID' })
  @IsOptional()
  @IsString()
  workspace_id?: string;

  @ApiPropertyOptional({ description: '過期時間' })
  @IsOptional()
  expires_at?: Date;
}

export class UpdateNotificationDto {
  @ApiPropertyOptional({ description: '是否已讀' })
  @IsOptional()
  @IsBoolean()
  is_read?: boolean;

  @ApiPropertyOptional({ description: '是否歸檔' })
  @IsOptional()
  @IsBoolean()
  is_archived?: boolean;
}

export class NotificationResponseDto {
  @ApiProperty({ description: '通知 ID' })
  id: string;

  @ApiProperty({ description: '通知標題' })
  title: string;

  @ApiProperty({ description: '通知訊息' })
  message: string;

  @ApiProperty({ description: '通知類型' })
  type: NotificationType;

  @ApiProperty({ description: '通知優先級' })
  priority: NotificationPriority;

  @ApiProperty({ description: '接收者 ID' })
  recipient_id: string;

  @ApiProperty({ description: '接收者類型' })
  recipient_type: string;

  @ApiProperty({ description: '實體類型' })
  entity_type?: string;

  @ApiProperty({ description: '實體 ID' })
  entity_id?: string;

  @ApiProperty({ description: '動作 URL' })
  action_url?: string;

  @ApiProperty({ description: '是否已讀' })
  is_read: boolean;

  @ApiProperty({ description: '是否歸檔' })
  is_archived: boolean;

  @ApiProperty({ description: '租戶 ID' })
  tenant_id: string;

  @ApiProperty({ description: '工作區 ID' })
  workspace_id?: string;

  @ApiProperty({ description: '創建時間' })
  created_at: Date;

  @ApiProperty({ description: '閱讀時間' })
  read_at?: Date;

  @ApiProperty({ description: '歸檔時間' })
  archived_at?: Date;

  @ApiProperty({ description: '過期時間' })
  expires_at?: Date;
}
