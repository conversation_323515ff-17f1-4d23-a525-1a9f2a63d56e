import { Modu<PERSON> } from '@nestjs/common';
import { MessageCenterController } from './controllers/message-center.controller';
import { MessageCenterService } from './services/message-center.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { AuthModule } from '../../core/auth/auth.module';
import { WebSocketModule } from '../websocket/websocket.module';

@Module({
  imports: [PrismaModule, AuthModule, WebSocketModule],
  controllers: [MessageCenterController],
  providers: [MessageCenterService],
  exports: [MessageCenterService],
})
export class MessageCenterModule {}
