import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateProjectDto, UpdateProjectDto, ProjectResponseDto } from './dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class ProjectsService {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    createProjectDto: CreateProjectDto,
    userId: string,
    tenantId: string,
  ): Promise<ProjectResponseDto> {
    try {
      // 驗證日期邏輯
      if (createProjectDto.startDate && createProjectDto.endDate) {
        if (new Date(createProjectDto.startDate) >= new Date(createProjectDto.endDate)) {
          throw new BadRequestException('開始日期必須早於結束日期');
        }
      }

      // 處理階層結構
      let level = 0;
      let path = '';

      if (createProjectDto.parentProjectId) {
        // 驗證父專案存在且屬於同一租戶
        const parentProject = await this.prisma.projects.findFirst({
          where: {
            id: createProjectDto.parentProjectId,
            tenant_id: tenantId,
          },
        });

        if (!parentProject) {
          throw new BadRequestException('父專案不存在或無權限存取');
        }

        // 檢查階層深度限制（最多 5 層）
        if (parentProject.level >= 4) {
          throw new BadRequestException('專案階層深度不能超過 5 層');
        }

        level = parentProject.level + 1;
        path = parentProject.path
          ? `${parentProject.path}/${parentProject.name}`
          : `/${parentProject.name}`;
      }

      const project = await this.prisma.projects.create({
        data: {
          name: createProjectDto.name,
          description: createProjectDto.description,
          status: createProjectDto.status || 'planning',
          start_date: createProjectDto.startDate,
          end_date: createProjectDto.endDate,
          budget: createProjectDto.budget,
          priority: createProjectDto.priority || 'medium',
          parent_project_id: createProjectDto.parentProjectId,
          level,
          path,
          tenant_id: tenantId,
          user_id: userId,
        },
        include: {
          parent_project: {
            select: {
              id: true,
              name: true,
              status: true,
            },
          },
          sub_projects: {
            select: {
              id: true,
              name: true,
              status: true,
              level: true,
            },
          },
        },
      });

      return this.mapToResponseDto(project);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new BadRequestException('專案名稱已存在');
        }
      }
      throw error;
    }
  }

  async findAll(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
    priority?: string,
  ): Promise<{
    projects: ProjectResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const where: Prisma.projectsWhereInput = {
      tenant_id: tenantId,
      ...(search && {
        OR: [
          { name: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(status && { status }),
      ...(priority && { priority }),
    };

    const [projects, total] = await Promise.all([
      this.prisma.projects.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
      }),
      this.prisma.projects.count({ where }),
    ]);

    return {
      projects: projects.map((project) => this.mapToResponseDto(project)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, tenantId: string): Promise<ProjectResponseDto> {
    const project = await this.prisma.projects.findFirst({
      where: { id, tenant_id: tenantId },
      include: {
        parent_project: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
            status: true,
            level: true,
          },
        },
        tasks: {
          select: {
            id: true,
            title: true,
            status: true,
            priority: true,
            due_date: true,
            assignee_id: true,
          },
        },
        progress_entries: {
          select: {
            id: true,
            title: true,
            progress_type: true,
            progress_value: true,
            recorded_at: true,
          },
          orderBy: { recorded_at: 'desc' },
          take: 5, // 最近 5 個進度記錄
        },
        project_milestones: {
          select: {
            id: true,
            title: true,
            status: true,
            target_date: true,
            completed_at: true,
          },
        },
        _count: {
          select: {
            tasks: true,
            progress_entries: true,
            project_milestones: true,
            sub_projects: true,
          },
        },
      },
    });

    if (!project) {
      throw new NotFoundException('專案不存在');
    }

    return this.mapToResponseDto(project);
  }

  async update(
    id: string,
    updateProjectDto: UpdateProjectDto,
    userId: string,
    tenantId: string,
  ): Promise<ProjectResponseDto> {
    // 檢查專案是否存在且屬於該租戶
    const existingProject = await this.prisma.projects.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!existingProject) {
      throw new NotFoundException('專案不存在');
    }

    // 驗證日期邏輯
    const startDate = updateProjectDto.startDate || existingProject.start_date;
    const endDate = updateProjectDto.endDate || existingProject.end_date;

    if (startDate && endDate && new Date(startDate) >= new Date(endDate)) {
      throw new BadRequestException('開始日期必須早於結束日期');
    }

    try {
      const project = await this.prisma.projects.update({
        where: { id },
        data: {
          ...(updateProjectDto.name && { name: updateProjectDto.name }),
          ...(updateProjectDto.description !== undefined && {
            description: updateProjectDto.description,
          }),
          ...(updateProjectDto.status && { status: updateProjectDto.status }),
          ...(updateProjectDto.startDate && { start_date: updateProjectDto.startDate }),
          ...(updateProjectDto.endDate && { end_date: updateProjectDto.endDate }),
          ...(updateProjectDto.budget !== undefined && { budget: updateProjectDto.budget }),
          ...(updateProjectDto.priority && { priority: updateProjectDto.priority }),
          updated_at: new Date(),
        },
      });

      return this.mapToResponseDto(project);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new BadRequestException('專案名稱已存在');
        }
      }
      throw error;
    }
  }

  async remove(id: string, tenantId: string): Promise<void> {
    const project = await this.prisma.projects.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!project) {
      throw new NotFoundException('專案不存在');
    }

    await this.prisma.projects.delete({
      where: { id },
    });
  }

  async getProjectStats(tenantId: string): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
  }> {
    const [total, statusStats, priorityStats] = await Promise.all([
      this.prisma.projects.count({ where: { tenant_id: tenantId } }),
      this.prisma.projects.groupBy({
        by: ['status'],
        where: { tenant_id: tenantId },
        _count: { status: true },
      }),
      this.prisma.projects.groupBy({
        by: ['priority'],
        where: { tenant_id: tenantId },
        _count: { priority: true },
      }),
    ]);

    const byStatus = statusStats.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count?.status || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const byPriority = priorityStats.reduce(
      (acc, stat) => {
        acc[stat.priority] = stat._count?.priority || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    return { total, byStatus, byPriority };
  }

  async getProjectDetailedStats(
    projectId: string,
    tenantId: string,
  ): Promise<{
    project: {
      id: string;
      name: string;
      status: string;
      priority: string;
    };
    tasks: {
      total: number;
      completed: number;
      inProgress: number;
      pending: number;
      overdue: number;
      completionRate: number;
    };
    progress: {
      totalEntries: number;
      latestProgress: number | null;
      averageProgress: number | null;
    };
    milestones: {
      total: number;
      completed: number;
      pending: number;
      overdue: number;
    };
  }> {
    // 驗證專案存在
    const project = await this.prisma.projects.findFirst({
      where: { id: projectId, tenant_id: tenantId },
      select: { id: true, name: true, status: true, priority: true },
    });

    if (!project) {
      throw new NotFoundException('專案不存在');
    }

    // 獲取任務統計
    const [totalTasks, tasksByStatus, overdueTasks, progressEntries, milestones] =
      await Promise.all([
        this.prisma.tasks.count({
          where: { project_id: projectId, tenant_id: tenantId },
        }),
        this.prisma.tasks.groupBy({
          by: ['status'],
          where: { project_id: projectId, tenant_id: tenantId },
          _count: { status: true },
        }),
        this.prisma.tasks.count({
          where: {
            project_id: projectId,
            tenant_id: tenantId,
            due_date: { lt: new Date() },
            status: { not: 'completed' },
          },
        }),
        this.prisma.progress_entries.findMany({
          where: { project_id: projectId, tenant_id: tenantId },
          select: { progress_value: true },
          orderBy: { recorded_at: 'desc' },
        }),
        this.prisma.project_milestones.groupBy({
          by: ['status'],
          where: { project_id: projectId, tenant_id: tenantId },
          _count: { status: true },
        }),
      ]);

    // 處理任務統計
    const taskStats = tasksByStatus.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count?.status || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const completedTasks = taskStats['completed'] || 0;
    const inProgressTasks = taskStats['in-progress'] || 0;
    const pendingTasks = taskStats['pending'] || taskStats['todo'] || 0;
    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    // 處理進度統計
    const validProgressValues = progressEntries
      .map((entry) => entry.progress_value)
      .filter((value) => value !== null && value !== undefined) as number[];

    const latestProgress = validProgressValues.length > 0 ? validProgressValues[0] : null;
    const averageProgress =
      validProgressValues.length > 0
        ? validProgressValues.reduce((sum, val) => sum + val, 0) / validProgressValues.length
        : null;

    // 處理里程碑統計
    const milestoneStats = milestones.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count?.status || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const totalMilestones = milestones.reduce((sum, stat) => sum + (stat._count?.status || 0), 0);
    const completedMilestones = milestoneStats['COMPLETED'] || 0;
    const pendingMilestones = milestoneStats['PENDING'] || 0;
    const overdueMilestones = milestoneStats['OVERDUE'] || 0;

    return {
      project,
      tasks: {
        total: totalTasks,
        completed: completedTasks,
        inProgress: inProgressTasks,
        pending: pendingTasks,
        overdue: overdueTasks,
        completionRate: Math.round(completionRate * 100) / 100,
      },
      progress: {
        totalEntries: progressEntries.length,
        latestProgress,
        averageProgress: averageProgress ? Math.round(averageProgress * 100) / 100 : null,
      },
      milestones: {
        total: totalMilestones,
        completed: completedMilestones,
        pending: pendingMilestones,
        overdue: overdueMilestones,
      },
    };
  }

  /**
   * 獲取專案階層樹狀結構
   */
  async getProjectHierarchy(tenantId: string): Promise<ProjectResponseDto[]> {
    const rootProjects = await this.prisma.projects.findMany({
      where: {
        tenant_id: tenantId,
        parent_project_id: null,
      },
      include: {
        _count: {
          select: {
            sub_projects: true,
            tasks: true,
          },
        },
      },
      orderBy: { created_at: 'asc' },
    });

    return rootProjects.map((project) => this.mapToResponseDto(project));
  }

  /**
   * 獲取專案的所有子專案（遞迴）
   */
  async getSubProjects(projectId: string, tenantId: string): Promise<ProjectResponseDto[]> {
    const project = await this.prisma.projects.findFirst({
      where: { id: projectId, tenant_id: tenantId },
    });

    if (!project) {
      throw new NotFoundException('專案不存在');
    }

    const subProjects = await this.prisma.projects.findMany({
      where: {
        tenant_id: tenantId,
        OR: [
          { parent_project_id: projectId },
          {
            path: {
              startsWith: project.path ? `${project.path}/${project.name}` : `/${project.name}`,
            },
          },
        ],
      },
      include: {
        _count: {
          select: {
            sub_projects: true,
            tasks: true,
          },
        },
      },
      orderBy: [{ level: 'asc' }, { created_at: 'asc' }],
    });

    return subProjects.map((project) => this.mapToResponseDto(project));
  }

  /**
   * 移動專案到新的父專案下
   */
  async moveProject(
    projectId: string,
    newParentProjectId: string | null,
    tenantId: string,
  ): Promise<ProjectResponseDto> {
    const project = await this.prisma.projects.findFirst({
      where: { id: projectId, tenant_id: tenantId },
      include: {
        sub_projects: true,
      },
    });

    if (!project) {
      throw new NotFoundException('專案不存在');
    }

    let newLevel = 0;
    let newPath = '';

    if (newParentProjectId) {
      // 驗證新父專案存在
      const newParentProject = await this.prisma.projects.findFirst({
        where: { id: newParentProjectId, tenant_id: tenantId },
      });

      if (!newParentProject) {
        throw new BadRequestException('新父專案不存在');
      }

      // 檢查是否會造成循環引用
      if (await this.wouldCreateCircularReference(projectId, newParentProjectId, tenantId)) {
        throw new BadRequestException('移動會造成循環引用');
      }

      // 檢查階層深度
      if (newParentProject.level >= 4) {
        throw new BadRequestException('專案階層深度不能超過 5 層');
      }

      newLevel = newParentProject.level + 1;
      newPath = newParentProject.path
        ? `${newParentProject.path}/${newParentProject.name}`
        : `/${newParentProject.name}`;
    }

    // 更新專案及其所有子專案的階層資訊
    await this.updateProjectHierarchy(projectId, newLevel, newPath, tenantId);

    const updatedProject = await this.prisma.projects.update({
      where: { id: projectId },
      data: {
        parent_project_id: newParentProjectId,
        level: newLevel,
        path: newPath,
      },
      include: {
        parent_project: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        sub_projects: {
          select: {
            id: true,
            name: true,
            status: true,
            level: true,
          },
        },
      },
    });

    return this.mapToResponseDto(updatedProject);
  }

  /**
   * 檢查是否會造成循環引用
   */
  private async wouldCreateCircularReference(
    projectId: string,
    newParentProjectId: string,
    tenantId: string,
  ): Promise<boolean> {
    let currentParentId: string | null = projectId;

    while (currentParentId) {
      if (currentParentId === newParentProjectId) {
        return true;
      }

      const parent = await this.prisma.projects.findFirst({
        where: { id: currentParentId, tenant_id: tenantId },
        select: { parent_project_id: true },
      });

      currentParentId = parent?.parent_project_id || null;
    }

    return false;
  }

  /**
   * 遞迴更新專案及其子專案的階層資訊
   */
  private async updateProjectHierarchy(
    projectId: string,
    newLevel: number,
    newPath: string,
    tenantId: string,
  ): Promise<void> {
    const project = await this.prisma.projects.findFirst({
      where: { id: projectId, tenant_id: tenantId },
      include: { sub_projects: true },
    });

    if (!project) return;

    // 更新子專案
    for (const subProject of project.sub_projects) {
      const subProjectNewLevel = newLevel + 1;
      const subProjectNewPath = newPath ? `${newPath}/${project.name}` : `/${project.name}`;

      await this.updateProjectHierarchy(
        subProject.id,
        subProjectNewLevel,
        subProjectNewPath,
        tenantId,
      );
    }

    await this.prisma.projects.update({
      where: { id: projectId },
      data: {
        level: newLevel,
        path: newPath,
      },
    });
  }

  private mapToResponseDto(project: any): ProjectResponseDto {
    return {
      id: project.id,
      name: project.name,
      description: project.description,
      status: project.status,
      startDate: project.start_date,
      endDate: project.end_date,
      budget: project.budget,
      priority: project.priority,
      tenantId: project.tenant_id,
      userId: project.user_id,
      parentProjectId: project.parent_project_id,
      level: project.level || 0,
      path: project.path,
      created_at: project.created_at,
      updated_at: project.updated_at,
      parentProject: project.parent_project,
      subProjects: project.sub_projects,
    };
  }
}
