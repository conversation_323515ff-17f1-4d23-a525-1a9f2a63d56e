import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProjectResponseDto {
  @ApiProperty({
    description: '專案 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  id: string;

  @ApiProperty({
    description: '專案名稱',
    example: '新產品開發專案',
  })
  name: string;

  @ApiPropertyOptional({
    description: '專案描述',
    example: '開發新的 SaaS 產品，包含前後端功能',
  })
  description?: string;

  @ApiProperty({
    description: '專案狀態',
    example: 'active',
    enum: ['planning', 'active', 'on-hold', 'completed', 'cancelled'],
  })
  status: string;

  @ApiPropertyOptional({
    description: '專案開始日期',
    example: '2024-01-01T00:00:00.000Z',
  })
  startDate?: Date;

  @ApiPropertyOptional({
    description: '專案結束日期',
    example: '2024-12-31T23:59:59.999Z',
  })
  endDate?: Date;

  @ApiPropertyOptional({
    description: '專案預算',
    example: 100000,
  })
  budget?: number;

  @ApiProperty({
    description: '專案優先級',
    example: 'high',
    enum: ['low', 'medium', 'high', 'urgent'],
  })
  priority: string;

  @ApiProperty({
    description: '租戶 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  tenantId: string;

  @ApiProperty({
    description: '創建者 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  userId: string;

  @ApiProperty({
    description: '創建時間',
    example: '2024-01-01T00:00:00.000Z',
  })
  created_at: Date;

  @ApiProperty({
    description: '更新時間',
    example: '2024-01-01T12:00:00.000Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: '父專案 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  parentProjectId?: string;

  @ApiProperty({
    description: '專案層級（0為根專案）',
    example: 0,
  })
  level: number;

  @ApiPropertyOptional({
    description: '專案路徑（用於快速查詢階層）',
    example: '/root-project/sub-project',
  })
  path?: string;

  @ApiPropertyOptional({
    description: '子專案列表',
    type: 'array',
    items: {
      type: 'object',
      properties: {
        id: { type: 'string' },
        name: { type: 'string' },
        status: { type: 'string' },
        level: { type: 'number' },
      },
    },
  })
  subProjects?: Array<{
    id: string;
    name: string;
    status: string;
    level: number;
  }>;

  @ApiPropertyOptional({
    description: '父專案資訊',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string' },
    },
  })
  parentProject?: {
    id: string;
    name: string;
    status: string;
  };
}
