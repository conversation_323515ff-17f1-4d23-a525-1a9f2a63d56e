import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsString, IsOptional, IsDateString, IsEnum, IsUUID } from 'class-validator';
import { MilestoneStatus } from '@prisma/client';

export class CreateMilestoneDto {
  @ApiProperty({
    description: '里程碑標題',
    example: '第一階段開發完成',
  })
  @IsString()
  title: string;

  @ApiPropertyOptional({
    description: '里程碑描述',
    example: '完成用戶管理和基本功能開發',
  })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '目標完成日期',
    example: '2024-07-01T00:00:00Z',
  })
  @IsDateString()
  targetDate: string;

  @ApiProperty({
    description: '關聯的項目 ID',
    example: 'clxxxxx',
  })
  @IsUUID()
  projectId: string;

  @ApiPropertyOptional({
    description: '優先級',
    example: 'high',
    default: 'medium',
  })
  @IsOptional()
  @IsString()
  priority?: string;
}
