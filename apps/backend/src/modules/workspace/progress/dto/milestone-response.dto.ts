import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { MilestoneStatus } from '@prisma/client';

export class MilestoneResponseDto {
  @ApiProperty({
    description: '里程碑 ID',
    example: 'clxxxxx',
  })
  id: string;

  @ApiProperty({
    description: '里程碑標題',
    example: '第一階段開發完成',
  })
  title: string;

  @ApiPropertyOptional({
    description: '里程碑描述',
    example: '完成用戶管理和基本功能開發',
  })
  description?: string;

  @ApiProperty({
    description: '目標完成日期',
    example: '2024-07-01T00:00:00Z',
  })
  targetDate: Date;

  @ApiPropertyOptional({
    description: '實際完成時間',
    example: '2024-06-30T15:30:00Z',
  })
  completedAt?: Date;

  @ApiProperty({
    description: '里程碑狀態',
    enum: MilestoneStatus,
    example: MilestoneStatus.IN_PROGRESS,
  })
  status: MilestoneStatus;

  @ApiProperty({
    description: '優先級',
    example: 'high',
  })
  priority: string;

  @ApiProperty({
    description: '關聯的項目 ID',
    example: 'clxxxxx',
  })
  projectId: string;

  @ApiProperty({
    description: '租戶 ID',
    example: 'clxxxxx',
  })
  tenantId: string;

  @ApiProperty({
    description: '創建者 ID',
    example: 'clxxxxx',
  })
  createdById: string;

  @ApiProperty({
    description: '創建時間',
    example: '2024-06-07T10:30:00Z',
  })
  created_at: Date;

  @ApiProperty({
    description: '更新時間',
    example: '2024-06-07T10:30:00Z',
  })
  updated_at: Date;

  @ApiPropertyOptional({
    description: '關聯的項目信息',
    type: 'object',
    properties: {
      id: { type: 'string' },
      name: { type: 'string' },
      status: { type: 'string' },
    },
  })
  project?: {
    id: string;
    name: string;
    status: string;
  };
}
