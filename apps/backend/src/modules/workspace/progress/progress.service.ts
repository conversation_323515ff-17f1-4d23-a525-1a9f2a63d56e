import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import {
  CreateProgressEntryDto,
  UpdateProgressEntryDto,
  ProgressEntryResponseDto,
  CreateMilestoneDto,
  UpdateMilestoneDto,
  MilestoneResponseDto,
  ProgressReportResponseDto,
} from './dto';
import { Prisma, ProgressType, MilestoneStatus, ReportType } from '@prisma/client';

@Injectable()
export class ProgressService {
  constructor(private readonly prisma: PrismaService) {}

  // ==================== 進度條目管理 ====================

  async createProgressEntry(
    createProgressEntryDto: CreateProgressEntryDto,
    userId: string,
    tenantId: string,
  ): Promise<ProgressEntryResponseDto> {
    // 驗證項目和任務是否存在且屬於該租戶
    if (createProgressEntryDto.projectId) {
      const project = await this.prisma.projects.findFirst({
        where: { id: createProgressEntryDto.projectId, tenant_id: tenantId },
      });
      if (!project) {
        throw new NotFoundException('項目不存在');
      }
    }

    if (createProgressEntryDto.taskId) {
      const task = await this.prisma.tasks.findFirst({
        where: { id: createProgressEntryDto.taskId, tenant_id: tenantId },
      });
      if (!task) {
        throw new NotFoundException('任務不存在');
      }
    }

    const progressEntry = await this.prisma.progress_entries.create({
      data: {
        title: createProgressEntryDto.title,
        description: createProgressEntryDto.description,
        progress_type: createProgressEntryDto.progress_type,
        progress_value: createProgressEntryDto.progress_value,
        status: createProgressEntryDto.status,
        notes: createProgressEntryDto.notes,
        photo_urls: createProgressEntryDto.photoUrls || [],
        metadata: createProgressEntryDto.metadata,
        project_id: createProgressEntryDto.projectId,
        task_id: createProgressEntryDto.taskId,
        user_id: userId,
        tenant_id: tenantId,
        recorded_at: createProgressEntryDto.recorded_at
          ? new Date(createProgressEntryDto.recorded_at)
          : new Date(),
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    return this.mapProgressEntryToResponseDto(progressEntry);
  }

  async findAllProgressEntries(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    projectId?: string,
    taskId?: string,
    progress_type?: ProgressType,
    startDate?: string,
    endDate?: string,
  ): Promise<{
    entries: ProgressEntryResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const where: Prisma.progress_entriesWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
      ...(taskId && { task_id: taskId }),
      ...(progress_type && { progress_type }),
      ...(startDate &&
        endDate && {
          recorded_at: {
            gte: new Date(startDate),
            lte: new Date(endDate),
          },
        }),
    };

    const [entries, total] = await Promise.all([
      this.prisma.progress_entries.findMany({
        where,
        skip,
        take: limit,
        orderBy: { recorded_at: 'desc' },
        include: {
          project: {
            select: { id: true, name: true, status: true },
          },
          task: {
            select: { id: true, title: true, status: true },
          },
        },
      }),
      this.prisma.progress_entries.count({ where }),
    ]);

    return {
      entries: entries.map((entry) => this.mapProgressEntryToResponseDto(entry)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOneProgressEntry(id: string, tenantId: string): Promise<ProgressEntryResponseDto> {
    const entry = await this.prisma.progress_entries.findFirst({
      where: { id, tenant_id: tenantId },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    if (!entry) {
      throw new NotFoundException('進度條目不存在');
    }

    return this.mapProgressEntryToResponseDto(entry);
  }

  async updateProgressEntry(
    id: string,
    updateProgressEntryDto: UpdateProgressEntryDto,
    tenantId: string,
  ): Promise<ProgressEntryResponseDto> {
    const existingEntry = await this.prisma.progress_entries.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!existingEntry) {
      throw new NotFoundException('進度條目不存在');
    }

    // 驗證項目和任務
    if (updateProgressEntryDto.projectId) {
      const project = await this.prisma.projects.findFirst({
        where: { id: updateProgressEntryDto.projectId, tenant_id: tenantId },
      });
      if (!project) {
        throw new NotFoundException('項目不存在');
      }
    }

    if (updateProgressEntryDto.taskId) {
      const task = await this.prisma.tasks.findFirst({
        where: { id: updateProgressEntryDto.taskId, tenant_id: tenantId },
      });
      if (!task) {
        throw new NotFoundException('任務不存在');
      }
    }

    const updatedEntry = await this.prisma.progress_entries.update({
      where: { id },
      data: {
        title: updateProgressEntryDto.title,
        description: updateProgressEntryDto.description,
        progress_type: updateProgressEntryDto.progress_type,
        progress_value: updateProgressEntryDto.progress_value,
        status: updateProgressEntryDto.status,
        notes: updateProgressEntryDto.notes,
        photo_urls: updateProgressEntryDto.photoUrls,
        metadata: updateProgressEntryDto.metadata,
        project_id: updateProgressEntryDto.projectId,
        task_id: updateProgressEntryDto.taskId,
        recorded_at: updateProgressEntryDto.recorded_at
          ? new Date(updateProgressEntryDto.recorded_at)
          : undefined,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
        task: {
          select: { id: true, title: true, status: true },
        },
      },
    });

    return this.mapProgressEntryToResponseDto(updatedEntry);
  }

  async removeProgressEntry(id: string, tenantId: string): Promise<void> {
    const entry = await this.prisma.progress_entries.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!entry) {
      throw new NotFoundException('進度條目不存在');
    }

    await this.prisma.progress_entries.delete({
      where: { id },
    });
  }

  // ==================== 里程碑管理 ====================

  async createMilestone(
    createMilestoneDto: CreateMilestoneDto,
    userId: string,
    tenantId: string,
  ): Promise<MilestoneResponseDto> {
    // 驗證項目是否存在
    const project = await this.prisma.projects.findFirst({
      where: { id: createMilestoneDto.projectId, tenant_id: tenantId },
    });

    if (!project) {
      throw new NotFoundException('項目不存在');
    }

    const milestone = await this.prisma.project_milestones.create({
      data: {
        title: createMilestoneDto.title,
        description: createMilestoneDto.description,
        target_date: new Date(createMilestoneDto.targetDate),
        priority: createMilestoneDto.priority || 'medium',
        project_id: createMilestoneDto.projectId,
        tenant_id: tenantId,
        created_by_id: userId,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapMilestoneToResponseDto(milestone);
  }

  async findAllMilestones(
    tenantId: string,
    projectId?: string,
    status?: MilestoneStatus,
  ): Promise<MilestoneResponseDto[]> {
    const where: Prisma.project_milestonesWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
      ...(status && { status }),
    };

    const milestones = await this.prisma.project_milestones.findMany({
      where,
      orderBy: { target_date: 'asc' },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return milestones.map((milestone) => this.mapMilestoneToResponseDto(milestone));
  }

  async updateMilestone(
    id: string,
    updateMilestoneDto: UpdateMilestoneDto,
    tenantId: string,
  ): Promise<MilestoneResponseDto> {
    const existingMilestone = await this.prisma.project_milestones.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!existingMilestone) {
      throw new NotFoundException('里程碑不存在');
    }

    const updatedMilestone = await this.prisma.project_milestones.update({
      where: { id },
      data: {
        title: updateMilestoneDto.title,
        description: updateMilestoneDto.description,
        target_date: updateMilestoneDto.targetDate
          ? new Date(updateMilestoneDto.targetDate)
          : undefined,
        status: updateMilestoneDto.status,
        priority: updateMilestoneDto.priority,
        completed_at: updateMilestoneDto.completedAt
          ? new Date(updateMilestoneDto.completedAt)
          : undefined,
      },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapMilestoneToResponseDto(updatedMilestone);
  }

  // ==================== 進度報告 ====================

  async generateProgressReport(
    tenantId: string,
    projectId: string | null,
    reportType: ReportType,
    userId: string,
  ): Promise<ProgressReportResponseDto> {
    // 計算報告期間
    const now = new Date();
    const period = this.calculateReportPeriod(now, reportType);

    // 獲取任務統計
    const taskStats = await this.getTaskStatistics(tenantId, projectId);

    // 生成報告標題
    const title = this.generateReportTitle(reportType, period, projectId);

    // 創建或更新報告
    const report = await this.prisma.progress_reports.upsert({
      where: {
        project_id_report_type_period: {
          project_id: projectId || '',
          report_type: reportType,
          period,
        },
      },
      update: {
        total_tasks: taskStats.totalTasks,
        completed_tasks: taskStats.completedTasks,
        in_progress_tasks: taskStats.inProgressTasks,
        overdue_tasks: taskStats.overdueTasks,
        completion_rate: taskStats.completionRate,
        generated_by: userId,
        report_date: now,
      },
      create: {
        title,
        report_type: reportType,
        period,
        total_tasks: taskStats.totalTasks,
        completed_tasks: taskStats.completedTasks,
        in_progress_tasks: taskStats.inProgressTasks,
        overdue_tasks: taskStats.overdueTasks,
        completion_rate: taskStats.completionRate,
        project_id: projectId,
        tenant_id: tenantId,
        generated_by: userId,
        report_date: now,
      },
      include: {
        project: projectId
          ? {
              select: { id: true, name: true, status: true },
            }
          : undefined,
      },
    });

    return this.mapReportToResponseDto(report);
  }

  // ==================== 私有方法 ====================

  private mapProgressEntryToResponseDto(entry: any): ProgressEntryResponseDto {
    return {
      id: entry.id,
      title: entry.title,
      description: entry.description,
      progress_type: entry.progress_type,
      progress_value: entry.progress_value,
      status: entry.status,
      notes: entry.notes,
      photoUrls: entry.photo_urls,
      projectId: entry.project_id,
      taskId: entry.task_id,
      userId: entry.user_id,
      tenantId: entry.tenant_id,
      recorded_at: entry.recorded_at,
      created_at: entry.created_at,
      updated_at: entry.updated_at,
      metadata: entry.metadata,
      project: entry.project,
      task: entry.task,
    };
  }

  private mapMilestoneToResponseDto(milestone: any): MilestoneResponseDto {
    return {
      id: milestone.id,
      title: milestone.title,
      description: milestone.description,
      targetDate: milestone.target_date,
      completedAt: milestone.completed_at,
      status: milestone.status,
      priority: milestone.priority,
      projectId: milestone.project_id,
      tenantId: milestone.tenant_id,
      createdById: milestone.created_by_id,
      created_at: milestone.created_at,
      updated_at: milestone.updated_at,
      project: milestone.project,
    };
  }

  private mapReportToResponseDto(report: any): ProgressReportResponseDto {
    return {
      id: report.id,
      title: report.title,
      reportType: report.report_type,
      period: report.period,
      totalTasks: report.total_tasks,
      completedTasks: report.completed_tasks,
      inProgressTasks: report.in_progress_tasks,
      overdueTasks: report.overdue_tasks,
      completionRate: report.completion_rate,
      predictedCompletionDate: report.predicted_completion_date,
      riskLevel: report.risk_level,
      recommendations: report.recommendations,
      projectId: report.project_id,
      tenantId: report.tenant_id,
      generatedBy: report.generated_by,
      reportDate: report.report_date,
      created_at: report.created_at,
      updated_at: report.updated_at,
      project: report.project,
    };
  }

  private async getTaskStatistics(tenantId: string, projectId: string | null) {
    const where: Prisma.tasksWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
    };

    const [totalTasks, tasksByStatus] = await Promise.all([
      this.prisma.tasks.count({ where }),
      this.prisma.tasks.groupBy({
        by: ['status'],
        where,
        _count: {
          status: true,
        },
      }),
    ]);

    // 為 milestones 創建正確的 where 條件
    const milestoneWhere: Prisma.project_milestonesWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
    };

    const [totalMilestones, milestonesByStatus] = await Promise.all([
      this.prisma.project_milestones.count({ where: milestoneWhere }),
      this.prisma.project_milestones.groupBy({
        by: ['status'],
        where: milestoneWhere,
        _count: {
          status: true,
        },
      }),
    ]);

    const overdueTasks = await this.prisma.tasks.count({
      where: {
        ...where,
        due_date: { lt: new Date() },
        status: { notIn: ['completed', 'done', 'cancelled'] },
      },
    });

    // 為 progress_entries 創建正確的 where 條件
    const progressWhere: Prisma.progress_entriesWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
    };

    const progressValues = await this.prisma.progress_entries.findMany({
      where: progressWhere,
      select: { progress_value: true },
    });

    const recentActivities = await this.prisma.progress_entries.findMany({
      where: progressWhere,
      take: 10,
      orderBy: { recorded_at: 'desc' },
      include: {
        project: { select: { name: true } },
        task: { select: { title: true } },
      },
    });

    const taskStats = tasksByStatus.reduce(
      (acc, stat) => {
        if (stat.status) {
          acc[stat.status] = stat._count?.status ?? 0;
        }
        return acc;
      },
      {} as Record<string, number>,
    );

    const completedTasks = taskStats['completed'] || taskStats['done'] || 0;
    const inProgressTasks = taskStats['in-progress'] || 0;
    const pendingTasks = taskStats['pending'] || taskStats['todo'] || 0;

    const completionRate = totalTasks > 0 ? (completedTasks / totalTasks) * 100 : 0;

    const formattedActivities = recentActivities.map((entry) => ({
      id: entry.id,
      title: entry.title,
      type: entry.progress_type,
      date: entry.recorded_at,
      projectName: entry.project?.name,
      taskTitle: entry.task?.title,
    }));

    return {
      totalTasks,
      completedTasks,
      inProgressTasks,
      pendingTasks,
      overdueTasks,
      completionRate: Math.round(completionRate * 100) / 100,
    };
  }

  private calculateReportPeriod(date: Date, reportType: ReportType): string {
    const year = date.getFullYear();

    switch (reportType) {
      case ReportType.DAILY:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
      case ReportType.WEEKLY:
        const weekNumber = this.getWeekNumber(date);
        return `${year}-W${String(weekNumber).padStart(2, '0')}`;
      case ReportType.MONTHLY:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}`;
      case ReportType.QUARTERLY:
        const quarter = Math.floor(date.getMonth() / 3) + 1;
        return `${year}-Q${quarter}`;
      default:
        return `${year}-${String(date.getMonth() + 1).padStart(2, '0')}`;
    }
  }

  private getWeekNumber(date: Date): number {
    const firstDayOfYear = new Date(date.getFullYear(), 0, 1);
    const pastDaysOfYear = (date.getTime() - firstDayOfYear.getTime()) / 86400000;
    return Math.ceil((pastDaysOfYear + firstDayOfYear.getDay() + 1) / 7);
  }

  private generateReportTitle(
    reportType: ReportType,
    period: string,
    projectId: string | null,
  ): string {
    const typeMap = {
      [ReportType.DAILY]: '日報',
      [ReportType.WEEKLY]: '週報',
      [ReportType.MONTHLY]: '月報',
      [ReportType.QUARTERLY]: '季報',
      [ReportType.PROJECT]: '項目報告',
      [ReportType.CUSTOM]: '自定義報告',
    };

    const prefix = projectId ? '項目' : '整體';
    return `${prefix}${typeMap[reportType]} - ${period}`;
  }

  async getProgressOverview(
    tenantId: string,
    projectId?: string,
  ): Promise<{
    summary: {
      totalProjects: number;
      totalTasks: number;
      totalProgressEntries: number;
      totalMilestones: number;
    };
    progress: {
      averageProjectProgress: number | null;
      completedTasks: number;
      inProgressTasks: number;
      pendingTasks: number;
      overdueTasks: number;
    };
    milestones: {
      completed: number;
      pending: number;
      overdue: number;
    };
    recentActivity: Array<{
      id: string;
      title: string;
      type: string;
      date: Date;
      projectName?: string;
      taskTitle?: string;
    }>;
  }> {
    const where = projectId
      ? { project_id: projectId, tenant_id: tenantId }
      : { tenant_id: tenantId };
    const projectWhere = projectId
      ? { id: projectId, tenant_id: tenantId }
      : { tenant_id: tenantId };

    // 基本統計
    const [
      totalProjects,
      totalTasks,
      totalProgressEntries,
      totalMilestones,
      tasksByStatus,
      milestonesByStatus,
      recentProgressEntries,
      progressValues,
    ] = await Promise.all([
      this.prisma.projects.count({ where: projectWhere }),
      this.prisma.tasks.count({ where }),
      this.prisma.progress_entries.count({ where }),
      this.prisma.project_milestones.count({ where }),
      this.prisma.tasks.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.project_milestones.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.progress_entries.findMany({
        where,
        select: {
          id: true,
          title: true,
          progress_type: true,
          recorded_at: true,
          project: {
            select: { name: true },
          },
          task: {
            select: { title: true },
          },
        },
        orderBy: { recorded_at: 'desc' },
        take: 10,
      }),
      this.prisma.progress_entries.findMany({
        where: {
          ...where,
          progress_value: { not: null },
        },
        select: { progress_value: true },
      }),
    ]);

    // 處理任務統計
    const taskStats = tasksByStatus.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      },
      {} as Record<string, number>,
    );

    const completedTasks = taskStats['completed'] || taskStats['done'] || 0;
    const inProgressTasks = taskStats['in-progress'] || 0;
    const pendingTasks = taskStats['pending'] || taskStats['todo'] || 0;

    // 計算逾期任務
    const overdueTasks = await this.prisma.tasks.count({
      where: {
        ...where,
        due_date: { lt: new Date() },
        status: { notIn: ['completed', 'done', 'cancelled'] },
      },
    });

    // 處理里程碑統計
    const milestoneStats = milestonesByStatus.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count.status;
        return acc;
      },
      {} as Record<string, number>,
    );

    const completedMilestones = milestoneStats['COMPLETED'] || 0;
    const pendingMilestones = milestoneStats['PENDING'] || 0;
    const overdueMilestones = milestoneStats['OVERDUE'] || 0;

    // 計算平均進度
    const validProgressValues = progressValues
      .map((entry) => entry.progress_value)
      .filter((value) => value !== null) as number[];

    const averageProjectProgress =
      validProgressValues.length > 0
        ? validProgressValues.reduce((sum, val) => sum + val, 0) / validProgressValues.length
        : null;

    // 處理最近活動
    const recentActivity = recentProgressEntries.map((entry) => ({
      id: entry.id,
      title: entry.title,
      type: entry.progress_type,
      date: entry.recorded_at,
      projectName: entry.project?.name,
      taskTitle: entry.task?.title,
    }));

    return {
      summary: {
        totalProjects,
        totalTasks,
        totalProgressEntries,
        totalMilestones,
      },
      progress: {
        averageProjectProgress: averageProjectProgress
          ? Math.round(averageProjectProgress * 100) / 100
          : null,
        completedTasks,
        inProgressTasks,
        pendingTasks,
        overdueTasks,
      },
      milestones: {
        completed: completedMilestones,
        pending: pendingMilestones,
        overdue: overdueMilestones,
      },
      recentActivity,
    };
  }
}
