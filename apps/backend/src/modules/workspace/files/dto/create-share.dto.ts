import { IsEnum, IsOptional, IsBoolean, IsString, IsN<PERSON>ber, IsDateString } from 'class-validator';
import { ShareType } from '@prisma/client';

export class CreateShareDto {
  @IsEnum(ShareType)
  @IsOptional()
  shareType?: ShareType = ShareType.LINK;

  @IsBoolean()
  @IsOptional()
  allowDownload?: boolean = true;

  @IsBoolean()
  @IsOptional()
  allowComment?: boolean = false;

  @IsBoolean()
  @IsOptional()
  requireAuth?: boolean = false;

  @IsString()
  @IsOptional()
  password?: string;

  @IsNumber()
  @IsOptional()
  maxDownloads?: number;

  @IsDateString()
  @IsOptional()
  expiresAt?: string;
}
