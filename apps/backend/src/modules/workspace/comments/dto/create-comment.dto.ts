import { IsString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>UUI<PERSON> } from 'class-validator';
import { CommentContentType, CommentEntityType } from '@prisma/client';

export class CreateCommentDto {
  @IsString()
  content: string;

  @IsEnum(CommentContentType)
  @IsOptional()
  contentType?: CommentContentType = CommentContentType.TEXT;

  @IsEnum(CommentEntityType)
  entityType: CommentEntityType;

  @IsUUID()
  entityId: string;

  @IsUUID()
  @IsOptional()
  parentId?: string;

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  mentions?: string[];
}
