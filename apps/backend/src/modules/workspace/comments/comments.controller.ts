import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { CommentsService } from './comments.service';
import { CreateCommentDto, UpdateCommentDto, AddCommentReactionDto } from './dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { CommentEntityType } from '@prisma/client';

@Controller('comments')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class CommentsController {
  constructor(private readonly commentsService: CommentsService) {}

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.COMMENT))
  async createComment(@Body() createCommentDto: CreateCommentDto, @Request() req: any) {
    const { userId, userType, tenantId, workspaceId } = req.user;

    return this.commentsService.createComment(
      createCommentDto,
      userId,
      userType,
      tenantId,
      workspaceId,
    );
  }

  @Get('entity/:entityType/:entityId')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.COMMENT))
  async getCommentsByEntity(
    @Param('entityType') entityType: CommentEntityType,
    @Param('entityId') entityId: string,
    @Request() req: any,
    @Query('page') page?: string,
    @Query('limit') limit?: string,
  ) {
    const { tenantId, workspaceId } = req.user;

    return this.commentsService.getCommentsByEntity(
      entityType,
      entityId,
      tenantId,
      workspaceId,
      page ? parseInt(page) : 1,
      limit ? parseInt(limit) : 20,
    );
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.COMMENT))
  async getCommentById(@Param('id') id: string, @Request() req: any) {
    const { tenantId, workspaceId } = req.user;

    return this.commentsService.getCommentById(id, tenantId, workspaceId);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.COMMENT))
  async updateComment(
    @Param('id') id: string,
    @Body() updateCommentDto: UpdateCommentDto,
    @Request() req: any,
  ) {
    const { userId, userType, tenantId, workspaceId } = req.user;

    return this.commentsService.updateComment(
      id,
      updateCommentDto,
      userId,
      userType,
      tenantId,
      workspaceId,
    );
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.COMMENT))
  async deleteComment(@Param('id') id: string, @Request() req: any) {
    const { userId, userType, tenantId, workspaceId } = req.user;

    return this.commentsService.deleteComment(id, userId, userType, tenantId, workspaceId);
  }

  @Post(':id/reactions')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.COMMENT_REACTION))
  async addReaction(
    @Param('id') id: string,
    @Body() addReactionDto: AddCommentReactionDto,
    @Request() req: any,
  ) {
    const { userId, userType, tenantId, workspaceId } = req.user;

    return this.commentsService.addReaction(
      id,
      addReactionDto,
      userId,
      userType,
      tenantId,
      workspaceId,
    );
  }

  @Delete(':id/reactions')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.COMMENT_REACTION))
  async removeReaction(@Param('id') id: string, @Request() req: any) {
    const { userId, userType, tenantId, workspaceId } = req.user;

    return this.commentsService.removeReaction(id, userId, userType, tenantId, workspaceId);
  }
}
