import {
  Injectable,
  NotFoundException,
  ForbiddenException,
  BadRequestException,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateTaskDto, UpdateTaskDto, TaskResponseDto } from './dto';
import { Prisma } from '@prisma/client';

@Injectable()
export class TasksService {
  constructor(private readonly prisma: PrismaService) {}

  async create(
    createTaskDto: CreateTaskDto,
    userId: string,
    tenantId: string,
  ): Promise<TaskResponseDto> {
    try {
      // 驗證專案是否存在且屬於該租戶
      const project = await this.prisma.projects.findFirst({
        where: { id: createTaskDto.projectId, tenant_id: tenantId },
      });

      if (!project) {
        throw new NotFoundException('專案不存在');
      }

      // 如果指定了 assigneeId，驗證用戶是否存在且屬於該租戶
      if (createTaskDto.assigneeId) {
        const assignee = await this.prisma.tenant_users.findFirst({
          where: { id: createTaskDto.assigneeId, tenant_id: tenantId },
        });

        if (!assignee) {
          throw new NotFoundException('指派的用戶不存在');
        }
      }

      const task = await this.prisma.tasks.create({
        data: {
          title: createTaskDto.title,
          description: createTaskDto.description,
          project_id: createTaskDto.projectId,
          status: createTaskDto.status || 'todo',
          priority: createTaskDto.priority || 'medium',
          assignee_id: createTaskDto.assigneeId,
          due_date: createTaskDto.dueDate ? new Date(createTaskDto.dueDate) : null,
          estimated_hours: createTaskDto.estimatedHours,
          created_by_id: userId,
          tenant_id: tenantId,
        },
        include: {
          project: {
            select: { id: true, name: true, status: true },
          },
        },
      });

      return this.mapToResponseDto(task);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new BadRequestException('任務標題已存在');
        }
      }
      throw error;
    }
  }

  async findAll(
    tenantId: string,
    page: number = 1,
    limit: number = 10,
    search?: string,
    status?: string,
    priority?: string,
    projectId?: string,
    assigneeId?: string,
  ): Promise<{
    tasks: TaskResponseDto[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    const skip = (page - 1) * limit;

    const where: Prisma.tasksWhereInput = {
      tenant_id: tenantId,
      ...(search && {
        OR: [
          { title: { contains: search, mode: 'insensitive' } },
          { description: { contains: search, mode: 'insensitive' } },
        ],
      }),
      ...(status && { status }),
      ...(priority && { priority }),
      ...(projectId && { project_id: projectId }),
      ...(assigneeId && { assignee_id: assigneeId }),
    };

    const [tasks, total] = await Promise.all([
      this.prisma.tasks.findMany({
        where,
        skip,
        take: limit,
        orderBy: { created_at: 'desc' },
        include: {
          project: {
            select: { id: true, name: true, status: true },
          },
        },
      }),
      this.prisma.tasks.count({ where }),
    ]);

    return {
      tasks: tasks.map((task) => this.mapToResponseDto(task)),
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(id: string, tenantId: string): Promise<TaskResponseDto> {
    const task = await this.prisma.tasks.findFirst({
      where: { id, tenant_id: tenantId },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },

        progress_entries: {
          orderBy: {
            created_at: 'desc',
          },
          take: 10, // 最近 10 個進度記錄
        },
        _count: {
          select: {
            progress_entries: true,
          },
        },
      },
    });

    if (!task) {
      throw new NotFoundException('任務不存在');
    }

    return this.mapToResponseDto(task);
  }

  async update(
    id: string,
    updateTaskDto: UpdateTaskDto,
    userId: string,
    tenantId: string,
  ): Promise<TaskResponseDto> {
    // 檢查任務是否存在且屬於該租戶
    const existingTask = await this.prisma.tasks.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!existingTask) {
      throw new NotFoundException('任務不存在');
    }

    // 驗證項目是否存在
    if (updateTaskDto.projectId) {
      const project = await this.prisma.projects.findFirst({
        where: { id: updateTaskDto.projectId, tenant_id: tenantId },
      });

      if (!project) {
        throw new NotFoundException(`Project with ID ${updateTaskDto.projectId} not found`);
      }
    }

    // 驗證指派對象是否存在
    if (updateTaskDto.assigneeId) {
      const assignee = await this.prisma.tenant_users.findFirst({
        where: { id: updateTaskDto.assigneeId, tenant_id: tenantId },
      });

      if (!assignee) {
        throw new NotFoundException('指派的用戶不存在');
      }
    }

    try {
      const task = await this.prisma.tasks.update({
        where: { id },
        data: {
          ...(updateTaskDto.title && { title: updateTaskDto.title }),
          ...(updateTaskDto.description && { description: updateTaskDto.description }),
          ...(updateTaskDto.projectId && { project_id: updateTaskDto.projectId }),
          ...(updateTaskDto.status && { status: updateTaskDto.status }),
          ...(updateTaskDto.priority && { priority: updateTaskDto.priority }),
          ...(updateTaskDto.assigneeId && { assignee_id: updateTaskDto.assigneeId }),
          ...(updateTaskDto.estimatedHours && { estimated_hours: updateTaskDto.estimatedHours }),
          ...(updateTaskDto.actualHours && { actual_hours: updateTaskDto.actualHours }),
          due_date: updateTaskDto.dueDate ? new Date(updateTaskDto.dueDate) : undefined,
          updated_at: new Date(),
        },
        include: {
          project: {
            select: { id: true, name: true, status: true },
          },
        },
      });

      return this.mapToResponseDto(task);
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError) {
        if (error.code === 'P2002') {
          throw new BadRequestException('任務標題已存在');
        }
      }
      throw error;
    }
  }

  async remove(id: string, tenantId: string): Promise<void> {
    const task = await this.prisma.tasks.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!task) {
      throw new NotFoundException(`Task with ID ${id} not found.`);
    }

    await this.prisma.tasks.delete({ where: { id } });
  }

  async getTaskStats(
    tenantId: string,
    projectId?: string,
  ): Promise<{
    total: number;
    byStatus: Record<string, number>;
    byPriority: Record<string, number>;
    overdueTasks: number;
  }> {
    const where: Prisma.tasksWhereInput = {
      tenant_id: tenantId,
      ...(projectId && { project_id: projectId }),
    };

    const [total, statusStats, priorityStats, overdueTasks] = await Promise.all([
      this.prisma.tasks.count({ where }),
      this.prisma.tasks.groupBy({
        by: ['status'],
        where,
        _count: { status: true },
      }),
      this.prisma.tasks.groupBy({
        by: ['priority'],
        where,
        _count: { priority: true },
      }),
      this.prisma.tasks.count({
        where: {
          ...where,
          due_date: { lt: new Date() },
          status: { notIn: ['done', 'cancelled'] },
        },
      }),
    ]);

    const byStatus = statusStats.reduce(
      (acc, stat) => {
        acc[stat.status] = stat._count.status || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    const byPriority = priorityStats.reduce(
      (acc, stat) => {
        acc[stat.priority] = stat._count.priority || 0;
        return acc;
      },
      {} as Record<string, number>,
    );

    return { total, byStatus, byPriority, overdueTasks };
  }

  async assignTask(taskId: string, assigneeId: string, tenantId: string): Promise<TaskResponseDto> {
    // 驗證任務是否存在
    const task = await this.prisma.tasks.findFirst({
      where: { id: taskId, tenant_id: tenantId },
    });

    if (!task) {
      throw new NotFoundException('任務不存在');
    }

    // 驗證用戶是否存在且屬於該租戶
    const assignee = await this.prisma.tenant_users.findFirst({
      where: { id: assigneeId, tenant_id: tenantId },
    });

    if (!assignee) {
      throw new NotFoundException('指派的用戶不存在');
    }

    const updatedTask = await this.prisma.tasks.update({
      where: { id: taskId },
      data: { assignee_id: assigneeId, updated_at: new Date() },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapToResponseDto(updatedTask);
  }

  async updateTaskStatus(
    taskId: string,
    status: string,
    tenantId: string,
  ): Promise<TaskResponseDto> {
    const task = await this.prisma.tasks.findFirst({
      where: { id: taskId, tenant_id: tenantId },
    });

    if (!task) {
      throw new NotFoundException('任務不存在');
    }

    const updatedTask = await this.prisma.tasks.update({
      where: { id: taskId },
      data: { status, updated_at: new Date() },
      include: {
        project: {
          select: { id: true, name: true, status: true },
        },
      },
    });

    return this.mapToResponseDto(updatedTask);
  }

  async getTaskAnalytics(tenantId: string, taskId: string) {
    // 獲取任務基本信息
    const task = await this.prisma.tasks.findFirst({
      where: { id: taskId, tenant_id: tenantId },
      select: {
        id: true,
        title: true,
        status: true,
        priority: true,
        estimated_hours: true,
        actual_hours: true,
      },
    });

    if (!task) {
      throw new NotFoundException('任務不存在');
    }

    // 獲取進度記錄
    const progressEntries = await this.prisma.progress_entries.findMany({
      where: { task_id: taskId, tenant_id: tenantId },
      select: {
        progress_value: true,
        progress_type: true,
        recorded_at: true,
      },
      orderBy: { recorded_at: 'desc' },
    });

    // 處理進度統計
    const validProgressValues = progressEntries
      .map((entry) => entry.progress_value)
      .filter((value) => value !== null && value !== undefined) as number[];

    const latestProgress = validProgressValues.length > 0 ? validProgressValues[0] : null;
    const averageProgress =
      validProgressValues.length > 0
        ? validProgressValues.reduce((a, b) => a + b, 0) / validProgressValues.length
        : null;

    // 進度歷史（按日期分組）
    const progressHistory = progressEntries.map((entry) => ({
      date: entry.recorded_at,
      value: entry.progress_value,
      type: entry.progress_type,
    }));

    // 時間追蹤統計
    const estimated = task.estimated_hours;
    const actual = task.actual_hours;
    let variance: number | null = null;
    let efficiency: number | null = null;

    if (estimated !== null && actual !== null) {
      variance = actual - estimated;
      efficiency = estimated > 0 ? (estimated / actual) * 100 : null;
    }

    return {
      task: {
        id: task.id,
        title: task.title,
        status: task.status,
        priority: task.priority,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
      },
      progress: {
        totalEntries: progressEntries.length,
        latestProgress,
        averageProgress: averageProgress ? Math.round(averageProgress * 100) / 100 : null,
        progressHistory,
      },
      timeTracking: {
        status: task.status,
        priority: task.priority,
        estimatedHours: task.estimated_hours,
        actualHours: task.actual_hours,
      },
    };
  }

  private mapToResponseDto(task: any): TaskResponseDto {
    return {
      id: task.id,
      title: task.title,
      description: task.description || undefined,
      projectId: task.project_id,
      status: task.status,
      priority: task.priority,
      assigneeId: task.assignee_id || undefined,
      dueDate: task.due_date || undefined,
      estimatedHours: task.estimated_hours || undefined,
      actualHours: task.actual_hours || undefined,
      tenantId: task.tenant_id,
      createdBy: task.created_by_id,
      created_at: task.created_at,
      updated_at: task.updated_at,
      project: task.project
        ? {
            id: task.project.id,
            name: task.project.name,
            status: task.project.status,
          }
        : undefined,
    };
  }
}
