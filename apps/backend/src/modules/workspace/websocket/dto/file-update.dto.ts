import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum FileUpdateType {
  UPLOADED = 'uploaded',
  UPDATED = 'updated',
  DELETED = 'deleted',
  SHARED = 'shared',
  PERMISSION_CHANGED = 'permission_changed',
}

export class FileUpdateDto {
  @IsString()
  @IsNotEmpty()
  file_id: string;

  @IsEnum(FileUpdateType)
  update_type: FileUpdateType;

  @IsString()
  @IsNotEmpty()
  entity_type: string;

  @IsString()
  @IsNotEmpty()
  entity_id: string;

  @IsString()
  @IsOptional()
  file_name?: string;

  @IsString()
  @IsOptional()
  description?: string;
}
