import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum TaskUpdateType {
  CREATED = 'created',
  UPDATED = 'updated',
  STATUS_CHANGED = 'status_changed',
  ASSIGNED = 'assigned',
  UNASSIGNED = 'unassigned',
  DELETED = 'deleted',
}

export class TaskUpdateDto {
  @IsString()
  @IsNotEmpty()
  task_id: string;

  @IsEnum(TaskUpdateType)
  update_type: TaskUpdateType;

  @IsString()
  @IsNotEmpty()
  project_id: string;

  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  assigned_to?: string;

  @IsString()
  @IsOptional()
  description?: string;
}
