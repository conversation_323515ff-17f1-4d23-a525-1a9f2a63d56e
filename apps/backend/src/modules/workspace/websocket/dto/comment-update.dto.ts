import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum CommentUpdateType {
  CREATED = 'created',
  UPDATED = 'updated',
  DELETED = 'deleted',
  REACTION_ADDED = 'reaction_added',
  REACTION_REMOVED = 'reaction_removed',
}

export class CommentUpdateDto {
  @IsString()
  @IsNotEmpty()
  comment_id: string;

  @IsEnum(CommentUpdateType)
  update_type: CommentUpdateType;

  @IsString()
  @IsNotEmpty()
  entity_type: string;

  @IsString()
  @IsNotEmpty()
  entity_id: string;

  @IsString()
  @IsOptional()
  content?: string;

  @IsString()
  @IsOptional()
  parent_comment_id?: string;
}
