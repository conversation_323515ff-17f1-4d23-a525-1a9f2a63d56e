export * from './join-room.dto';
export * from './leave-room.dto';
export * from './send-message.dto';
export * from './file-update.dto';
export * from './comment-update.dto';
export * from './task-update.dto';
export * from './project-update.dto';

// 聊天相關事件DTO
export interface ChatJoinConversationDto {
  conversationId: string;
}

export interface ChatLeaveConversationDto {
  conversation_id: string;
}

export interface ChatSendMessageDto {
  conversation_id: string;
  content: string;
  type?: string;
  reply_to_id?: string;
}

export interface ChatTypingDto {
  conversation_id: string;
}

export interface ChatMessageReactionDto {
  message_id: string;
  emoji: string;
}

// 訊息中心相關事件DTO
export interface MessageCenterSendMessageDto {
  conversationId: string;
  content: string;
  contentType?: string;
  replyToMessageId?: string;
}

export interface MessageCenterJoinConversationDto {
  conversation_id: string;
}

export interface MessageCenterNotificationDto {
  recipient_id: string;
  type: string;
  title: string;
  content: string;
  workspace_id?: string;
}

// 通用事件響應DTO
export interface WebSocketEventResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  timestamp: Date;
}

export interface UserOnlineStatusDto {
  user_id: string;
  username: string;
  is_online: boolean;
  timestamp: Date;
}

export interface RoomUserJoinedDto {
  user_id: string;
  username: string;
  room_type: string;
  room_id: string;
  timestamp: Date;
}

export interface RoomUserLeftDto {
  user_id: string;
  username: string;
  room_type: string;
  room_id: string;
  timestamp: Date;
}
