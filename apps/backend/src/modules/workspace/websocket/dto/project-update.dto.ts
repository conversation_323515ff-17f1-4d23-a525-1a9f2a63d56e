import { IsString, <PERSON>NotEmpty, <PERSON><PERSON><PERSON>, IsOptional } from 'class-validator';

export enum ProjectUpdateType {
  CREATED = 'created',
  UPDATED = 'updated',
  STATUS_CHANGED = 'status_changed',
  MEMBER_ADDED = 'member_added',
  MEMBER_REMOVED = 'member_removed',
  DELETED = 'deleted',
}

export class ProjectUpdateDto {
  @IsString()
  @IsNotEmpty()
  project_id: string;

  @IsEnum(ProjectUpdateType)
  update_type: ProjectUpdateType;

  @IsString()
  @IsNotEmpty()
  workspace_id: string;

  @IsString()
  @IsOptional()
  title?: string;

  @IsString()
  @IsOptional()
  status?: string;

  @IsString()
  @IsOptional()
  description?: string;

  @IsString()
  @IsOptional()
  member_id?: string;
}
