import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
  ConnectedSocket,
  MessageBody,
  OnGatewayInit,
} from '@nestjs/websockets';
import { Server, Socket } from 'socket.io';
import { Logger, UseGuards } from '@nestjs/common';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { WebSocketService } from './websocket.service';
import {
  JoinRoomDto,
  LeaveRoomDto,
  SendMessageDto,
  FileUpdateDto,
  CommentUpdateDto,
  TaskUpdateDto,
  ProjectUpdateDto,
} from './dto';

@WebSocketGateway({
  namespace: '/',
  cors: {
    origin: [
      process.env.FRONTEND_URL || 'http://localhost:5173',
      'http://localhost:5173',
      'http://localhost:4000',
    ],
    credentials: true,
    methods: ['GET', 'POST'],
    allowedHeaders: ['Content-Type', 'Authorization', 'Cookie'],
  },
  transports: ['websocket', 'polling'],
  allowEIO3: true,
  pingTimeout: 60000,
  pingInterval: 25000,
  cookie: {
    name: 'io',
    httpOnly: false,
    secure: false, // 開發環境設為 false
    sameSite: 'lax',
    path: '/',
  },
})
export class WorkspaceWebSocketGateway
  implements OnGatewayInit, OnGatewayConnection, OnGatewayDisconnect
{
  @WebSocketServer()
  server: Server;

  private readonly logger = new Logger(WorkspaceWebSocketGateway.name);

  constructor(private readonly websocketService: WebSocketService) {
    this.logger.log('🏗️ WorkspaceWebSocketGateway constructor called');
  }

  afterInit(server: Server) {
    this.logger.log('🚀 WebSocket Gateway initialized');
    this.logger.log(`🌐 WebSocket server listening on namespace: /`);
    this.logger.log(
      `🔧 CORS origins: ${JSON.stringify([
        process.env.FRONTEND_URL || 'http://localhost:5173',
        'http://localhost:5173',
        'http://localhost:4000',
      ])}`,
    );
    this.websocketService.setServer(server);
  }

  async handleConnection(client: Socket) {
    this.logger.log(
      `🔌 New WebSocket connection attempt from ${client.handshake.address} (Socket ID: ${client.id})`,
    );
    this.logger.debug(`🔍 Connection headers: ${JSON.stringify(client.handshake.headers)}`);
    this.logger.debug(`🔍 Connection query: ${JSON.stringify(client.handshake.query)}`);
    this.logger.debug(`🔍 Connection auth: ${JSON.stringify(client.handshake.auth)}`);

    try {
      const user = await this.websocketService.authenticateSocket(client);
      if (!user) {
        this.logger.warn(`❌ Authentication failed for socket ${client.id}, disconnecting`);
        client.disconnect();
        return;
      }

      this.logger.log(
        `✅ Client connected successfully: ${client.id} (User: ${user.id}, Type: ${user.user_type})`,
      );

      // 儲存用戶連線資訊
      await this.websocketService.addConnection(client.id, user);

      // 通知用戶上線
      client.broadcast.emit('user:online', {
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`💥 Connection error for socket ${client.id}: ${error.message}`);
      this.logger.error(`💥 Error stack: ${error.stack}`);
      client.disconnect();
    }
  }

  async handleDisconnect(client: Socket) {
    try {
      const user = await this.websocketService.removeConnection(client.id);

      if (user) {
        this.logger.log(`Client disconnected: ${client.id} (User: ${user.id})`);

        // 通知用戶離線
        client.broadcast.emit('user:offline', {
          userId: user.id,
          username: user.username,
          timestamp: new Date(),
        });
      }
    } catch (error) {
      this.logger.error(`Disconnect error: ${error.message}`);
    }
  }

  // 加入房間
  @SubscribeMessage('join:room')
  async handleJoinRoom(@ConnectedSocket() client: Socket, @MessageBody() data: JoinRoomDto) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 驗證房間權限
      const hasPermission = await this.websocketService.validateRoomAccess(
        user.id,
        data.room_type,
        data.room_id,
      );

      if (!hasPermission) {
        client.emit('error', { message: 'Access denied to room' });
        return;
      }

      const roomName = `${data.room_type}:${data.room_id}`;
      await client.join(roomName);

      this.logger.log(`User ${user.id} joined room: ${roomName}`);

      // 通知房間內其他用戶
      client.to(roomName).emit('room:user-joined', {
        userId: user.id,
        username: user.username,
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });

      client.emit('room:joined', {
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Join room error: ${error.message}`);
      client.emit('error', { message: 'Failed to join room' });
    }
  }

  // 離開房間
  @SubscribeMessage('leave:room')
  async handleLeaveRoom(@ConnectedSocket() client: Socket, @MessageBody() data: LeaveRoomDto) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `${data.room_type}:${data.room_id}`;
      await client.leave(roomName);

      this.logger.log(`User ${user.id} left room: ${roomName}`);

      // 通知房間內其他用戶
      client.to(roomName).emit('room:user-left', {
        userId: user.id,
        username: user.username,
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });

      client.emit('room:left', {
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Leave room error: ${error.message}`);
    }
  }

  // 測試訊息處理
  @SubscribeMessage('test:message')
  async handleTestMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { message: string; timestamp: string; clientId: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      this.logger.log(`📨 Test message from user ${user.id} (${user.username}): ${data.message}`);

      // 回應測試訊息
      client.emit('test:message:response', {
        message: `伺服器收到您的測試訊息: "${data.message}"`,
        originalMessage: data.message,
        timestamp: new Date().toISOString(),
        serverTime: new Date().toISOString(),
        userId: user.id,
        username: user.username,
      });

      // 發送回音測試
      setTimeout(() => {
        client.emit('test:echo', {
          message: `回音: ${data.message}`,
          delay: '1秒延遲',
          timestamp: new Date().toISOString(),
        });
      }, 1000);
    } catch (error) {
      this.logger.error(`Test message error: ${error.message}`);
      client.emit('error', { message: 'Failed to process test message' });
    }
  }

  // 發送即時訊息
  @SubscribeMessage('message:send')
  async handleSendMessage(@ConnectedSocket() client: Socket, @MessageBody() data: SendMessageDto) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 驗證房間權限
      const hasPermission = await this.websocketService.validateRoomAccess(
        user.id,
        data.room_type,
        data.room_id,
      );

      if (!hasPermission) {
        client.emit('error', { message: 'Access denied' });
        return;
      }

      const roomName = `${data.room_type}:${data.room_id}`;
      const messageData = {
        id: `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        content: data.content,
        userId: user.id,
        username: user.username,
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
        replyTo: data.reply_to,
      };

      // 廣播訊息到房間內所有用戶
      this.server.to(roomName).emit('message:received', messageData);

      this.logger.log(`Message sent in room ${roomName} by user ${user.id}`);
    } catch (error) {
      this.logger.error(`Send message error: ${error.message}`);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  // 檔案更新通知
  @SubscribeMessage('file:update')
  async handleFileUpdate(@ConnectedSocket() client: Socket, @MessageBody() data: FileUpdateDto) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `${data.entity_type}:${data.entity_id}`;

      // 廣播檔案更新事件
      client.to(roomName).emit('file:updated', {
        ...data,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
      });

      this.logger.log(`File update broadcasted in room ${roomName}`);
    } catch (error) {
      this.logger.error(`File update error: ${error.message}`);
    }
  }

  // 評論更新通知
  @SubscribeMessage('comment:update')
  async handleCommentUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: CommentUpdateDto,
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `${data.entity_type}:${data.entity_id}`;

      // 廣播評論更新事件
      client.to(roomName).emit('comment:updated', {
        ...data,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
      });

      this.logger.log(`Comment update broadcasted in room ${roomName}`);
    } catch (error) {
      this.logger.error(`Comment update error: ${error.message}`);
    }
  }

  // 任務更新通知
  @SubscribeMessage('task:update')
  async handleTaskUpdate(@ConnectedSocket() client: Socket, @MessageBody() data: TaskUpdateDto) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `project:${data.project_id}`;

      // 廣播任務更新事件
      client.to(roomName).emit('task:updated', {
        ...data,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
      });

      this.logger.log(`Task update broadcasted in room ${roomName}`);
    } catch (error) {
      this.logger.error(`Task update error: ${error.message}`);
    }
  }

  // 專案更新通知
  @SubscribeMessage('project:update')
  async handleProjectUpdate(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: ProjectUpdateDto,
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `workspace:${data.workspace_id}`;

      // 廣播專案更新事件
      client.to(roomName).emit('project:updated', {
        ...data,
        userId: user.id,
        username: user.username,
        timestamp: new Date(),
      });

      this.logger.log(`Project update broadcasted in room ${roomName}`);
    } catch (error) {
      this.logger.error(`Project update error: ${error.message}`);
    }
  }

  // 用戶正在輸入
  @SubscribeMessage('typing:start')
  async handleTypingStart(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room_type: string; room_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `${data.room_type}:${data.room_id}`;

      client.to(roomName).emit('typing:user-started', {
        userId: user.id,
        username: user.username,
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Typing start error: ${error.message}`);
    }
  }

  // 用戶停止輸入
  @SubscribeMessage('typing:stop')
  async handleTypingStop(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { room_type: string; room_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `${data.room_type}:${data.room_id}`;

      client.to(roomName).emit('typing:user-stopped', {
        userId: user.id,
        username: user.username,
        roomType: data.room_type,
        roomId: data.room_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Typing stop error: ${error.message}`);
    }
  }

  // 聊天相關事件處理 - 簡化版本，只處理 WebSocket 事件轉發
  @SubscribeMessage('chat:join-conversation')
  async handleJoinConversation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 簡化版本：直接加入房間，權限檢查由業務層處理
      const roomName = `conversation:${data.conversation_id}`;
      await client.join(roomName);

      this.logger.log(`User ${user.id} joined conversation: ${data.conversation_id}`);

      // 通知對話中的其他用戶
      client.to(roomName).emit('conversation:user-joined', {
        userId: user.id,
        username: user.username,
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });

      client.emit('conversation:joined', {
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Join conversation error: ${error.message}`);
      client.emit('error', { message: 'Failed to join conversation' });
    }
  }

  @SubscribeMessage('chat:leave-conversation')
  async handleLeaveConversation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `conversation:${data.conversation_id}`;
      await client.leave(roomName);

      this.logger.log(`User ${user.id} left conversation: ${data.conversation_id}`);

      // 通知對話中的其他用戶
      client.to(roomName).emit('conversation:user-left', {
        userId: user.id,
        username: user.username,
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });

      client.emit('conversation:left', {
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Leave conversation error: ${error.message}`);
    }
  }

  @SubscribeMessage('chat:send-message')
  async handleChatSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: { conversation_id: string; content: string; type?: string; replyToId?: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 簡化版本：只轉發事件，實際業務邏輯由 ChatService 通過 RealtimeEventsService 處理
      client.emit('chat:message-processing', {
        conversationId: data.conversation_id,
        content: data.content,
        type: data.type,
        replyToId: data.replyToId,
        senderId: user.id,
        timestamp: new Date(),
      });

      // 停止輸入狀態
      this.handleChatTypingStop(client, { conversation_id: data.conversation_id });
    } catch (error) {
      this.logger.error(`Chat send message error: ${error.message}`);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  @SubscribeMessage('chat:typing-start')
  async handleChatTypingStart(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `conversation:${data.conversation_id}`;
      client.to(roomName).emit('chat:typing-start', {
        userId: user.id,
        username: user.username,
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Chat typing start error: ${error.message}`);
    }
  }

  @SubscribeMessage('chat:typing-stop')
  async handleChatTypingStop(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) return;

      const roomName = `conversation:${data.conversation_id}`;
      client.to(roomName).emit('chat:typing-stop', {
        userId: user.id,
        username: user.username,
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Chat typing stop error: ${error.message}`);
    }
  }

  // 訊息中心相關事件處理 - 簡化版本
  @SubscribeMessage('message-center:send-message')
  async handleMessageCenterSendMessage(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: {
      conversation_id: string;
      content: string;
      content_type?: string;
      replyToMessageId?: string;
    },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 簡化版本：只轉發事件，實際業務邏輯由 MessageCenterService 處理
      client.emit('message-center:message-processing', {
        conversationId: data.conversation_id,
        content: data.content,
        contentType: data.content_type,
        replyToMessageId: data.replyToMessageId,
        senderId: user.id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Message center send message error: ${error.message}`);
      client.emit('error', { message: 'Failed to send message' });
    }
  }

  @SubscribeMessage('message-center:join-conversation')
  async handleMessageCenterJoinConversation(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { conversation_id: string },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 簡化版本：直接加入房間
      const roomName = `message-conversation:${data.conversation_id}`;
      await client.join(roomName);

      this.logger.log(
        `User ${user.id} joined message center conversation: ${data.conversation_id}`,
      );

      client.emit('message-center:conversation-joined', {
        conversationId: data.conversation_id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Message center join conversation error: ${error.message}`);
      client.emit('error', { message: 'Failed to join conversation' });
    }
  }

  @SubscribeMessage('message-center:notification')
  async handleMessageCenterNotification(
    @ConnectedSocket() client: Socket,
    @MessageBody()
    data: {
      recipient_id: string;
      type: string;
      title: string;
      content: string;
      workspace_id?: string;
    },
  ) {
    try {
      const user = await this.websocketService.getConnectionUser(client.id);
      if (!user) {
        client.emit('error', { message: 'Unauthorized' });
        return;
      }

      // 簡化版本：只轉發事件，實際業務邏輯由 MessageCenterService 處理
      client.emit('message-center:notification-processing', {
        ...data,
        senderId: user.id,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Message center notification error: ${error.message}`);
      client.emit('error', { message: 'Failed to send notification' });
    }
  }
}
