import { Injectable, Logger } from '@nestjs/common';
import { Server, Socket } from 'socket.io';
import { JwtService } from '@nestjs/jwt';
import { PrismaService } from '../../core/prisma/prisma.service';

interface ConnectedUser {
  id: string;
  username: string;
  email: string;
  tenant_id: string | null;
  user_type: 'system' | 'tenant';
  workspace_id?: string;
}

interface UserConnection {
  socket_id: string;
  user: ConnectedUser;
  connected_at: Date;
  last_activity: Date;
}

@Injectable()
export class WebSocketService {
  private server: Server;
  private connections = new Map<string, UserConnection>();
  private user_sockets = new Map<string, Set<string>>(); // user_id -> Set of socketIds
  private readonly logger = new Logger(WebSocketService.name);

  constructor(
    private readonly jwtService: JwtService,
    private readonly prisma: PrismaService,
  ) {}

  setServer(server: Server) {
    this.server = server;
  }

  // 認證 Socket 連線
  async authenticateSocket(socket: Socket): Promise<ConnectedUser | null> {
    try {
      this.logger.log(`🔐 Authenticating socket ${socket.id}`);
      this.logger.log(`Socket handshake auth: ${JSON.stringify(socket.handshake.auth)}`);
      this.logger.log(`Socket handshake query: ${JSON.stringify(socket.handshake.query)}`);
      this.logger.log(`Socket handshake headers: ${JSON.stringify(socket.handshake.headers)}`);

      // 支援多種方式獲取 token，優先順序：auth -> authorization header -> query -> cookie
      let token =
        socket.handshake.auth?.token ||
        socket.handshake.headers?.authorization?.replace('Bearer ', '') ||
        (socket.handshake.query as any)?.token;

      // 如果沒有 token，嘗試從 cookie 中獲取
      if (!token) {
        const cookies = socket.handshake.headers.cookie;
        this.logger.debug(`Socket cookies: ${cookies}`);

        if (cookies) {
          // 嘗試多種 cookie 名稱，與後端設置保持一致
          const auth_token_match = cookies.match(/auth_token=([^;]+)/);
          const access_token_match = cookies.match(/access_token=([^;]+)/);

          if (auth_token_match) {
            token = auth_token_match[1];
            this.logger.log(`✅ Found auth_token in cookies for socket ${socket.id}`);
          } else if (access_token_match) {
            token = access_token_match[1];
            this.logger.log(`✅ Found access_token in cookies for socket ${socket.id}`);
          } else {
            this.logger.debug(`Available cookies: ${cookies}`);
          }
        }
      }

      if (!token) {
        this.logger.warn(
          `❌ No token provided for socket ${socket.id}. Checked auth, authorization header, and cookies.`,
        );
        return null;
      }

      this.logger.log(`✅ Token found for socket ${socket.id}: ${token.substring(0, 20)}...`);

      // 驗證 JWT token
      const payload = this.jwtService.verify(token);
      const user_type = socket.handshake.auth?.user_type || payload.user_type;

      let user: any;
      if (user_type === 'system') {
        user = await this.prisma.system_users.findUnique({
          where: { id: payload.sub },
        });
      } else {
        user = await this.prisma.tenant_users.findUnique({
          where: { id: payload.sub },
          include: {
            tenant: true,
          },
        });
      }

      if (!user) {
        this.logger.warn(`User not found for socket ${socket.id} with user_type: ${user_type}`);
        return null;
      }

      // 對於租戶用戶，確保租戶存在
      if (user_type === 'tenant' && !user.tenant) {
        this.logger.warn(`Tenant not found for tenant_users with socket ${socket.id}`);
        return null;
      }

      return {
        id: user.id,
        username: user.name || user.email,
        email: user.email,
        tenant_id: user.tenant?.id || null,
        user_type: user_type,
      };
    } catch (error) {
      this.logger.error(`Authentication failed for socket ${socket.id}: ${error.message}`);
      return null;
    }
  }

  // 添加連線
  async addConnection(socket_id: string, user: ConnectedUser): Promise<void> {
    const connection: UserConnection = {
      socket_id,
      user,
      connected_at: new Date(),
      last_activity: new Date(),
    };

    this.connections.set(socket_id, connection);

    // 維護用戶 -> socket 映射
    if (!this.user_sockets.has(user.id)) {
      this.user_sockets.set(user.id, new Set());
    }
    this.user_sockets.get(user.id)!.add(socket_id);

    this.logger.log(`Added connection for user ${user.id} (socket: ${socket_id})`);
  }

  // 移除連線
  async removeConnection(socket_id: string): Promise<ConnectedUser | null> {
    const connection = this.connections.get(socket_id);
    if (!connection) return null;

    const user = connection.user;

    // 移除連線記錄
    this.connections.delete(socket_id);

    // 更新用戶 socket 映射
    const user_socket_set = this.user_sockets.get(user.id);
    if (user_socket_set) {
      user_socket_set.delete(socket_id);
      if (user_socket_set.size === 0) {
        this.user_sockets.delete(user.id);
      }
    }

    this.logger.log(`Removed connection for user ${user.id} (socket: ${socket_id})`);
    return user;
  }

  // 獲取連線的用戶資訊
  async getConnectionUser(socket_id: string): Promise<ConnectedUser | null> {
    const connection = this.connections.get(socket_id);
    if (!connection) return null;

    // 更新最後活動時間
    connection.last_activity = new Date();
    return connection.user;
  }

  // 驗證房間存取權限
  async validateRoomAccess(user_id: string, room_type: string, room_id: string): Promise<boolean> {
    try {
      switch (room_type) {
        case 'user':
          // 用戶只能加入自己的個人房間
          return user_id === room_id;
        case 'workspace':
          return await this.validateWorkspaceAccess(user_id, room_id);
        case 'project':
          return await this.validateProjectAccess(user_id, room_id);
        case 'task':
          return await this.validateTaskAccess(user_id, room_id);
        case 'file':
          return await this.validateFileAccess(user_id, room_id);
        default:
          return false;
      }
    } catch (error) {
      this.logger.error(`Room access validation failed: ${error.message}`);
      return false;
    }
  }

  // 驗證工作區存取權限
  private async validateWorkspaceAccess(user_id: string, workspace_id: string): Promise<boolean> {
    const member = await this.prisma.workspace_members.findFirst({
      where: {
        tenant_user_id: user_id,
        workspace_id: workspace_id,
      },
    });
    return !!member;
  }

  // 驗證專案存取權限
  private async validateProjectAccess(user_id: string, project_id: string): Promise<boolean> {
    const project = await this.prisma.projects.findFirst({
      where: {
        id: project_id,
        workspaces: {
          workspace_members: {
            some: {
              tenant_user_id: user_id,
            },
          },
        },
      },
    });
    return !!project;
  }

  // 驗證任務存取權限
  private async validateTaskAccess(user_id: string, task_id: string): Promise<boolean> {
    const task = await this.prisma.tasks.findFirst({
      where: {
        id: task_id,
        project: {
          workspaces: {
            workspace_members: {
              some: {
                tenant_user_id: user_id,
              },
            },
          },
        },
      },
    });
    return !!task;
  }

  // 驗證檔案存取權限
  private async validateFileAccess(user_id: string, file_id: string): Promise<boolean> {
    const filePermission = await this.prisma.file_permissions.findFirst({
      where: {
        file_id: file_id,
        user_id: user_id,
      },
    });
    if (filePermission) return true;

    const sharedFile = await this.prisma.shared_files.findFirst({
      where: {
        id: file_id,
        uploader_id: user_id,
      },
    });
    if (sharedFile) return true;

    const workspaceFile = await this.prisma.workspaces.findFirst({
      where: {
        shared_files: { some: { id: file_id } },
        workspace_members: {
          some: {
            tenant_user_id: user_id,
          },
        },
      },
    });
    return !!workspaceFile;
  }

  // 廣播到特定用戶的所有連線
  async broadcastToUser(user_id: string, event: string, data: any): Promise<void> {
    const socket_ids = this.user_sockets.get(user_id);
    if (!socket_ids || socket_ids.size === 0) return;

    socket_ids.forEach((socket_id) => {
      this.server.to(socket_id).emit(event, data);
    });

    this.logger.log(`Broadcasted ${event} to user ${user_id} (${socket_ids.size} connections)`);
  }

  // 廣播到房間
  async broadcastToRoom(
    room_type: string,
    room_id: string,
    event: string,
    data: any,
  ): Promise<void> {
    const room_name = `${room_type}:${room_id}`;
    this.server.to(room_name).emit(event, data);
    this.logger.log(`Broadcasted ${event} to room ${room_name}`);
  }

  // 廣播到工作區所有成員
  async broadcastToWorkspace(workspace_id: string, event: string, data: any): Promise<void> {
    await this.broadcastToRoom('workspace', workspace_id, event, data);
  }

  // 廣播到專案所有成員
  async broadcastToProject(project_id: string, event: string, data: any): Promise<void> {
    await this.broadcastToRoom('project', project_id, event, data);
  }

  // 獲取線上用戶列表
  getOnlineUsers(): ConnectedUser[] {
    const onlineUsers = new Map<string, ConnectedUser>();

    this.connections.forEach((connection) => {
      onlineUsers.set(connection.user.id, connection.user);
    });

    return Array.from(onlineUsers.values());
  }

  // 獲取房間內的線上用戶
  async getRoomOnlineUsers(room_type: string, room_id: string): Promise<ConnectedUser[]> {
    const room_name = `${room_type}:${room_id}`;
    const room = this.server.sockets.adapter.rooms.get(room_name);

    if (!room) return [];

    const onlineUsers: ConnectedUser[] = [];
    room.forEach((socket_id) => {
      const connection = this.connections.get(socket_id);
      if (connection) {
        onlineUsers.push(connection.user);
      }
    });

    return onlineUsers;
  }

  // 清理過期連線
  async cleanupExpiredConnections(): Promise<void> {
    const now = new Date();
    const expiredThreshold = 30 * 60 * 1000; // 30 分鐘

    const expiredConnections: string[] = [];

    this.connections.forEach((connection, socket_id) => {
      if (now.getTime() - connection.last_activity.getTime() > expiredThreshold) {
        expiredConnections.push(socket_id);
      }
    });

    expiredConnections.forEach((socket_id) => {
      this.removeConnection(socket_id);
    });

    if (expiredConnections.length > 0) {
      this.logger.log(`Cleaned up ${expiredConnections.length} expired connections`);
    }
  }

  // 獲取連線統計
  getConnectionStats() {
    const totalConnections = this.connections.size;
    const uniqueUsers = this.user_sockets.size;
    const onlineUsers = this.getOnlineUsers();

    return {
      totalConnections,
      uniqueUsers,
      onlineUsers: onlineUsers.length,
      connections: Array.from(this.connections.values()).map((conn) => ({
        socket_id: conn.socket_id,
        user_id: conn.user.id,
        username: conn.user.username,
        connected_at: conn.connected_at,
        last_activity: conn.last_activity,
      })),
    };
  }

  async isWorkspaceMember(user_id: string, workspace_id: string): Promise<boolean> {
    const member = await this.prisma.workspace_members.findFirst({
      where: {
        tenant_user_id: user_id,
        workspace_id: workspace_id,
      },
    });
    return !!member;
  }

  async getWorkspacesForUser(user_id: string, tenant_id: string) {
    const workspaces = await this.prisma.workspaces.findMany({
      where: {
        tenant_id: tenant_id,
        workspace_members: {
          some: {
            tenant_user_id: user_id,
          },
        },
      },
      include: {
        _count: {
          select: {
            workspace_members: {
              where: {
                tenant_user_id: user_id,
              },
            },
          },
        },
      },
    });
    return workspaces;
  }

  async canUserAccessFile(user_id: string, file_id: string): Promise<boolean> {
    const filePermission = await this.prisma.file_permissions.findFirst({
      where: {
        file_id: file_id,
        user_id: user_id,
      },
    });
    if (filePermission) return true;

    const sharedFile = await this.prisma.shared_files.findFirst({
      where: {
        id: file_id,
        uploader_id: user_id,
      },
    });
    if (sharedFile) return true;

    const workspaceFile = await this.prisma.workspaces.findFirst({
      where: {
        shared_files: { some: { id: file_id } },
        workspace_members: {
          some: {
            tenant_user_id: user_id,
          },
        },
      },
    });
    return !!workspaceFile;
  }
}
