import { Injectable, Logger } from '@nestjs/common';
import { WebSocketService } from './websocket.service';
import { FileUpdateType, CommentUpdateType, TaskUpdateType, ProjectUpdateType } from './dto';

@Injectable()
export class RealtimeEventsService {
  private readonly logger = new Logger(RealtimeEventsService.name);

  constructor(private readonly websocketService: WebSocketService) {}

  // 檔案相關事件
  async notifyFileUploaded(data: {
    file_id: string;
    file_name: string;
    entity_type: string;
    entity_id: string;
    uploadedBy: string;
    workspaceId: string;
  }) {
    const eventData = {
      file_id: data.file_id,
      updateType: FileUpdateType.UPLOADED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      fileName: data.file_name,
      description: `檔案 "${data.file_name}" 已上傳`,
    };

    // 廣播到相關房間
    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'file:updated',
      eventData,
    );
    await this.websocketService.broadcastToWorkspace(data.workspaceId, 'file:updated', eventData);

    this.logger.log(`File upload notification sent: ${data.file_name}`);
  }

  async notifyFileUpdated(data: {
    file_id: string;
    file_name: string;
    entity_type: string;
    entity_id: string;
    updatedBy: string;
    workspaceId: string;
  }) {
    const eventData = {
      file_id: data.file_id,
      updateType: FileUpdateType.UPDATED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      fileName: data.file_name,
      description: `檔案 "${data.file_name}" 已更新`,
    };

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'file:updated',
      eventData,
    );
    await this.websocketService.broadcastToWorkspace(data.workspaceId, 'file:updated', eventData);

    this.logger.log(`File update notification sent: ${data.file_name}`);
  }

  async notifyFileShared(data: {
    file_id: string;
    file_name: string;
    entity_type: string;
    entity_id: string;
    shared_by: string;
    sharedWith: string[];
    workspaceId: string;
  }) {
    const eventData = {
      file_id: data.file_id,
      updateType: FileUpdateType.SHARED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      fileName: data.file_name,
      description: `檔案 "${data.file_name}" 已分享`,
    };

    // 通知被分享的用戶
    for (const sharedUserId of data.sharedWith) {
      await this.websocketService.broadcastToUser(sharedUserId, 'file:shared', eventData);
    }

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'file:updated',
      eventData,
    );

    this.logger.log(
      `File share notification sent: ${data.file_name} to ${data.sharedWith.length} users`,
    );
  }

  async notifyFileDeleted(data: {
    file_id: string;
    file_name: string;
    entity_type: string;
    entity_id: string;
    deletedBy: string;
    workspaceId: string;
  }) {
    const eventData = {
      file_id: data.file_id,
      updateType: FileUpdateType.DELETED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      fileName: data.file_name,
      description: `檔案 "${data.file_name}" 已刪除`,
    };

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'file:updated',
      eventData,
    );
    await this.websocketService.broadcastToWorkspace(data.workspaceId, 'file:updated', eventData);

    this.logger.log(`File deletion notification sent: ${data.file_name}`);
  }

  // 評論相關事件
  async notifyCommentCreated(data: {
    comment_id: string;
    content: string;
    entity_type: string;
    entity_id: string;
    author_id: string;
    authorName: string;
    workspaceId: string;
    parentCommentId?: string;
  }) {
    const eventData = {
      commentId: data.comment_id,
      updateType: CommentUpdateType.CREATED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      content: data.content,
      parentCommentId: data.parentCommentId,
      authorId: data.author_id,
      authorName: data.authorName,
    };

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'comment:updated',
      eventData,
    );
    await this.websocketService.broadcastToWorkspace(
      data.workspaceId,
      'comment:created',
      eventData,
    );

    this.logger.log(`Comment creation notification sent for ${data.entity_type}:${data.entity_id}`);
  }

  async notifyCommentUpdated(data: {
    comment_id: string;
    content: string;
    entity_type: string;
    entity_id: string;
    author_id: string;
    workspaceId: string;
  }) {
    const eventData = {
      commentId: data.comment_id,
      updateType: CommentUpdateType.UPDATED,
      entityType: data.entity_type,
      entityId: data.entity_id,
      content: data.content,
    };

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'comment:updated',
      eventData,
    );

    this.logger.log(`Comment update notification sent: ${data.comment_id}`);
  }

  async notifyCommentDeleted(data: {
    comment_id: string;
    entity_type: string;
    entity_id: string;
    workspaceId: string;
  }) {
    const eventData = {
      commentId: data.comment_id,
      updateType: CommentUpdateType.DELETED,
      entityType: data.entity_type,
      entityId: data.entity_id,
    };

    await this.websocketService.broadcastToRoom(
      data.entity_type,
      data.entity_id,
      'comment:updated',
      eventData,
    );

    this.logger.log(`Comment deletion notification sent: ${data.comment_id}`);
  }

  // 任務相關事件
  async notifyTaskCreated(data: {
    taskId: string;
    title: string;
    projectId: string;
    workspaceId: string;
    createdBy: string;
    assignedTo?: string;
  }) {
    const eventData = {
      taskId: data.taskId,
      updateType: TaskUpdateType.CREATED,
      projectId: data.projectId,
      title: data.title,
      assignedTo: data.assignedTo,
    };

    await this.websocketService.broadcastToProject(data.projectId, 'task:updated', eventData);
    await this.websocketService.broadcastToWorkspace(data.workspaceId, 'task:created', eventData);

    // 通知被指派的用戶
    if (data.assignedTo) {
      await this.websocketService.broadcastToUser(data.assignedTo, 'task:assigned', eventData);
    }

    this.logger.log(`Task creation notification sent: ${data.title}`);
  }

  async notifyTaskStatusChanged(data: {
    taskId: string;
    title: string;
    status: string;
    projectId: string;
    workspaceId: string;
    updatedBy: string;
    assignedTo?: string;
  }) {
    const eventData = {
      taskId: data.taskId,
      updateType: TaskUpdateType.STATUS_CHANGED,
      projectId: data.projectId,
      title: data.title,
      status: data.status,
      assignedTo: data.assignedTo,
    };

    await this.websocketService.broadcastToProject(data.projectId, 'task:updated', eventData);

    // 通知被指派的用戶
    if (data.assignedTo) {
      await this.websocketService.broadcastToUser(
        data.assignedTo,
        'task:status-changed',
        eventData,
      );
    }

    this.logger.log(`Task status change notification sent: ${data.title} -> ${data.status}`);
  }

  async notifyTaskAssigned(data: {
    taskId: string;
    title: string;
    projectId: string;
    workspaceId: string;
    assignedTo: string;
    assignedBy: string;
  }) {
    const eventData = {
      taskId: data.taskId,
      updateType: TaskUpdateType.ASSIGNED,
      projectId: data.projectId,
      title: data.title,
      assignedTo: data.assignedTo,
    };

    await this.websocketService.broadcastToProject(data.projectId, 'task:updated', eventData);
    await this.websocketService.broadcastToUser(data.assignedTo, 'task:assigned', eventData);

    this.logger.log(`Task assignment notification sent: ${data.title} to user ${data.assignedTo}`);
  }

  // 專案相關事件
  async notifyProjectCreated(data: {
    projectId: string;
    title: string;
    workspaceId: string;
    createdBy: string;
  }) {
    const eventData = {
      projectId: data.projectId,
      updateType: ProjectUpdateType.CREATED,
      workspaceId: data.workspaceId,
      title: data.title,
    };

    await this.websocketService.broadcastToWorkspace(
      data.workspaceId,
      'project:updated',
      eventData,
    );

    this.logger.log(`Project creation notification sent: ${data.title}`);
  }

  async notifyProjectStatusChanged(data: {
    projectId: string;
    title: string;
    status: string;
    workspaceId: string;
    updatedBy: string;
  }) {
    const eventData = {
      projectId: data.projectId,
      updateType: ProjectUpdateType.STATUS_CHANGED,
      workspaceId: data.workspaceId,
      title: data.title,
      status: data.status,
    };

    await this.websocketService.broadcastToWorkspace(
      data.workspaceId,
      'project:updated',
      eventData,
    );
    await this.websocketService.broadcastToProject(
      data.projectId,
      'project:status-changed',
      eventData,
    );

    this.logger.log(`Project status change notification sent: ${data.title} -> ${data.status}`);
  }

  async notifyProjectMemberAdded(data: {
    projectId: string;
    title: string;
    workspaceId: string;
    memberId: string;
    memberName: string;
    addedBy: string;
  }) {
    const eventData = {
      projectId: data.projectId,
      updateType: ProjectUpdateType.MEMBER_ADDED,
      workspaceId: data.workspaceId,
      title: data.title,
      memberId: data.memberId,
    };

    await this.websocketService.broadcastToProject(data.projectId, 'project:updated', eventData);
    await this.websocketService.broadcastToUser(data.memberId, 'project:member-added', eventData);

    this.logger.log(
      `Project member addition notification sent: ${data.memberName} to ${data.title}`,
    );
  }

  // 通用通知
  async notifyUserMention(data: {
    mentionedUserId: string;
    mentionedBy: string;
    mentionedByName: string;
    entity_type: string;
    entity_id: string;
    content: string;
    workspaceId: string;
  }) {
    const eventData = {
      mentionedBy: data.mentionedBy,
      mentionedByName: data.mentionedByName,
      entityType: data.entity_type,
      entityId: data.entity_id,
      content: data.content,
      timestamp: new Date(),
    };

    await this.websocketService.broadcastToUser(data.mentionedUserId, 'user:mentioned', eventData);

    this.logger.log(`User mention notification sent to ${data.mentionedUserId}`);
  }

  // 聊天相關即時事件
  async notifyNewChatMessage(conversationId: string, message: any, senderId: string) {
    try {
      const roomName = `conversation:${conversationId}`;
      await this.websocketService.broadcastToRoom(
        'conversation',
        conversationId,
        'chat:new-message',
        {
          message,
          conversationId,
          senderId,
          timestamp: new Date(),
        },
      );

      this.logger.log(`Broadcasted new chat message to conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Failed to notify new chat message: ${error.message}`);
    }
  }

  async notifyConversationUpdate(conversationId: string, updateData: any, updatedBy: string) {
    try {
      await this.websocketService.broadcastToRoom(
        'conversation',
        conversationId,
        'chat:conversation-updated',
        {
          conversationId,
          updateData,
          updatedBy,
          timestamp: new Date(),
        },
      );

      this.logger.log(`Broadcasted conversation update for ${conversationId}`);
    } catch (error) {
      this.logger.error(`Failed to notify conversation update: ${error.message}`);
    }
  }

  async notifyUserTyping(conversationId: string, userId: string, isTyping: boolean) {
    try {
      const event = isTyping ? 'chat:user-typing-start' : 'chat:user-typing-stop';
      await this.websocketService.broadcastToRoom('conversation', conversationId, event, {
        conversationId,
        userId,
        timestamp: new Date(),
      });
    } catch (error) {
      this.logger.error(`Failed to notify typing status: ${error.message}`);
    }
  }

  async notifyMessageReaction(
    conversationId: string,
    messageId: string,
    reaction: any,
    userId: string,
  ) {
    try {
      await this.websocketService.broadcastToRoom(
        'conversation',
        conversationId,
        'chat:message-reaction',
        {
          conversationId,
          messageId,
          reaction,
          userId,
          timestamp: new Date(),
        },
      );

      this.logger.log(`Broadcasted message reaction for message ${messageId}`);
    } catch (error) {
      this.logger.error(`Failed to notify message reaction: ${error.message}`);
    }
  }

  // 訊息中心相關即時事件
  async notifyNewMessageCenterMessage(conversationId: string, message: any, senderId: string) {
    try {
      const roomName = `message-conversation:${conversationId}`;
      await this.websocketService.broadcastToRoom(
        'message-conversation',
        conversationId,
        'message-center:new-message',
        {
          message,
          conversationId,
          senderId,
          timestamp: new Date(),
        },
      );

      this.logger.log(`Broadcasted new message center message to conversation ${conversationId}`);
    } catch (error) {
      this.logger.error(`Failed to notify new message center message: ${error.message}`);
    }
  }

  async notifyNewNotification(userId: string, notification: any) {
    try {
      await this.websocketService.broadcastToUser(userId, 'message-center:new-notification', {
        notification,
        timestamp: new Date(),
      });

      this.logger.log(`Sent notification to user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to send notification: ${error.message}`);
    }
  }

  async notifyNotificationRead(userId: string, notificationId: string) {
    try {
      await this.websocketService.broadcastToUser(userId, 'message-center:notification-read', {
        notificationId,
        timestamp: new Date(),
      });

      this.logger.log(`Notified notification read for user ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to notify notification read: ${error.message}`);
    }
  }

  async notifyUnreadCountUpdate(userId: string, unreadCount: number) {
    try {
      await this.websocketService.broadcastToUser(userId, 'message-center:unread-count-update', {
        unreadCount,
        timestamp: new Date(),
      });

      this.logger.log(`Updated unread count for user ${userId}: ${unreadCount}`);
    } catch (error) {
      this.logger.error(`Failed to update unread count: ${error.message}`);
    }
  }

  // 工作區相關即時事件
  async notifyWorkspaceUpdate(workspaceId: string, updateData: any, updatedBy: string) {
    try {
      await this.websocketService.broadcastToWorkspace(workspaceId, 'workspace:updated', {
        workspaceId: workspaceId,
        updateData,
        updatedBy,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted workspace update for ${workspaceId}`);
    } catch (error) {
      this.logger.error(`Failed to notify workspace update: ${error.message}`);
    }
  }

  async notifyUserOnlineStatus(workspaceId: string, userId: string, isOnline: boolean) {
    try {
      const event = isOnline ? 'user:online' : 'user:offline';
      await this.websocketService.broadcastToWorkspace(workspaceId, event, {
        userId: userId,
        isOnline,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted user ${isOnline ? 'online' : 'offline'} status for ${userId}`);
    } catch (error) {
      this.logger.error(`Failed to notify user online status: ${error.message}`);
    }
  }

  // 專案相關即時事件
  async notifyProjectUpdate(projectId: string, updateData: any, updatedBy: string) {
    try {
      await this.websocketService.broadcastToProject(projectId, 'project:updated', {
        projectId,
        updateData,
        updatedBy,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted project update for ${projectId}`);
    } catch (error) {
      this.logger.error(`Failed to notify project update: ${error.message}`);
    }
  }

  // 任務相關即時事件
  async notifyTaskUpdate(taskId: string, updateData: any, updatedBy: string) {
    try {
      await this.websocketService.broadcastToRoom('task', taskId, 'task:updated', {
        taskId,
        updateData,
        updatedBy,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted task update for ${taskId}`);
    } catch (error) {
      this.logger.error(`Failed to notify task update: ${error.message}`);
    }
  }

  // 檔案相關即時事件
  async notifyFileUpdate(file_id: string, updateData: any, updatedBy: string) {
    try {
      await this.websocketService.broadcastToRoom('file', file_id, 'file:updated', {
        fileId: file_id,
        updateData,
        updatedBy,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted file update for ${file_id}`);
    } catch (error) {
      this.logger.error(`Failed to notify file update: ${error.message}`);
    }
  }

  // 評論相關即時事件
  async notifyCommentUpdate(
    entityType: string,
    entityId: string,
    commentData: any,
    updatedBy: string,
  ) {
    try {
      await this.websocketService.broadcastToRoom(entityType, entityId, 'comment:updated', {
        entityType,
        entityId,
        commentData,
        updatedBy,
        timestamp: new Date(),
      });

      this.logger.log(`Broadcasted comment update for ${entityType}:${entityId}`);
    } catch (error) {
      this.logger.error(`Failed to notify comment update: ${error.message}`);
    }
  }

  // 系統通知
  async notifySystemMessage(
    workspaceId: string,
    message: string,
    type: 'info' | 'warning' | 'error' = 'info',
  ) {
    try {
      await this.websocketService.broadcastToWorkspace(workspaceId, 'system:message', {
        message,
        type,
        timestamp: new Date(),
      });

      this.logger.log(`Sent system message to workspace ${workspaceId}: ${message}`);
    } catch (error) {
      this.logger.error(`Failed to send system message: ${error.message}`);
    }
  }

  // 獲取在線用戶統計
  async getOnlineUsersStats(workspaceId?: string): Promise<{ total: number; users: any[] }> {
    try {
      const onlineUsers = this.websocketService.getOnlineUsers();

      if (workspaceId) {
        // 過濾特定工作區的用戶
        const workspaceUsers = onlineUsers.filter((user: any) => user.workspaceId === workspaceId);
        return {
          total: workspaceUsers.length,
          users: workspaceUsers,
        };
      }

      return {
        total: onlineUsers.length,
        users: onlineUsers,
      };
    } catch (error) {
      this.logger.error(`Failed to get online users stats: ${error.message}`);
      return { total: 0, users: [] };
    }
  }
}
