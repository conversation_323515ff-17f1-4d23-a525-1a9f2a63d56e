import { Modu<PERSON> } from '@nestjs/common';
import { WorkspaceWebSocketGateway } from './websocket.gateway';
import { WebSocketService } from './websocket.service';
import { WebSocketController } from './websocket.controller';
import { RealtimeEventsService } from './realtime-events.service';
import { PrismaModule } from '../../core/prisma/prisma.module';
import { WorkspacesModule } from '../../admin/workspaces/workspaces.module';
import { AuthModule } from '../../core/auth/auth.module';

@Module({
  imports: [PrismaModule, WorkspacesModule, AuthModule],
  controllers: [WebSocketController],
  providers: [WorkspaceWebSocketGateway, WebSocketService, RealtimeEventsService],
  exports: [WebSocketService, RealtimeEventsService],
})
export class WebSocketModule {}
