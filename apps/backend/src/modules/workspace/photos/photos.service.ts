import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { StorageService } from '../../core/storage/storage.service';
import { CreatePhotoDto, UpdatePhotoDto, PhotoFilterDto } from './dto/photo.dto';
import { PhotoCategory } from '../../../common/enums/photo-category.enum';
import { randomUUID } from 'crypto';
import { PhotoWithProject } from './entities/photo.entity';

// 使用自訂的 SQL 輔助函式
const sql = (strings: TemplateStringsArray, ...values: any[]) => ({
  text: String.raw(strings, ...values.map((v, i) => `$${i + 1}`)),
  values,
});
// 修改 empty 為字串而非 Symbol
const empty = '';

@Injectable()
export class PhotosService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly storageService: StorageService,
  ) {}

  async getPhotos(
    filters: PhotoFilterDto,
    userId: string,
    tenantId: string,
  ): Promise<PhotoWithProject[]> {
    const photos = await this.prisma.$queryRaw<PhotoWithProject[]>`
      SELECT 
        p.*,
        json_build_object(
          'id', pr.id,
          'name', pr.name,
          'description', pr.description,
          'tenant_id', pr."tenant_id",
          'user_id', pr."user_id",
          'created_at', pr."created_at",
          'updated_at', pr."updated_at"
        ) as project
      FROM "photos" p
      LEFT JOIN "projects" pr ON p."project_id" = pr.id
      WHERE p."user_id" = ${userId}
      AND p."tenant_id" = ${tenantId}
      ${filters.category ? `AND p.category = '${filters.category}'` : ''}
      ${filters.projectId ? `AND p."project_id" = '${filters.projectId}'` : ''}
      ${
        filters.searchKeyword
          ? `AND (
        p.title ILIKE '%${filters.searchKeyword}%' OR 
        p.description ILIKE '%${filters.searchKeyword}%'
      )`
          : ''
      }
      ORDER BY ${filters.sortBy === 'name' ? 'p.title ASC' : 'p."created_at" DESC'}
    `;

    return photos;
  }

  async uploadPhotos(
    files: Express.Multer.File[],
    projectId: string,
    userId: string,
    tenantId: string,
  ) {
    const project = await this.prisma.projects.findUnique({
      where: {
        id: projectId,
      },
    });

    if (!project || project.user_id !== userId || project.tenant_id !== tenantId) {
      throw new NotFoundException('專案不存在');
    }

    // 讀取預設相簿，如果沒有則建立一個
    let album = await this.prisma.albums.findFirst({
      where: {
        user_id: userId,
        tenant_id: tenantId,
      },
    });

    if (!album) {
      const newAlbumId = randomUUID();
      await this.prisma.$executeRaw`
        INSERT INTO "albums" ("id", "name", "description", "user_id", "tenant_id", "photos_count", "created_at", "updated_at")
        VALUES (
          ${newAlbumId},
          '預設相簿',
          '自動建立的預設相簿',
          ${userId},
          ${tenantId},
          0,
          NOW(),
          NOW()
        )
      `;

      album = await this.prisma.albums.findUnique({
        where: { id: newAlbumId },
      });

      if (!album) {
        throw new Error('無法建立預設相簿');
      }
    }

    const uploadedPhotos = await Promise.all(
      files.map(async (file) => {
        const url = await this.storageService.uploadFile(file);
        const photoId = randomUUID();

        await this.prisma.$executeRaw`
          INSERT INTO "photos" (
            "id", "title", "url", "category", "user_id", "tenant_id", "project_id", "album_id",
            "created_at", "updated_at"
          )
          VALUES (
            ${photoId},
            ${file.originalname},
            ${url},
            ${PhotoCategory.SITE},
            ${userId},
            ${tenantId},
            ${projectId},
            ${album.id},
            NOW(),
            NOW()
          )
        `;

        const [photo] = await this.prisma.$queryRaw<PhotoWithProject[]>`
          SELECT 
            p.*,
            json_build_object(
              'id', pr.id,
              'name', pr.name,
              'description', pr.description,
              'tenant_id', pr."tenant_id",
              'user_id', pr."user_id",
              'created_at', pr."created_at",
              'updated_at', pr."updated_at"
            ) as project
          FROM "photos" p
          LEFT JOIN "projects" pr ON p."project_id" = pr.id
          WHERE p.id = ${photoId}
        `;

        return photo;
      }),
    );

    return {
      success: true,
      photos: uploadedPhotos,
    };
  }

  async updatePhoto(id: string, updatePhotoDto: UpdatePhotoDto, userId: string, tenantId: string) {
    const photo = await this.prisma.photos.findUnique({
      where: { id },
    });

    if (!photo) {
      throw new NotFoundException('照片不存在');
    }

    // 使用 SQL 查詢檢查權限
    const [photoWithOwnership] = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM "photos" WHERE id = ${id} AND "user_id" = ${userId} AND "tenant_id" = ${tenantId}
    `;

    if (!photoWithOwnership) {
      throw new NotFoundException('您沒有權限存取此照片');
    }

    const { category, ...updateData } = updatePhotoDto;

    const data = {
      ...updateData,
      ...(category && { category }),
    };

    return this.prisma.photos.update({
      where: { id },
      data,
    });
  }

  async deletePhoto(id: string, userId: string, tenantId: string) {
    const photo = await this.prisma.photos.findUnique({
      where: { id },
    });

    if (!photo) {
      throw new NotFoundException('照片不存在');
    }

    // 使用 SQL 查詢檢查權限
    const [photoWithOwnership] = await this.prisma.$queryRaw<any[]>`
      SELECT * FROM "photos" WHERE id = ${id} AND "user_id" = ${userId} AND "tenant_id" = ${tenantId}
    `;

    if (!photoWithOwnership) {
      throw new NotFoundException('您沒有權限存取此照片');
    }

    await this.storageService.deleteFile(photo.url);

    await this.prisma.photos.delete({
      where: { id },
    });
  }
}
