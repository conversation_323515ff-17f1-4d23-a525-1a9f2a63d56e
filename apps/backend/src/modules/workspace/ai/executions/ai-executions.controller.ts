import {
  Controller,
  Post,
  Get,
  Body,
  Param,
  ValidationPipe,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AiExecutionService } from './ai-execution.service';
import { ExecuteAiServiceDto } from './dto/execute-ai-service.dto';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';

@Controller('workspace/ai/executions')
@UseGuards(JwtAuthGuard)
export class AiExecutionsController {
  constructor(private readonly executionService: AiExecutionService) {}

  @Post()
  async executeService(@Body(ValidationPipe) executeDto: ExecuteAiServiceDto, @Request() req: any) {
    const user_id = req.user.id;
    const workspace_id = req.user.workspace_id;

    return this.executionService.executeService(
      executeDto.service_definition_id,
      executeDto.input_data,
      user_id,
      workspace_id,
    );
  }

  @Get(':executionId')
  async getExecutionStatus(@Param('executionId') execution_id: string) {
    // This endpoint will be implemented to fetch execution status from database
    // For now, return a placeholder
    return { message: 'Execution status endpoint - to be implemented' };
  }
}
