import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { AiBotsService } from '@/modules/admin/ai/bots/ai-bots.service';

@Injectable()
export class AiExecutionService {
  private readonly logger = new Logger(AiExecutionService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly botExecutionService: AiBotsService,
  ) {}

  async executeService(
    definition_id: string,
    input_data: Record<string, any>,
    user_id: string,
    workspace_id?: string,
  ) {
    this.logger.log(
      `Starting execution for service definition: ${definition_id} by user: ${user_id}`,
    );

    try {
      // 簡化的執行邏輯
      const result = {
        execution_id: `exec_${Date.now()}`,
        result: input_data,
        status: 'completed',
      };

      this.logger.log(`Successfully completed execution: ${result.execution_id}`);
      return result;
    } catch (error) {
      this.logger.error(
        `Failed execution for definition ${definition_id}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }
}
