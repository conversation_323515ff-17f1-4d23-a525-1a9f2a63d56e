import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AiExecutionService } from './ai-execution.service';
import { AiExecutionsController } from './ai-executions.controller';
import { AiBotsModule } from '@/modules/admin/ai/bots/ai-bots.module';

@Module({
  imports: [
    PrismaModule,
    AiBotsModule, // Import for bot execution functionality
  ],
  controllers: [AiExecutionsController],
  providers: [AiExecutionService],
  exports: [AiExecutionService],
})
export class AiExecutionsModule {}
