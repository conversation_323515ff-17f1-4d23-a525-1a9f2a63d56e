import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsString,
  IsEnum,
  IsOptional,
  IsBoolean,
  IsDateString,
  IsArray,
  IsN<PERSON>ber,
  <PERSON>,
  Max,
} from 'class-validator';

export enum ProjectAnalysisType {
  STATUS = 'status',
  RISK = 'risk',
  PERFORMANCE = 'performance',
  OPTIMIZATION = 'optimization',
}

export enum PhotoAnalysisType {
  PROGRESS = 'progress',
  QUALITY = 'quality',
  SAFETY = 'safety',
  EQUIPMENT = 'equipment',
}

export class ProjectAnalysisRequestDto {
  @ApiProperty({
    description: '項目 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  @IsString()
  projectId: string;

  @ApiProperty({
    description: '分析類型',
    enum: ProjectAnalysisType,
    example: ProjectAnalysisType.STATUS,
  })
  @IsEnum(ProjectAnalysisType)
  analysisType: ProjectAnalysisType;

  @ApiPropertyOptional({
    description: '是否包含子項目',
    default: false,
  })
  @IsOptional()
  @IsBoolean()
  includeSubProjects?: boolean;

  @ApiPropertyOptional({
    description: '是否包含任務',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  includeTasks?: boolean;

  @ApiPropertyOptional({
    description: '是否包含進度',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  includeProgress?: boolean;

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    example: 0.8,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}

export class PhotoAnalysisRequestDto {
  @ApiProperty({
    description: '照片 URL',
    example: 'https://example.com/photo.jpg',
  })
  @IsString()
  photoUrl: string;

  @ApiProperty({
    description: '項目 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  @IsString()
  projectId: string;

  @ApiProperty({
    description: '分析類型',
    enum: PhotoAnalysisType,
    example: PhotoAnalysisType.PROGRESS,
  })
  @IsEnum(PhotoAnalysisType)
  analysisType: PhotoAnalysisType;

  @ApiPropertyOptional({
    description: '額外上下文信息',
    example: '這是施工現場的照片，請分析進度',
  })
  @IsOptional()
  @IsString()
  context?: string;

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    example: 0.8,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}

export class WorkflowOptimizationRequestDto {
  @ApiPropertyOptional({
    description: '項目 ID（可選，如果不提供則分析整個租戶）',
    example: 'clxxxxxxxxxxxxx',
  })
  @IsOptional()
  @IsString()
  projectId?: string;

  @ApiProperty({
    description: '開始日期',
    example: '2024-01-01T00:00:00.000Z',
  })
  @IsDateString()
  startDate: string;

  @ApiProperty({
    description: '結束日期',
    example: '2024-12-31T23:59:59.999Z',
  })
  @IsDateString()
  endDate: string;

  @ApiPropertyOptional({
    description: '包含的指標',
    example: ['taskCompletion', 'resourceUtilization', 'timeTracking'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  includeMetrics?: string[];

  @ApiPropertyOptional({
    description: 'AI 分析信心度設定 (0-1)',
    example: 0.8,
    minimum: 0,
    maximum: 1,
  })
  @IsOptional()
  @IsNumber()
  @Min(0)
  @Max(1)
  confidence?: number;
}

export class ProjectInsightsDto {
  @ApiProperty({
    description: '項目現狀摘要',
    example: '項目進展順利，已完成 75% 的任務',
  })
  summary: string;

  @ApiProperty({
    description: '改進建議',
    example: ['增加資源投入', '優化任務分配', '加強溝通協調'],
  })
  recommendations: string[];

  @ApiProperty({
    description: '風險因素',
    example: ['預算超支風險', '時程延遲風險'],
  })
  riskFactors: string[];

  @ApiProperty({
    description: '優化建議',
    example: ['實施敏捷開發', '引入自動化工具'],
  })
  optimizationSuggestions: string[];
}

export class ProjectAnalysisResultDto {
  @ApiProperty({
    description: '項目 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  projectId: string;

  @ApiProperty({
    description: '分析類型',
    example: 'status',
  })
  analysisType: string;

  @ApiProperty({
    description: '分析洞察',
    type: ProjectInsightsDto,
  })
  insights: ProjectInsightsDto;

  @ApiProperty({
    description: '信心度',
    example: 0.85,
  })
  confidence: number;

  @ApiProperty({
    description: '生成時間',
    example: '2024-01-01T12:00:00.000Z',
  })
  generatedAt: Date;
}

export class PhotoFindingsDto {
  @ApiProperty({
    description: '描述',
    example: '施工現場整體進展良好，主體結構已完成',
  })
  description: string;

  @ApiPropertyOptional({
    description: '進度百分比',
    example: 75,
  })
  progressPercentage?: number;

  @ApiPropertyOptional({
    description: '質量評分（1-10）',
    example: 8,
  })
  qualityScore?: number;

  @ApiProperty({
    description: '安全問題',
    example: ['工人未佩戴安全帽', '腳手架不穩固'],
  })
  safetyIssues: string[];

  @ApiProperty({
    description: '檢測到的設備',
    example: ['挖掘機', '混凝土攪拌車', '塔吊'],
  })
  equipmentDetected: string[];

  @ApiProperty({
    description: '建議',
    example: ['加強安全管理', '檢查設備狀態'],
  })
  recommendations: string[];
}

export class PhotoAnalysisResultDto {
  @ApiProperty({
    description: '照片 URL',
    example: 'https://example.com/photo.jpg',
  })
  photoUrl: string;

  @ApiProperty({
    description: '項目 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  projectId: string;

  @ApiProperty({
    description: '分析類型',
    example: 'progress',
  })
  analysisType: string;

  @ApiProperty({
    description: '分析發現',
    type: PhotoFindingsDto,
  })
  findings: PhotoFindingsDto;

  @ApiProperty({
    description: '信心度',
    example: 0.8,
  })
  confidence: number;

  @ApiProperty({
    description: '生成時間',
    example: '2024-01-01T12:00:00.000Z',
  })
  generatedAt: Date;
}

export class BottleneckDto {
  @ApiProperty({
    description: '區域',
    example: '任務分配',
  })
  area: string;

  @ApiProperty({
    description: '描述',
    example: '任務分配不均，部分成員工作量過大',
  })
  description: string;

  @ApiProperty({
    description: '影響程度',
    enum: ['low', 'medium', 'high'],
    example: 'high',
  })
  impact: 'low' | 'medium' | 'high';

  @ApiProperty({
    description: '建議',
    example: ['重新分配任務', '增加人力資源'],
  })
  suggestions: string[];
}

export class EfficiencyMetricsDto {
  @ApiProperty({
    description: '任務完成率',
    example: 85.5,
  })
  taskCompletionRate: number;

  @ApiProperty({
    description: '平均任務持續時間（小時）',
    example: 24.5,
  })
  averageTaskDuration: number;

  @ApiProperty({
    description: '資源利用率',
    example: 78.2,
  })
  resourceUtilization: number;
}

export class RecommendationDto {
  @ApiProperty({
    description: '優先級',
    enum: ['low', 'medium', 'high'],
    example: 'high',
  })
  priority: 'low' | 'medium' | 'high';

  @ApiProperty({
    description: '類別',
    example: '流程優化',
  })
  category: string;

  @ApiProperty({
    description: '描述',
    example: '實施看板管理系統',
  })
  description: string;

  @ApiProperty({
    description: '預期影響',
    example: '提升 20% 的工作效率',
  })
  expectedImpact: string;
}

export class WorkflowAnalysisDto {
  @ApiProperty({
    description: '瓶頸',
    type: [BottleneckDto],
  })
  bottlenecks: BottleneckDto[];

  @ApiProperty({
    description: '效率指標',
    type: EfficiencyMetricsDto,
  })
  efficiencyMetrics: EfficiencyMetricsDto;

  @ApiProperty({
    description: '建議',
    type: [RecommendationDto],
  })
  recommendations: RecommendationDto[];
}

export class WorkflowOptimizationResultDto {
  @ApiProperty({
    description: '租戶 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  tenantId: string;

  @ApiPropertyOptional({
    description: '項目 ID',
    example: 'clxxxxxxxxxxxxx',
  })
  projectId?: string;

  @ApiProperty({
    description: '分析結果',
    type: WorkflowAnalysisDto,
  })
  analysis: WorkflowAnalysisDto;

  @ApiProperty({
    description: '信心度',
    example: 0.8,
  })
  confidence: number;

  @ApiProperty({
    description: '生成時間',
    example: '2024-01-01T12:00:00.000Z',
  })
  generatedAt: Date;
}
