import { Controller, Post, Body, UseGuards, Request, HttpStatus, HttpCode } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { AiBusinessIntegrationService } from '@/modules/admin/ai/integrations/ai-business-integration.service';
import {
  ProjectAnalysisRequest,
  ProjectAnalysisResult,
  PhotoAnalysisRequest,
  PhotoAnalysisResult,
  WorkflowOptimizationRequest,
  WorkflowOptimizationResult,
} from '@/modules/admin/ai/integrations/types/business.types';
import {
  ProjectAnalysisRequestDto,
  ProjectAnalysisResultDto,
  PhotoAnalysisRequestDto,
  PhotoAnalysisResultDto,
  WorkflowOptimizationRequestDto,
  WorkflowOptimizationResultDto,
} from './dto/ai-business-integration.dto';

@ApiTags('AI Business Integration')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('ai/business-integration')
export class AiBusinessIntegrationController {
  constructor(private readonly aiBusinessIntegrationService: AiBusinessIntegrationService) {}

  @Post('analyze-project')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '分析項目',
    description: '使用 AI 分析項目狀態、風險、性能或優化建議',
  })
  @ApiResponse({
    status: 200,
    description: '項目分析結果',
    type: ProjectAnalysisResultDto,
  })
  async analyzeProject(
    @Body() request: ProjectAnalysisRequestDto,
    @Request() req: any,
  ): Promise<ProjectAnalysisResult> {
    // 確保使用當前用戶的租戶 ID
    const tenantId = req.user.tenant_id;

    return await this.aiBusinessIntegrationService.analyzeProject({
      project_id: request.projectId,
      tenant_id: tenantId,
      analysis_type: request.analysisType,
      include_sub_projects: request.includeSubProjects,
      include_tasks: request.includeTasks,
      include_progress: request.includeProgress,
      confidence: request.confidence,
    });
  }

  @Post('analyze-photo')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '分析項目照片',
    description: '使用 AI 視覺分析項目照片中的進度、質量、安全或設備信息',
  })
  @ApiResponse({
    status: 200,
    description: '照片分析結果',
    type: PhotoAnalysisResultDto,
  })
  async analyzePhoto(
    @Body() request: PhotoAnalysisRequestDto,
    @Request() req: any,
  ): Promise<PhotoAnalysisResult> {
    // 確保使用當前用戶的租戶 ID
    const tenantId = req.user.tenant_id;

    return await this.aiBusinessIntegrationService.analyzePhoto({
      photo_url: request.photoUrl,
      project_id: request.projectId,
      tenant_id: tenantId,
      analysis_type: request.analysisType,
      context: request.context,
      confidence: request.confidence,
    });
  }

  @Post('optimize-workflow')
  @HttpCode(HttpStatus.OK)
  @ApiOperation({
    summary: '優化工作流程',
    description: '使用 AI 分析工作流程並提供優化建議',
  })
  @ApiResponse({
    status: 200,
    description: '工作流程優化結果',
    type: WorkflowOptimizationResultDto,
  })
  async optimizeWorkflow(
    @Body() request: WorkflowOptimizationRequestDto,
    @Request() req: any,
  ): Promise<WorkflowOptimizationResult> {
    // 確保使用當前用戶的租戶 ID
    const tenantId = req.user.tenant_id;

    return await this.aiBusinessIntegrationService.optimizeWorkflow({
      tenant_id: tenantId,
      project_id: request.projectId,
      time_range: {
        start_date: new Date(request.startDate),
        end_date: new Date(request.endDate),
      },
      include_metrics: request.includeMetrics || [],
      confidence: request.confidence,
    });
  }
}
