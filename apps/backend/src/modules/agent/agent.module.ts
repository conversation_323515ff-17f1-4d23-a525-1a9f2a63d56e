import { Module } from '@nestjs/common';
import { PrismaModule } from '../core/prisma/prisma.module';
import { AiModule } from '../admin/ai/ai.module';
import { ProjectsModule } from '../workspace/projects/projects.module';
import { AgentRunnerService } from './agent.service';
import { AgentController } from './agent.controller';
import { ProjectInfoToolFactory } from './tools/project-info-tool.factory';

/**
 * Agent 模組
 *
 * 負責管理 LangChain agents 的核心功能，包括：
 * - Agent 初始化和執行
 * - 工具集成和管理
 * - 多租戶隔離
 *
 * 依賴模組：
 * - PrismaModule: 資料庫存取
 * - AiModule: AI 模型和金鑰管理
 * - ProjectsModule: 專案資料存取
 */
@Module({
  imports: [
    PrismaModule,
    AiModule, // 提供 AiModelsService, AiKeysService 等
    ProjectsModule, // 提供 ProjectsService
  ],
  controllers: [
    AgentController,
  ],
  providers: [
    AgentRunnerService,
    ProjectInfoToolFactory,
  ],
  exports: [
    AgentRunnerService,
    ProjectInfoToolFactory,
  ],
})
export class AgentModule {}
