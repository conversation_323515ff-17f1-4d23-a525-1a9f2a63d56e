import { Module } from '@nestjs/common';
import { PrismaModule } from '../core/prisma/prisma.module';
import { AiModule } from '../admin/ai/ai.module';
import { AgentRunnerService } from './agent.service';
import { AgentController } from './agent.controller';

/**
 * Agent 模組
 *
 * 負責管理 LangChain agents 的核心功能，包括：
 * - Agent 初始化和執行
 * - 工具集成和管理
 * - 多租戶隔離
 *
 * 依賴模組：
 * - PrismaModule: 資料庫存取
 * - AiModule: AI 模型和金鑰管理
 */
@Module({
  imports: [
    PrismaModule,
    AiModule, // 提供 AiModelsService, AiKeysService 等
  ],
  controllers: [
    AgentController,
  ],
  providers: [
    AgentRunnerService,
  ],
  exports: [
    AgentRunnerService,
  ],
})
export class AgentModule {}
