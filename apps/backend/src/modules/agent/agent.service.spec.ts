import { Test, TestingModule } from '@nestjs/testing';
import { AgentRunnerService } from './agent.service';
import { PrismaService } from '../core/prisma/prisma.service';
import { AiModelsService } from '../admin/ai/configuration/models/ai-models.service';
import { AiKeysService } from '../admin/ai/configuration/keys/ai-keys.service';
import { BadRequestException } from '@nestjs/common';

describe('AgentRunnerService', () => {
  let service: AgentRunnerService;
  let prismaService: jest.Mocked<PrismaService>;
  let aiModelsService: jest.Mocked<AiModelsService>;
  let aiKeysService: jest.Mocked<AiKeysService>;

  beforeEach(async () => {
    const mockPrismaService = {
      $queryRaw: jest.fn(),
    };

    const mockAiModelsService = {
      findOne: jest.fn(),
    };

    const mockAiKeysService = {
      findOne: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AgentRunnerService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: AiModelsService,
          useValue: mockAiModelsService,
        },
        {
          provide: AiKeysService,
          useValue: mockAiKeysService,
        },
      ],
    }).compile();

    service = module.get<AgentRunnerService>(AgentRunnerService);
    prismaService = module.get(PrismaService);
    aiModelsService = module.get(AiModelsService);
    aiKeysService = module.get(AiKeysService);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('runAgent', () => {
    it('should successfully run agent with valid input', async () => {
      const userInput = 'Hello, how can you help me?';
      const tenantId = 'test-tenant-id';

      const result = await service.runAgent(userInput, tenantId);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
      expect(result).toContain(userInput);
      expect(result).toContain('placeholder response');
    });

    it('should throw BadRequestException for empty user input', async () => {
      const userInput = '';
      const tenantId = 'test-tenant-id';

      await expect(service.runAgent(userInput, tenantId)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should throw BadRequestException for empty tenant ID', async () => {
      const userInput = 'Hello, how can you help me?';
      const tenantId = '';

      await expect(service.runAgent(userInput, tenantId)).rejects.toThrow(
        BadRequestException,
      );
    });

    it('should handle agent config ID parameter', async () => {
      const userInput = 'Hello, how can you help me?';
      const tenantId = 'test-tenant-id';
      const agentConfigId = 'custom-agent-config';

      const result = await service.runAgent(userInput, tenantId, agentConfigId);

      expect(result).toBeDefined();
      expect(typeof result).toBe('string');
    });
  });

  describe('getAgentStatus', () => {
    it('should return healthy status when database connection is active', async () => {
      prismaService.$queryRaw.mockResolvedValue([{ '?column?': 1 }]);

      const status = await service.getAgentStatus();

      expect(status).toEqual({
        status: 'healthy',
        message: 'Agent service is running and database connection is active',
      });
    });

    it('should return unhealthy status when database connection fails', async () => {
      const error = new Error('Database connection failed');
      prismaService.$queryRaw.mockRejectedValue(error);

      const status = await service.getAgentStatus();

      expect(status).toEqual({
        status: 'unhealthy',
        message: 'Agent service error: Database connection failed',
      });
    });
  });
});
