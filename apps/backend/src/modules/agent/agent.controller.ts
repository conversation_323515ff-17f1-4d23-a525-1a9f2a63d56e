import { Controller, Post, Get, Body, UseGuards, Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AgentRunnerService } from './agent.service';
import { JwtAuthGuard } from '../core/auth/guards/auth.guard';
import { Request } from 'express';

/**
 * Agent 控制器
 * 
 * 提供 Agent 執行和管理的 API 端點
 */
@ApiTags('Agent')
@ApiBearerAuth()
@Controller('agent')
@UseGuards(JwtAuthGuard)
export class AgentController {
  constructor(private readonly agentRunnerService: AgentRunnerService) {}

  /**
   * 執行 Agent
   */
  @Post('run')
  @ApiOperation({ 
    summary: '執行 Agent',
    description: '使用指定的輸入執行 LangChain agent，返回處理結果'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Agent 執行成功',
    schema: {
      type: 'object',
      properties: {
        result: { type: 'string', description: 'Agent 執行結果' },
        executionTime: { type: 'number', description: '執行時間（毫秒）' }
      }
    }
  })
  @ApiResponse({ status: 400, description: '輸入參數無效' })
  @ApiResponse({ status: 401, description: '未授權' })
  @ApiResponse({ status: 500, description: '內部伺服器錯誤' })
  async runAgent(
    @Body() body: { input: string; agentConfigId?: string },
    @Req() req: Request,
  ) {
    const startTime = Date.now();
    
    // 從 JWT token 中獲取租戶 ID
    const user = req.user as any;
    const tenantId = user.tenantId || user.tenant_id;
    
    if (!tenantId) {
      throw new Error('Tenant ID not found in user context');
    }

    const result = await this.agentRunnerService.runAgent(
      body.input,
      tenantId,
      body.agentConfigId,
    );

    const executionTime = Date.now() - startTime;

    return {
      result,
      executionTime,
    };
  }

  /**
   * 獲取 Agent 狀態
   */
  @Get('status')
  @ApiOperation({ 
    summary: '獲取 Agent 狀態',
    description: '檢查 Agent 服務的健康狀態'
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Agent 狀態資訊',
    schema: {
      type: 'object',
      properties: {
        status: { type: 'string', enum: ['healthy', 'unhealthy'] },
        message: { type: 'string', description: '狀態描述' }
      }
    }
  })
  async getAgentStatus() {
    return this.agentRunnerService.getAgentStatus();
  }
}
