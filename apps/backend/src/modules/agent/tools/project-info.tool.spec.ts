import { Test, TestingModule } from '@nestjs/testing';
import { ProjectInfoTool } from './project-info.tool';
import { ProjectsService } from '../../workspace/projects/projects.service';

describe('ProjectInfoTool', () => {
  let tool: ProjectInfoTool;
  let projectsService: jest.Mocked<ProjectsService>;

  const mockTenantId = 'test-tenant-id';
  const mockProjectId = 'clabcdefghijklmnopqrstuvwx';
  const mockProject = {
    id: mockProjectId,
    name: '測試專案',
    description: '這是一個測試專案',
    status: 'active',
    priority: 'high',
    startDate: new Date('2024-01-01'),
    endDate: new Date('2024-12-31'),
    budget: 100000,
    tenantId: mockTenantId,
    userId: 'test-user-id',
    level: 0,
    path: undefined,
    created_at: new Date('2024-01-01'),
    updated_at: new Date('2024-01-01'),
    parentProject: undefined,
    subProjects: [],
  };

  // 擴展的 mock 物件，包含 ProjectInfoTool 需要的額外屬性
  const mockProjectWithDetails = {
    ...mockProject,
    tasks: [
      {
        id: 'task-1',
        title: '測試任務',
        status: 'pending',
        priority: 'medium',
        due_date: new Date('2024-06-01'),
        assignee_id: 'user-1',
      },
    ],
    progressEntries: [
      {
        id: 'progress-1',
        title: '進度更新',
        progress_type: 'milestone',
        progress_value: 50,
        recordedAt: new Date('2024-03-01'),
      },
    ],
    project_milestones: [],
  };

  beforeEach(async () => {
    const mockProjectsService = {
      findOne: jest.fn(),
      findAll: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: ProjectsService,
          useValue: mockProjectsService,
        },
      ],
    }).compile();

    projectsService = module.get<ProjectsService>(ProjectsService) as jest.Mocked<ProjectsService>;
    tool = new ProjectInfoTool(projectsService, mockTenantId);
  });

  it('should be defined', () => {
    expect(tool).toBeDefined();
    expect(tool.name).toBe('ProjectInfoTool');
    expect(tool.description).toContain('查詢專案資訊的工具');
  });

  describe('_call', () => {
    it('should query project by ID', async () => {
      projectsService.findOne.mockResolvedValue(mockProjectWithDetails);

      const result = await tool._call(mockProjectId);

      expect(projectsService.findOne).toHaveBeenCalledWith(mockProjectId, mockTenantId);
      expect(result).toContain('=== 專案詳細資訊 ===');
      expect(result).toContain('測試專案');
      expect(result).toContain('進行中');
      expect(result).toContain('高');
    });

    it('should search projects by name', async () => {
      const mockSearchResult = {
        projects: [mockProject],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      projectsService.findAll.mockResolvedValue(mockSearchResult);
      projectsService.findOne.mockResolvedValue(mockProjectWithDetails);

      const result = await tool._call('測試專案');

      expect(projectsService.findAll).toHaveBeenCalledWith(
        mockTenantId,
        1,
        10,
        '測試專案',
      );
      expect(result).toContain('=== 專案詳細資訊 ===');
    });

    it('should filter projects by status', async () => {
      const mockFilterResult = {
        projects: [mockProject],
        total: 1,
        page: 1,
        limit: 20,
        totalPages: 1,
      };

      projectsService.findAll.mockResolvedValue(mockFilterResult);

      const result = await tool._call('status:active');

      expect(projectsService.findAll).toHaveBeenCalledWith(
        mockTenantId,
        1,
        20,
        undefined,
        'active',
        undefined,
      );
      expect(result).toContain('篩選結果');
    });

    it('should return all projects overview when no specific input', async () => {
      const mockAllResult = {
        projects: [mockProject],
        total: 1,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      projectsService.findAll.mockResolvedValue(mockAllResult);

      const result = await tool._call('');

      expect(projectsService.findAll).toHaveBeenCalledWith(mockTenantId, 1, 10);
      expect(result).toContain('專案概覽');
    });

    it('should handle project not found error', async () => {
      projectsService.findOne.mockRejectedValue(new Error('Project not found'));

      const result = await tool._call(mockProjectId);

      expect(result).toContain('找不到 ID 為');
      expect(result).toContain('的專案');
    });

    it('should handle search with no results', async () => {
      const mockEmptyResult = {
        projects: [],
        total: 0,
        page: 1,
        limit: 10,
        totalPages: 0,
      };

      projectsService.findAll.mockResolvedValue(mockEmptyResult);

      const result = await tool._call('不存在的專案');

      expect(result).toContain('找不到名稱包含');
    });

    it('should handle multiple search results', async () => {
      const mockMultipleResult = {
        projects: [
          mockProject,
          {
            ...mockProject,
            id: 'project-2',
            name: '另一個測試專案',
            tenantId: mockTenantId,
            userId: 'test-user-id',
            level: 0,
          }
        ],
        total: 2,
        page: 1,
        limit: 10,
        totalPages: 1,
      };

      projectsService.findAll.mockResolvedValue(mockMultipleResult);

      const result = await tool._call('測試');

      expect(result).toContain('搜尋 "測試" 的結果');
      expect(result).toContain('總計: 2 個專案');
    });

    it('should handle filter with no results', async () => {
      const mockEmptyFilterResult = {
        projects: [],
        total: 0,
        page: 1,
        limit: 20,
        totalPages: 0,
      };

      projectsService.findAll.mockResolvedValue(mockEmptyFilterResult);

      const result = await tool._call('status:completed');

      expect(result).toContain('找不到符合條件');
      expect(result).toContain('status=completed');
    });

    it('should handle service errors gracefully', async () => {
      projectsService.findAll.mockRejectedValue(new Error('Database connection failed'));

      const result = await tool._call('');

      expect(result).toContain('獲取專案概覽時發生錯誤');
      expect(result).toContain('Database connection failed');
    });
  });

  describe('parseInput', () => {
    it('should parse CUID as project ID', () => {
      const parseInput = (tool as any).parseInput.bind(tool);
      const result = parseInput('clabcdefghijklmnopqrstuvwx');
      expect(result.projectId).toBe('clabcdefghijklmnopqrstuvwx');
    });

    it('should parse filter conditions', () => {
      const parseInput = (tool as any).parseInput.bind(tool);
      const result = parseInput('status:active,priority:high');
      expect(result.filters).toEqual({ status: 'active', priority: 'high' });
    });

    it('should parse regular text as project name', () => {
      const parseInput = (tool as any).parseInput.bind(tool);
      const result = parseInput('我的專案');
      expect(result.projectName).toBe('我的專案');
    });
  });

  describe('formatProjectDetails', () => {
    it('should format project details correctly', () => {
      const formatProjectDetails = (tool as any).formatProjectDetails.bind(tool);
      const result = formatProjectDetails(mockProjectWithDetails);

      expect(result).toContain('=== 專案詳細資訊 ===');
      expect(result).toContain('專案名稱: 測試專案');
      expect(result).toContain('狀態: 進行中');
      expect(result).toContain('優先級: 高');
      expect(result).toContain('預算: NT$ 100,000');
      expect(result).toContain('任務 (1 個):');
      expect(result).toContain('最近進度記錄:');
    });
  });

  describe('translateStatus', () => {
    it('should translate status correctly', () => {
      const translateStatus = (tool as any).translateStatus.bind(tool);
      expect(translateStatus('planning')).toBe('規劃中');
      expect(translateStatus('active')).toBe('進行中');
      expect(translateStatus('on-hold')).toBe('暫停');
      expect(translateStatus('completed')).toBe('已完成');
      expect(translateStatus('cancelled')).toBe('已取消');
      expect(translateStatus('unknown')).toBe('unknown');
    });
  });

  describe('translatePriority', () => {
    it('should translate priority correctly', () => {
      const translatePriority = (tool as any).translatePriority.bind(tool);
      expect(translatePriority('low')).toBe('低');
      expect(translatePriority('medium')).toBe('中');
      expect(translatePriority('high')).toBe('高');
      expect(translatePriority('urgent')).toBe('緊急');
      expect(translatePriority('unknown')).toBe('unknown');
    });
  });
});
