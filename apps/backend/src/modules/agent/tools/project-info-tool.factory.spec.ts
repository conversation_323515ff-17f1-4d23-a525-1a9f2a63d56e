import { Test, TestingModule } from '@nestjs/testing';
import { ProjectInfoToolFactory } from './project-info-tool.factory';
import { ProjectsService } from '../../workspace/projects/projects.service';
import { ProjectInfoTool } from './project-info.tool';

describe('ProjectInfoToolFactory', () => {
  let factory: ProjectInfoToolFactory;
  let projectsService: jest.Mocked<ProjectsService>;

  beforeEach(async () => {
    const mockProjectsService = {
      findOne: jest.fn(),
      findAll: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ProjectInfoToolFactory,
        {
          provide: ProjectsService,
          useValue: mockProjectsService,
        },
      ],
    }).compile();

    factory = module.get<ProjectInfoToolFactory>(ProjectInfoToolFactory);
    projectsService = module.get<ProjectsService>(ProjectsService) as jest.Mocked<ProjectsService>;
  });

  it('should be defined', () => {
    expect(factory).toBeDefined();
  });

  describe('create', () => {
    it('should create ProjectInfoTool instance with correct dependencies', () => {
      const tenantId = 'test-tenant-id';
      
      const tool = factory.create(tenantId);

      expect(tool).toBeInstanceOf(ProjectInfoTool);
      expect(tool.name).toBe('ProjectInfoTool');
      expect(tool.description).toContain('查詢專案資訊的工具');
    });

    it('should create different instances for different tenants', () => {
      const tenantId1 = 'tenant-1';
      const tenantId2 = 'tenant-2';
      
      const tool1 = factory.create(tenantId1);
      const tool2 = factory.create(tenantId2);

      expect(tool1).toBeInstanceOf(ProjectInfoTool);
      expect(tool2).toBeInstanceOf(ProjectInfoTool);
      expect(tool1).not.toBe(tool2); // Different instances
    });

    it('should inject ProjectsService correctly', () => {
      const tenantId = 'test-tenant-id';
      
      const tool = factory.create(tenantId);

      // Verify that the tool can access the injected service
      expect(tool).toBeDefined();
      expect((tool as any).projectsService).toBe(projectsService);
      expect((tool as any).tenantId).toBe(tenantId);
    });
  });
});
