import { Injectable, Logger } from '@nestjs/common';
import { Tool } from 'langchain/tools';
import { ProjectsService } from '../../workspace/projects/projects.service';

/**
 * ProjectInfoTool - LangChain Tool for querying project information
 * 
 * 此工具允許 Agent 查詢專案狀態和詳細資訊，透過封裝現有的 ProjectsService 功能。
 * 支援多租戶隔離，確保只能存取所屬租戶的專案資料。
 */
@Injectable()
export class ProjectInfoTool extends Tool {
  name = 'ProjectInfoTool';
  description = `查詢專案資訊的工具。可以根據專案名稱或 ID 查詢專案的詳細資訊，包括狀態、進度、任務等。
輸入格式：
- 專案 ID（如："clxxxxxxxxxxxxx"）
- 專案名稱（如："新產品開發專案"）
- 查詢條件（如："status:active" 或 "priority:high"）

回傳專案的完整資訊，包括基本資料、階層關係、任務清單、進度記錄等。`;

  private readonly logger = new Logger(ProjectInfoTool.name);

  constructor(
    private readonly projectsService: ProjectsService,
    private readonly tenantId: string,
  ) {
    super();
  }

  /**
   * 執行專案資訊查詢
   * 
   * @param input 查詢輸入（專案 ID、名稱或查詢條件）
   * @returns 專案資訊的字串格式
   */
  async _call(input: string): Promise<string> {
    this.logger.debug(`ProjectInfoTool called with input: ${input}`);

    try {
      // 解析輸入參數
      const queryParams = this.parseInput(input);
      
      if (queryParams.projectId) {
        // 根據 ID 查詢單一專案
        return await this.getProjectById(queryParams.projectId);
      } else if (queryParams.projectName) {
        // 根據名稱搜尋專案
        return await this.searchProjectsByName(queryParams.projectName);
      } else if (queryParams.filters) {
        // 根據條件篩選專案
        return await this.filterProjects(queryParams.filters);
      } else {
        // 預設返回所有專案概覽
        return await this.getAllProjectsOverview();
      }
    } catch (error) {
      this.logger.error(`ProjectInfoTool execution failed: ${error.message}`, error.stack);
      return `查詢專案資訊時發生錯誤: ${error.message}`;
    }
  }

  /**
   * 解析輸入參數
   */
  private parseInput(input: string): {
    projectId?: string;
    projectName?: string;
    filters?: Record<string, string>;
  } {
    const trimmedInput = input.trim();

    // 檢查是否為 CUID 格式的 ID
    if (/^cl[a-z0-9]{24}$/i.test(trimmedInput)) {
      return { projectId: trimmedInput };
    }

    // 檢查是否為查詢條件格式（如 "status:active"）
    if (trimmedInput.includes(':')) {
      const filters: Record<string, string> = {};
      const conditions = trimmedInput.split(',').map(c => c.trim());
      
      for (const condition of conditions) {
        const [key, value] = condition.split(':').map(s => s.trim());
        if (key && value) {
          filters[key] = value;
        }
      }
      
      if (Object.keys(filters).length > 0) {
        return { filters };
      }
    }

    // 否則視為專案名稱
    return { projectName: trimmedInput };
  }

  /**
   * 根據 ID 獲取專案詳細資訊
   */
  private async getProjectById(projectId: string): Promise<string> {
    try {
      const project = await this.projectsService.findOne(projectId, this.tenantId);
      return this.formatProjectDetails(project);
    } catch (error) {
      return `找不到 ID 為 "${projectId}" 的專案，或您沒有存取權限。`;
    }
  }

  /**
   * 根據名稱搜尋專案
   */
  private async searchProjectsByName(projectName: string): Promise<string> {
    try {
      const result = await this.projectsService.findAll(
        this.tenantId,
        1, // page
        10, // limit
        projectName, // search
      );

      if (result.projects.length === 0) {
        return `找不到名稱包含 "${projectName}" 的專案。`;
      }

      if (result.projects.length === 1) {
        // 如果只有一個結果，返回詳細資訊
        const project = await this.projectsService.findOne(result.projects[0].id, this.tenantId);
        return this.formatProjectDetails(project);
      }

      // 如果有多個結果，返回列表
      return this.formatProjectList(result.projects, `搜尋 "${projectName}" 的結果`);
    } catch (error) {
      return `搜尋專案時發生錯誤: ${error.message}`;
    }
  }

  /**
   * 根據條件篩選專案
   */
  private async filterProjects(filters: Record<string, string>): Promise<string> {
    try {
      const result = await this.projectsService.findAll(
        this.tenantId,
        1, // page
        20, // limit
        undefined, // search
        filters.status,
        filters.priority,
      );

      if (result.projects.length === 0) {
        const filterDesc = Object.entries(filters)
          .map(([key, value]) => `${key}=${value}`)
          .join(', ');
        return `找不到符合條件 (${filterDesc}) 的專案。`;
      }

      return this.formatProjectList(result.projects, '篩選結果');
    } catch (error) {
      return `篩選專案時發生錯誤: ${error.message}`;
    }
  }

  /**
   * 獲取所有專案概覽
   */
  private async getAllProjectsOverview(): Promise<string> {
    try {
      const result = await this.projectsService.findAll(this.tenantId, 1, 10);
      
      if (result.projects.length === 0) {
        return '目前沒有任何專案。';
      }

      return this.formatProjectList(result.projects, '專案概覽');
    } catch (error) {
      return `獲取專案概覽時發生錯誤: ${error.message}`;
    }
  }

  /**
   * 格式化專案詳細資訊
   */
  private formatProjectDetails(project: any): string {
    const sections = [
      `=== 專案詳細資訊 ===`,
      `專案名稱: ${project.name}`,
      `專案 ID: ${project.id}`,
      `狀態: ${this.translateStatus(project.status)}`,
      `優先級: ${this.translatePriority(project.priority)}`,
    ];

    if (project.description) {
      sections.push(`描述: ${project.description}`);
    }

    if (project.startDate) {
      sections.push(`開始日期: ${new Date(project.startDate).toLocaleDateString('zh-TW')}`);
    }

    if (project.endDate) {
      sections.push(`結束日期: ${new Date(project.endDate).toLocaleDateString('zh-TW')}`);
    }

    if (project.budget) {
      sections.push(`預算: NT$ ${project.budget.toLocaleString()}`);
    }

    // 階層資訊
    if (project.parentProject) {
      sections.push(`父專案: ${project.parentProject.name} (${project.parentProject.status})`);
    }

    if (project.subProjects && project.subProjects.length > 0) {
      sections.push(`子專案 (${project.subProjects.length} 個):`);
      project.subProjects.forEach((sub: any) => {
        sections.push(`  - ${sub.name} (${this.translateStatus(sub.status)})`);
      });
    }

    // 任務資訊
    if (project.tasks && project.tasks.length > 0) {
      sections.push(`任務 (${project.tasks.length} 個):`);
      project.tasks.slice(0, 5).forEach((task: any) => {
        sections.push(`  - ${task.title} (${this.translateStatus(task.status)})`);
      });
      if (project.tasks.length > 5) {
        sections.push(`  ... 還有 ${project.tasks.length - 5} 個任務`);
      }
    }

    // 進度記錄
    if (project.progressEntries && project.progressEntries.length > 0) {
      sections.push(`最近進度記錄:`);
      project.progressEntries.slice(0, 3).forEach((entry: any) => {
        const date = new Date(entry.recordedAt).toLocaleDateString('zh-TW');
        sections.push(`  - ${entry.title} (${date})`);
      });
    }

    sections.push(`創建時間: ${new Date(project.created_at).toLocaleDateString('zh-TW')}`);
    sections.push(`最後更新: ${new Date(project.updated_at).toLocaleDateString('zh-TW')}`);

    return sections.join('\n');
  }

  /**
   * 格式化專案列表
   */
  private formatProjectList(projects: any[], title: string): string {
    const sections = [`=== ${title} ===`];
    
    projects.forEach((project, index) => {
      sections.push(
        `${index + 1}. ${project.name} (ID: ${project.id})`,
        `   狀態: ${this.translateStatus(project.status)} | 優先級: ${this.translatePriority(project.priority)}`,
      );
      
      if (project.description) {
        const shortDesc = project.description.length > 50 
          ? project.description.substring(0, 50) + '...' 
          : project.description;
        sections.push(`   描述: ${shortDesc}`);
      }
      
      sections.push(''); // 空行分隔
    });

    sections.push(`總計: ${projects.length} 個專案`);
    return sections.join('\n');
  }

  /**
   * 翻譯狀態
   */
  private translateStatus(status: string): string {
    const statusMap: Record<string, string> = {
      'planning': '規劃中',
      'active': '進行中',
      'on-hold': '暫停',
      'completed': '已完成',
      'cancelled': '已取消',
    };
    return statusMap[status] || status;
  }

  /**
   * 翻譯優先級
   */
  private translatePriority(priority: string): string {
    const priorityMap: Record<string, string> = {
      'low': '低',
      'medium': '中',
      'high': '高',
      'urgent': '緊急',
    };
    return priorityMap[priority] || priority;
  }
}
