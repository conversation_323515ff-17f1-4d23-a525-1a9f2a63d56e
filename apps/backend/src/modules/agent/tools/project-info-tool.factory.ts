import { Injectable } from '@nestjs/common';
import { ProjectsService } from '../../workspace/projects/projects.service';
import { ProjectInfoTool } from './project-info.tool';

/**
 * ProjectInfoTool 工廠服務
 * 
 * 負責創建 ProjectInfoTool 實例，並注入必要的依賴項和租戶上下文。
 * 這是必要的，因為 LangChain Tool 需要在運行時動態創建，並且需要租戶隔離。
 */
@Injectable()
export class ProjectInfoToolFactory {
  constructor(private readonly projectsService: ProjectsService) {}

  /**
   * 創建 ProjectInfoTool 實例
   * 
   * @param tenantId 租戶 ID，用於多租戶隔離
   * @returns ProjectInfoTool 實例
   */
  create(tenantId: string): ProjectInfoTool {
    return new ProjectInfoTool(this.projectsService, tenantId);
  }
}
