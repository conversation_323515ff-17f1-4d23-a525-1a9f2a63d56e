# Agent 模組

## 概述

Agent 模組是 HorizAI SaaS 平台的核心 AI 代理系統，負責管理和執行 LangChain agents。此模組提供了一個統一的介面來初始化、配置和執行各種 AI 代理任務。

## 架構

```
apps/backend/src/modules/agent/
├── agent.module.ts          # Agent 模組定義
├── agent.service.ts         # AgentRunnerService 核心服務
├── agent.controller.ts      # API 控制器
├── agent.service.spec.ts    # 單元測試
├── tools/                   # 工具目錄（待建立）
│   ├── project-info.tool.ts
│   └── knowledge-base.tool.ts
└── README.md               # 本文檔
```

## 核心功能

### AgentRunnerService

主要的 Agent 執行服務，提供以下功能：

#### 主要方法

- **`runAgent(userInput, tenantId, agentConfigId?)`**: 執行 Agent 的主要方法
- **`getAgentStatus()`**: 健康檢查和狀態監控
- **`validateInput()`**: 輸入參數驗證
- **`getAgentConfig()`**: Agent 配置管理
- **`initializeLLM()`**: LLM 初始化
- **`initializeTools()`**: 工具集初始化
- **`executeAgent()`**: Agent 執行邏輯

#### 特性

- ✅ **多租戶隔離**: 所有操作都基於 tenantId 進行隔離
- ✅ **錯誤處理**: 完整的錯誤處理和日誌記錄
- ✅ **輸入驗證**: 嚴格的輸入參數驗證
- ✅ **健康檢查**: 提供服務狀態監控
- 🚧 **LLM 整合**: 目前為佔位符實作，待後續完成
- 🚧 **工具系統**: 目前為佔位符實作，待後續完成

### API 端點

#### POST /agent/run
執行 Agent 並返回結果

**請求體:**
```json
{
  "input": "使用者輸入文字",
  "agentConfigId": "可選的配置ID"
}
```

**回應:**
```json
{
  "result": "Agent 執行結果",
  "executionTime": 1234
}
```

#### GET /agent/status
獲取 Agent 服務狀態

**回應:**
```json
{
  "status": "healthy|unhealthy",
  "message": "狀態描述"
}
```

## 依賴關係

- **PrismaModule**: 資料庫存取
- **AiModule**: AI 模型和金鑰管理
- **JwtAuthGuard**: 身份驗證

## 測試

### 單元測試

執行測試：
```bash
npm test -- --testPathPattern=agent.service.spec.ts
```

測試涵蓋：
- ✅ 服務實例化
- ✅ 正常 Agent 執行流程
- ✅ 輸入驗證錯誤處理
- ✅ 健康檢查功能
- ✅ 配置參數處理

### 測試結果
```
✓ should be defined
✓ should successfully run agent with valid input
✓ should throw BadRequestException for empty user input
✓ should throw BadRequestException for empty tenant ID
✓ should handle agent config ID parameter
✓ should return healthy status when database connection is active
✓ should return unhealthy status when database connection fails

Test Suites: 1 passed, 1 total
Tests: 7 passed, 7 total
```

## 使用方式

### 基本使用

```typescript
import { AgentRunnerService } from './agent.service';

// 注入服務
constructor(private readonly agentService: AgentRunnerService) {}

// 執行 Agent
const result = await this.agentService.runAgent(
  'Hello, how can you help me?',
  'tenant-123',
  'optional-config-id'
);

// 檢查狀態
const status = await this.agentService.getAgentStatus();
```

### API 使用

```bash
# 執行 Agent
curl -X POST http://localhost:3000/agent/run \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "input": "Hello, how can you help me?",
    "agentConfigId": "optional-config-id"
  }'

# 檢查狀態
curl -X GET http://localhost:3000/agent/status \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 開發狀態

### ✅ 已完成
- [x] Agent 模組基礎架構
- [x] AgentRunnerService 核心服務
- [x] API 控制器和端點
- [x] 輸入驗證和錯誤處理
- [x] 多租戶隔離機制
- [x] 健康檢查功能
- [x] 完整的單元測試
- [x] Swagger API 文檔

### 🚧 進行中
- [ ] LLM 整合實作
- [ ] 工具系統開發
- [ ] Agent 執行邏輯
- [ ] 配置管理系統

### 📋 待辦事項
- [ ] ProjectInfoTool 開發
- [ ] KnowledgeBaseTool 開發
- [ ] Agent 配置資料庫設計
- [ ] 效能監控和日誌
- [ ] 快取機制實作

## 後續開發

下一步將開發 `ProjectInfoTool`，這是任務 #10 的內容。該工具將允許 Agent 查詢專案資訊和狀態。

## 貢獻指南

1. 所有新功能都應該包含單元測試
2. 遵循現有的程式碼風格和命名慣例
3. 確保多租戶隔離機制正確實作
4. 添加適當的錯誤處理和日誌記錄
5. 更新相關文檔

## 授權

此模組是 HorizAI SaaS 平台的一部分，遵循專案的整體授權條款。
