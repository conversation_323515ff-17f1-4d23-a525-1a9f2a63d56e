import { Injectable, Logger, BadRequestException, NotFoundException } from '@nestjs/common';
import { Tool } from 'langchain/tools';
import { PrismaService } from '../core/prisma/prisma.service';
import { AiModelsService } from '../admin/ai/configuration/models/ai-models.service';
import { AiKeysService } from '../admin/ai/configuration/keys/ai-keys.service';
import { ProjectInfoToolFactory } from './tools/project-info-tool.factory';

/**
 * Agent 執行器服務
 * 
 * 負責初始化和執行 LangChain agents，提供以下功能：
 * - Agent 配置管理
 * - LLM 初始化
 * - 工具集成
 * - 多租戶隔離
 * - 執行結果追蹤
 */
@Injectable()
export class AgentRunnerService {
  private readonly logger = new Logger(AgentRunnerService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly aiModelsService: AiModelsService,
    private readonly aiKeysService: AiKeysService,
    private readonly projectInfoToolFactory: ProjectInfoToolFactory,
  ) {}

  /**
   * 執行 Agent
   * 
   * @param userInput 使用者輸入
   * @param tenantId 租戶 ID（用於多租戶隔離）
   * @param agentConfigId 可選的 Agent 配置 ID
   * @returns Agent 執行結果
   */
  async runAgent(
    userInput: string,
    tenantId: string,
    agentConfigId?: string,
  ): Promise<string> {
    this.logger.log(`Starting agent execution for tenant: ${tenantId}`);
    this.logger.debug(`User input: ${userInput}`);
    this.logger.debug(`Agent config ID: ${agentConfigId || 'default'}`);

    try {
      // 驗證輸入參數
      this.validateInput(userInput, tenantId);

      // 獲取 Agent 配置
      const agentConfig = await this.getAgentConfig(tenantId, agentConfigId);
      this.logger.debug(`Using agent config: ${JSON.stringify(agentConfig)}`);

      // 初始化 LLM（目前為佔位符邏輯）
      const llmConfig = await this.initializeLLM(agentConfig);
      this.logger.debug(`LLM initialized with config: ${JSON.stringify(llmConfig)}`);

      // 初始化工具集（目前為佔位符邏輯）
      const tools = await this.initializeTools(tenantId);
      this.logger.debug(`Initialized ${tools.length} tools`);

      // 執行 Agent（目前為佔位符邏輯）
      const result = await this.executeAgent(userInput, llmConfig, tools);
      
      this.logger.log(`Agent execution completed successfully for tenant: ${tenantId}`);
      return result;

    } catch (error) {
      this.logger.error(
        `Agent execution failed for tenant ${tenantId}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 驗證輸入參數
   */
  private validateInput(userInput: string, tenantId: string): void {
    if (!userInput || userInput.trim().length === 0) {
      throw new BadRequestException('User input cannot be empty');
    }

    if (!tenantId || tenantId.trim().length === 0) {
      throw new BadRequestException('Tenant ID is required');
    }
  }

  /**
   * 獲取 Agent 配置
   * 
   * 目前返回預設配置，未來將從資料庫或配置檔案中讀取
   */
  private async getAgentConfig(tenantId: string, agentConfigId?: string) {
    // TODO: 實作從資料庫讀取 Agent 配置的邏輯
    // 目前返回預設配置作為佔位符
    
    this.logger.debug(`Fetching agent config for tenant: ${tenantId}, configId: ${agentConfigId}`);
    
    return {
      id: agentConfigId || 'default',
      name: 'Default Agent',
      description: 'Default LangChain agent configuration',
      modelId: null, // 將在後續任務中實作
      systemPrompt: 'You are a helpful AI assistant.',
      temperature: 0.7,
      maxTokens: 2000,
      tenantId,
    };
  }

  /**
   * 初始化 LLM
   * 
   * 目前為佔位符實作，未來將整合 AiModelsService 和 AiKeysService
   */
  private async initializeLLM(agentConfig: any) {
    // TODO: 實作 LLM 初始化邏輯
    // 1. 根據 agentConfig.modelId 獲取 AI 模型配置
    // 2. 獲取對應的 API 金鑰
    // 3. 初始化 LangChain LLM 實例
    
    this.logger.debug('Initializing LLM (placeholder implementation)');
    
    return {
      provider: 'openai', // 佔位符
      model: 'gpt-3.5-turbo', // 佔位符
      temperature: agentConfig.temperature,
      maxTokens: agentConfig.maxTokens,
    };
  }

  /**
   * 初始化工具集
   *
   * 載入實際的 LangChain 工具，包括 ProjectInfoTool 等
   */
  private async initializeTools(tenantId: string): Promise<Tool[]> {
    this.logger.debug(`Initializing tools for tenant: ${tenantId}`);

    const tools: Tool[] = [];

    try {
      // 1. 載入 ProjectInfoTool
      const projectInfoTool = this.projectInfoToolFactory.create(tenantId);
      tools.push(projectInfoTool);
      this.logger.debug('ProjectInfoTool initialized successfully');

      // 2. TODO: 載入 KnowledgeBaseTool
      // const knowledgeBaseTool = this.knowledgeBaseToolFactory.create(tenantId);
      // tools.push(knowledgeBaseTool);

      // 3. TODO: 其他租戶特定的工具

      this.logger.debug(`Initialized ${tools.length} tools for tenant: ${tenantId}`);
      return tools;

    } catch (error) {
      this.logger.error(`Failed to initialize tools for tenant ${tenantId}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 執行 Agent
   * 
   * 目前為佔位符實作，未來將使用 LangChain AgentExecutor
   */
  private async executeAgent(userInput: string, llmConfig: any, tools: Tool[]) {
    // TODO: 實作實際的 Agent 執行邏輯
    // 1. 創建 LangChain Agent
    // 2. 配置 AgentExecutor
    // 3. 執行並返回結果
    
    this.logger.debug('Executing agent (placeholder implementation)');
    
    // 佔位符回應，但現在顯示實際的工具資訊
    const toolNames = tools.map(tool => tool.name).join(', ');
    const response = `Agent received input: "${userInput}". ` +
      `Using LLM: ${llmConfig.provider}/${llmConfig.model}. ` +
      `Available tools: ${toolNames}. ` +
      `This is a placeholder response that will be replaced with actual LangChain agent execution.`;
    
    return response;
  }

  /**
   * 獲取 Agent 狀態
   * 
   * 用於健康檢查和監控
   */
  async getAgentStatus(): Promise<{ status: string; message: string }> {
    try {
      // 簡單的健康檢查
      await this.prisma.$queryRaw`SELECT 1`;
      
      return {
        status: 'healthy',
        message: 'Agent service is running and database connection is active',
      };
    } catch (error) {
      this.logger.error(`Agent status check failed: ${error.message}`);
      return {
        status: 'unhealthy',
        message: `Agent service error: ${error.message}`,
      };
    }
  }
}
