import { Module } from '@nestjs/common';
import { HttpModule } from '@nestjs/axios';
import { CaslModule } from '@/casl/casl.module';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { AiModelsController } from './configuration/models/ai-models.controller';
import { AiModelsService } from './configuration/models/ai-models.service';
import { AiKeysController } from './configuration/keys/ai-keys.controller';
import { AiKeysService } from './configuration/keys/ai-keys.service';
import { AiBotsController } from './bots/ai-bots.controller';
import { AiBotsService } from './bots/ai-bots.service';
import { AiSettingsController } from './configuration/settings/ai-settings.controller';
import { AiSettingsService } from './configuration/settings/ai-settings.service';
import { AiUsageController } from './configuration/usage/ai-usage.controller';
import { AiUsageService } from './configuration/usage/ai-usage.service';
import { AiBusinessIntegrationController } from './integrations/ai-business-integration.controller';
import { AiProviderFactory } from './core/providers/factory';
import { AiPriceCrawlerService } from './configuration/pricing/ai-price-crawler.service';
import { AiPricingService } from './configuration/pricing/ai-pricing.service';
import { TenantAiQuotaService } from './configuration/usage/tenant-ai-quota.service';
import { AiBusinessIntegrationService } from './integrations/ai-business-integration.service';
import { AiWorkflowsModule } from './workflows/ai-workflows.module';

@Module({
  imports: [HttpModule, CaslModule, PrismaModule, AuthModule, AiWorkflowsModule],
  controllers: [
    AiModelsController,
    AiKeysController,
    AiBotsController,
    AiSettingsController,
    AiUsageController,
    AiBusinessIntegrationController,
  ],
  providers: [
    AiModelsService,
    AiKeysService,
    AiBotsService,
    AiSettingsService,
    AiUsageService,
    AiProviderFactory,
    AiPriceCrawlerService,
    AiPricingService,
    TenantAiQuotaService,
    AiBusinessIntegrationService,
  ],
  exports: [
    AiModelsService,
    AiKeysService,
    AiBotsService,
    AiSettingsService,
    AiUsageService,
    AiProviderFactory,
    AiPriceCrawlerService,
    AiPricingService,
    TenantAiQuotaService,
    AiBusinessIntegrationService,
  ],
})
export class AiModule {}
