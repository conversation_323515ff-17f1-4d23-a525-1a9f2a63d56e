import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { EventEmitter2 } from '@nestjs/event-emitter';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { BaseAiException } from '../exceptions/ai-service.exceptions';

/**
 * 監控指標
 */
interface MetricData {
  timestamp: number;
  provider: string;
  model?: string;
  operation: string;
  duration: number;
  success: boolean;
  errorType?: string;
  errorMessage?: string;
  requestSize?: number;
  responseSize?: number;
  tokens?: {
    input: number;
    output: number;
    total: number;
  };
}

/**
 * 聚合統計
 */
interface AggregatedStats {
  provider: string;
  model?: string;
  timeWindow: string;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  successRate: number;
  averageResponseTime: number;
  p95ResponseTime: number;
  p99ResponseTime: number;
  totalTokens: number;
  averageTokensPerRequest: number;
  errorTypes: Record<string, number>;
  costEstimate: number;
}

/**
 * 警報配置
 */
interface AlertConfig {
  name: string;
  condition: 'error_rate' | 'response_time' | 'failure_count' | 'availability';
  threshold: number;
  timeWindow: number; // minutes
  enabled: boolean;
  severity: 'info' | 'warning' | 'error' | 'critical';
  cooldown: number; // minutes
}

/**
 * 警報事件
 */
interface AlertEvent {
  id: string;
  alertName: string;
  severity: string;
  message: string;
  provider: string;
  model?: string;
  currentValue: number;
  threshold: number;
  timestamp: number;
  acknowledged: boolean;
}

@Injectable()
export class AiMonitoringService {
  private readonly logger = new Logger(AiMonitoringService.name);
  private readonly metrics: MetricData[] = [];
  private readonly maxMetricsRetention = 10000; // 保留最近 10k 條記錄
  private readonly alertEvents: AlertEvent[] = [];
  private readonly lastAlertTimes = new Map<string, number>();

  // 預設警報配置
  private readonly defaultAlerts: AlertConfig[] = [
    {
      name: 'high_error_rate',
      condition: 'error_rate',
      threshold: 0.1, // 10% 錯誤率
      timeWindow: 5,
      enabled: true,
      severity: 'warning',
      cooldown: 15,
    },
    {
      name: 'critical_error_rate',
      condition: 'error_rate',
      threshold: 0.25, // 25% 錯誤率
      timeWindow: 5,
      enabled: true,
      severity: 'critical',
      cooldown: 10,
    },
    {
      name: 'slow_response_time',
      condition: 'response_time',
      threshold: 30000, // 30 秒
      timeWindow: 10,
      enabled: true,
      severity: 'warning',
      cooldown: 20,
    },
    {
      name: 'high_failure_count',
      condition: 'failure_count',
      threshold: 10, // 10 次失敗
      timeWindow: 5,
      enabled: true,
      severity: 'error',
      cooldown: 15,
    },
  ];

  constructor(
    private readonly configService: ConfigService,
    private readonly eventEmitter: EventEmitter2,
    private readonly prisma: PrismaService,
  ) {
    // 定期清理舊指標
    setInterval(() => this.cleanupOldMetrics(), 300000); // 每 5 分鐘清理一次

    // 定期檢查警報條件
    setInterval(() => this.checkAlerts(), 60000); // 每分鐘檢查一次
  }

  /**
   * 記錄操作指標
   */
  recordMetric(
    provider: string,
    operation: string,
    duration: number,
    success: boolean,
    options?: {
      model?: string;
      error?: BaseAiException;
      requestSize?: number;
      responseSize?: number;
      tokens?: { input: number; output: number };
    },
  ): void {
    const metric: MetricData = {
      timestamp: Date.now(),
      provider,
      model: options?.model,
      operation,
      duration,
      success,
      errorType: options?.error?.constructor.name,
      errorMessage: options?.error?.message,
      requestSize: options?.requestSize,
      responseSize: options?.responseSize,
      tokens: options?.tokens
        ? {
            input: options.tokens.input,
            output: options.tokens.output,
            total: options.tokens.input + options.tokens.output,
          }
        : undefined,
    };

    this.metrics.push(metric);

    // 發出事件以供其他服務監聽
    this.eventEmitter.emit('ai.metric.recorded', metric);

    // 記錄到數據庫（異步）
    this.saveMetricToDatabase(metric).catch((error) => {
      this.logger.error('保存指標到數據庫失敗', error);
    });

    this.logger.debug(
      `記錄指標: ${provider}/${operation} - ${success ? '成功' : '失敗'} (${duration}ms)`,
    );
  }

  /**
   * 獲取聚合統計
   */
  getAggregatedStats(
    timeWindowMinutes: number = 60,
    provider?: string,
    model?: string,
  ): AggregatedStats[] {
    const cutoffTime = Date.now() - timeWindowMinutes * 60 * 1000;
    const filteredMetrics = this.metrics.filter((metric) => {
      const matchesTime = metric.timestamp >= cutoffTime;
      const matchesProvider = !provider || metric.provider === provider;
      const matchesModel = !model || metric.model === model;
      return matchesTime && matchesProvider && matchesModel;
    });

    // 按 provider 和 model 分組
    const groupedMetrics = new Map<string, MetricData[]>();
    filteredMetrics.forEach((metric) => {
      const key = `${metric.provider}:${metric.model || 'default'}`;
      if (!groupedMetrics.has(key)) {
        groupedMetrics.set(key, []);
      }
      groupedMetrics.get(key)!.push(metric);
    });

    // 計算統計
    const stats: AggregatedStats[] = [];
    groupedMetrics.forEach((metrics, key) => {
      const [providerName, modelName] = key.split(':');
      stats.push(this.calculateStatsForGroup(metrics, providerName, modelName, timeWindowMinutes));
    });

    return stats;
  }

  /**
   * 計算單組統計
   */
  private calculateStatsForGroup(
    metrics: MetricData[],
    provider: string,
    model: string,
    timeWindowMinutes: number,
  ): AggregatedStats {
    const totalRequests = metrics.length;
    const successfulRequests = metrics.filter((m) => m.success).length;
    const failedRequests = totalRequests - successfulRequests;
    const successRate = totalRequests > 0 ? successfulRequests / totalRequests : 0;

    // 響應時間統計
    const durations = metrics.map((m) => m.duration).sort((a, b) => a - b);
    const averageResponseTime =
      durations.length > 0 ? durations.reduce((sum, d) => sum + d, 0) / durations.length : 0;

    const p95Index = Math.floor(durations.length * 0.95);
    const p99Index = Math.floor(durations.length * 0.99);
    const p95ResponseTime = durations[p95Index] || 0;
    const p99ResponseTime = durations[p99Index] || 0;

    // Token 統計
    const totalTokens = metrics
      .filter((m) => m.tokens)
      .reduce((sum, m) => sum + m.tokens!.total, 0);
    const averageTokensPerRequest = totalRequests > 0 ? totalTokens / totalRequests : 0;

    // 錯誤類型統計
    const errorTypes: Record<string, number> = {};
    metrics
      .filter((m) => !m.success && m.errorType)
      .forEach((m) => {
        errorTypes[m.errorType!] = (errorTypes[m.errorType!] || 0) + 1;
      });

    // 成本估算（需要進一步實現）
    const costEstimate = 0; // TODO: 根據 token 使用量和定價計算

    return {
      provider,
      model: model === 'default' ? undefined : model,
      timeWindow: `${timeWindowMinutes}m`,
      totalRequests,
      successfulRequests,
      failedRequests,
      successRate,
      averageResponseTime,
      p95ResponseTime,
      p99ResponseTime,
      totalTokens,
      averageTokensPerRequest,
      errorTypes,
      costEstimate,
    };
  }

  /**
   * 獲取即時健康狀態
   */
  getHealthStatus(): Record<string, any> {
    const stats = this.getAggregatedStats(5); // 最近 5 分鐘
    const overallHealth = {
      status: 'healthy',
      totalServices: stats.length,
      healthyServices: 0,
      degradedServices: 0,
      unhealthyServices: 0,
      alerts: this.getActiveAlerts().length,
    };

    stats.forEach((stat) => {
      if (stat.successRate >= 0.95) {
        overallHealth.healthyServices++;
      } else if (stat.successRate >= 0.8) {
        overallHealth.degradedServices++;
        if (overallHealth.status === 'healthy') {
          overallHealth.status = 'degraded';
        }
      } else {
        overallHealth.unhealthyServices++;
        overallHealth.status = 'unhealthy';
      }
    });

    return {
      overall: overallHealth,
      services: stats,
      activeAlerts: this.getActiveAlerts(),
    };
  }

  /**
   * 檢查警報條件
   */
  private checkAlerts(): void {
    const stats = this.getAggregatedStats(5); // 檢查最近 5 分鐘

    this.defaultAlerts.forEach((alertConfig) => {
      if (!alertConfig.enabled) return;

      stats.forEach((stat) => {
        this.evaluateAlert(alertConfig, stat);
      });
    });
  }

  /**
   * 評估單個警報
   */
  private evaluateAlert(alertConfig: AlertConfig, stat: AggregatedStats): void {
    const alertKey = `${alertConfig.name}:${stat.provider}:${stat.model || 'default'}`;
    const lastAlertTime = this.lastAlertTimes.get(alertKey) || 0;
    const cooldownPeriod = alertConfig.cooldown * 60 * 1000;

    // 檢查冷卻期
    if (Date.now() - lastAlertTime < cooldownPeriod) {
      return;
    }

    let currentValue: number;
    let shouldAlert = false;

    switch (alertConfig.condition) {
      case 'error_rate':
        currentValue = 1 - stat.successRate;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'response_time':
        currentValue = stat.p95ResponseTime;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'failure_count':
        currentValue = stat.failedRequests;
        shouldAlert = currentValue > alertConfig.threshold;
        break;
      case 'availability':
        currentValue = stat.successRate;
        shouldAlert = currentValue < alertConfig.threshold;
        break;
      default:
        return;
    }

    if (shouldAlert) {
      this.triggerAlert(alertConfig, stat, currentValue);
      this.lastAlertTimes.set(alertKey, Date.now());
    }
  }

  /**
   * 觸發警報
   */
  private triggerAlert(
    alertConfig: AlertConfig,
    stat: AggregatedStats,
    currentValue: number,
  ): void {
    const alertEvent: AlertEvent = {
      id: `${alertConfig.name}-${Date.now()}`,
      alertName: alertConfig.name,
      severity: alertConfig.severity,
      message: this.generateAlertMessage(alertConfig, stat, currentValue),
      provider: stat.provider,
      model: stat.model,
      currentValue,
      threshold: alertConfig.threshold,
      timestamp: Date.now(),
      acknowledged: false,
    };

    this.alertEvents.push(alertEvent);

    // 發出警報事件
    this.eventEmitter.emit('ai.alert.triggered', alertEvent);

    this.logger.warn(`AI 警報觸發: ${alertEvent.message}`, {
      alert: alertConfig.name,
      severity: alertConfig.severity,
      provider: stat.provider,
      model: stat.model,
    });

    // 保存警報到數據庫（異步）
    this.saveAlertToDatabase(alertEvent).catch((error) => {
      this.logger.error('保存警報到數據庫失敗', error);
    });
  }

  /**
   * 生成警報訊息
   */
  private generateAlertMessage(
    alertConfig: AlertConfig,
    stat: AggregatedStats,
    currentValue: number,
  ): string {
    const service = `${stat.provider}${stat.model ? `/${stat.model}` : ''}`;

    switch (alertConfig.condition) {
      case 'error_rate':
        return `${service} 錯誤率過高: ${(currentValue * 100).toFixed(2)}% (閾值: ${(alertConfig.threshold * 100).toFixed(2)}%)`;
      case 'response_time':
        return `${service} 響應時間過慢: ${currentValue}ms (閾值: ${alertConfig.threshold}ms)`;
      case 'failure_count':
        return `${service} 失敗次數過多: ${currentValue} 次 (閾值: ${alertConfig.threshold})`;
      case 'availability':
        return `${service} 可用性過低: ${(currentValue * 100).toFixed(2)}% (閾值: ${(alertConfig.threshold * 100).toFixed(2)}%)`;
      default:
        return `${service} 出現異常`;
    }
  }

  /**
   * 獲取活躍警報
   */
  getActiveAlerts(): AlertEvent[] {
    const cutoffTime = Date.now() - 24 * 60 * 60 * 1000; // 24 小時內
    return this.alertEvents
      .filter((alert) => alert.timestamp >= cutoffTime && !alert.acknowledged)
      .sort((a, b) => b.timestamp - a.timestamp);
  }

  /**
   * 確認警報
   */
  acknowledgeAlert(alertId: string): boolean {
    const alert = this.alertEvents.find((a) => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      this.logger.log(`警報已確認: ${alertId}`);
      return true;
    }
    return false;
  }

  /**
   * 清理舊指標
   */
  private cleanupOldMetrics(): void {
    if (this.metrics.length > this.maxMetricsRetention) {
      const removeCount = this.metrics.length - this.maxMetricsRetention;
      this.metrics.splice(0, removeCount);
      this.logger.debug(`清理了 ${removeCount} 條舊指標`);
    }

    // 清理舊警報（保留 7 天）
    const cutoffTime = Date.now() - 7 * 24 * 60 * 60 * 1000;
    const initialLength = this.alertEvents.length;
    for (let i = this.alertEvents.length - 1; i >= 0; i--) {
      if (this.alertEvents[i].timestamp < cutoffTime) {
        this.alertEvents.splice(i, 1);
      }
    }
    const removedAlerts = initialLength - this.alertEvents.length;
    if (removedAlerts > 0) {
      this.logger.debug(`清理了 ${removedAlerts} 條舊警報`);
    }
  }

  /**
   * 保存指標到數據庫
   */
  private async saveMetricToDatabase(metric: MetricData): Promise<void> {
    try {
      // 這裡可以實現將指標保存到數據庫的邏輯
      // 目前先記錄日誌
      this.logger.debug('指標已記錄', {
        provider: metric.provider,
        operation: metric.operation,
        success: metric.success,
        duration: metric.duration,
      });
    } catch (error) {
      this.logger.error('保存指標失敗', error);
    }
  }

  /**
   * 保存警報到數據庫
   */
  private async saveAlertToDatabase(alert: AlertEvent): Promise<void> {
    try {
      // 這裡可以實現將警報保存到數據庫的邏輯
      // 目前先記錄日誌
      this.logger.warn('警報已觸發', {
        id: alert.id,
        severity: alert.severity,
        message: alert.message,
      });
    } catch (error) {
      this.logger.error('保存警報失敗', error);
    }
  }

  /**
   * 獲取性能趨勢
   */
  getPerformanceTrends(hours: number = 24): Record<string, any> {
    const cutoffTime = Date.now() - hours * 60 * 60 * 1000;
    const relevantMetrics = this.metrics.filter((m) => m.timestamp >= cutoffTime);

    // 按小時分組
    const hourlyData = new Map<number, MetricData[]>();
    relevantMetrics.forEach((metric) => {
      const hour = Math.floor(metric.timestamp / (60 * 60 * 1000));
      if (!hourlyData.has(hour)) {
        hourlyData.set(hour, []);
      }
      hourlyData.get(hour)!.push(metric);
    });

    // 計算每小時統計
    const trends: any[] = [];
    hourlyData.forEach((metrics, hour) => {
      const hourStats = this.calculateStatsForGroup(metrics, 'all', 'all', 60);
      trends.push({
        timestamp: hour * 60 * 60 * 1000,
        ...hourStats,
      });
    });

    return {
      timeRange: `${hours}h`,
      dataPoints: trends.sort((a, b) => a.timestamp - b.timestamp),
    };
  }
}
