import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import {
  BaseAiException,
  AiErrorMapper,
  AiCircuitBreakerOpenException,
  AiServiceDegradedException,
  AiConcurrencyLimitException,
} from '../exceptions/ai-service.exceptions';

/**
 * 重試策略配置
 */
interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
  jitterFactor: number;
}

/**
 * 斷路器狀態
 */
type CircuitBreakerState = 'closed' | 'open' | 'half-open';

/**
 * 斷路器配置
 */
interface CircuitBreakerConfig {
  failureThreshold: number;
  successThreshold: number;
  timeout: number;
  resetTimeout: number;
}

/**
 * 服務狀態記錄
 */
interface ServiceHealth {
  provider: string;
  model?: string;
  state: CircuitBreakerState;
  failureCount: number;
  successCount: number;
  lastFailureTime: number;
  lastSuccessTime: number;
  activeRequests: number;
}

/**
 * 重試結果
 */
interface RetryResult<T> {
  success: boolean;
  data?: T;
  error?: BaseAiException;
  attempts: number;
  totalDuration: number;
}

@Injectable()
export class AiErrorHandlerService {
  private readonly logger = new Logger(AiErrorHandlerService.name);
  private readonly serviceHealthMap = new Map<string, ServiceHealth>();
  private readonly concurrencyLimits = new Map<string, number>();

  // 預設配置
  private readonly defaultRetryConfig: RetryConfig = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    jitterFactor: 0.1,
  };

  private readonly defaultCircuitBreakerConfig: CircuitBreakerConfig = {
    failureThreshold: 5,
    successThreshold: 3,
    timeout: 60000,
    resetTimeout: 300000,
  };

  constructor(private readonly configService: ConfigService) {
    this.initializeConfig();
  }

  /**
   * 初始化配置
   */
  private initializeConfig(): void {
    // 設定並發限制
    this.concurrencyLimits.set('openai', this.configService.get('AI_OPENAI_CONCURRENCY_LIMIT', 10));
    this.concurrencyLimits.set(
      'anthropic',
      this.configService.get('AI_ANTHROPIC_CONCURRENCY_LIMIT', 5),
    );
    this.concurrencyLimits.set('google', this.configService.get('AI_GOOGLE_CONCURRENCY_LIMIT', 8));
  }

  /**
   * 執行帶有錯誤處理的 AI 操作
   */
  async executeWithErrorHandling<T>(
    operation: () => Promise<T>,
    provider: string,
    model?: string,
    customRetryConfig?: Partial<RetryConfig>,
  ): Promise<T> {
    const serviceKey = this.getServiceKey(provider, model);

    // 檢查斷路器狀態
    await this.checkCircuitBreaker(serviceKey, provider, model);

    // 檢查並發限制
    await this.checkConcurrencyLimit(serviceKey, provider);

    try {
      // 增加活躍請求計數
      this.incrementActiveRequests(serviceKey, provider, model);

      // 執行重試邏輯
      const result = await this.executeWithRetry(operation, provider, model, customRetryConfig);

      // 記錄成功
      this.recordSuccess(serviceKey, provider, model);

      return result.data!;
    } catch (error) {
      // 記錄失敗
      this.recordFailure(serviceKey, provider, model, error);
      throw error;
    } finally {
      // 減少活躍請求計數
      this.decrementActiveRequests(serviceKey, provider, model);
    }
  }

  /**
   * 帶重試邏輯的執行
   */
  private async executeWithRetry<T>(
    operation: () => Promise<T>,
    provider: string,
    model?: string,
    customRetryConfig?: Partial<RetryConfig>,
  ): Promise<RetryResult<T>> {
    const config = { ...this.defaultRetryConfig, ...customRetryConfig };
    const startTime = Date.now();
    let lastError: BaseAiException;

    for (let attempt = 1; attempt <= config.maxRetries + 1; attempt++) {
      try {
        const data = await operation();
        return {
          success: true,
          data,
          attempts: attempt,
          totalDuration: Date.now() - startTime,
        };
      } catch (error) {
        // 轉換為 AI 特定異常
        const aiError =
          error instanceof BaseAiException ? error : AiErrorMapper.mapError(error, provider, model);

        lastError = aiError;

        this.logger.warn(
          `AI 操作失敗 (嘗試 ${attempt}/${config.maxRetries + 1}): ${aiError.message}`,
          {
            provider,
            model,
            error: aiError.name,
            retryable: aiError.retryable,
          },
        );

        // 檢查是否為最後一次嘗試或不可重試的錯誤
        if (attempt > config.maxRetries || !AiErrorMapper.isRetryable(aiError)) {
          throw aiError;
        }

        // 計算延遲時間
        const delay = this.calculateDelay(attempt - 1, config);
        this.logger.debug(`等待 ${delay}ms 後重試...`);

        await this.sleep(delay);
      }
    }

    // 這裡不應該到達，但為了類型安全
    throw lastError!;
  }

  /**
   * 檢查斷路器狀態
   */
  private async checkCircuitBreaker(
    serviceKey: string,
    provider: string,
    model?: string,
  ): Promise<void> {
    const health = this.getServiceHealth(serviceKey, provider, model);
    const config = this.defaultCircuitBreakerConfig;

    if (health.state === 'open') {
      const timeSinceLastFailure = Date.now() - health.lastFailureTime;

      if (timeSinceLastFailure >= config.resetTimeout) {
        // 轉為半開狀態
        health.state = 'half-open';
        health.successCount = 0;
        this.logger.log(`斷路器轉為半開狀態: ${serviceKey}`);
      } else {
        throw new AiCircuitBreakerOpenException(
          `AI 服務斷路器開啟 (${provider}/${model || 'default'})`,
          provider,
          model,
        );
      }
    }
  }

  /**
   * 檢查並發限制
   */
  private async checkConcurrencyLimit(serviceKey: string, provider: string): Promise<void> {
    const health = this.getServiceHealth(serviceKey, provider);
    const limit = this.concurrencyLimits.get(provider) || 10;

    if (health.activeRequests >= limit) {
      throw new AiConcurrencyLimitException(
        `AI 服務並發請求超限 (${health.activeRequests}/${limit})`,
        provider,
      );
    }
  }

  /**
   * 記錄成功
   */
  private recordSuccess(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    const config = this.defaultCircuitBreakerConfig;

    health.successCount++;
    health.lastSuccessTime = Date.now();

    if (health.state === 'half-open' && health.successCount >= config.successThreshold) {
      health.state = 'closed';
      health.failureCount = 0;
      this.logger.log(`斷路器關閉: ${serviceKey}`);
    }
  }

  /**
   * 記錄失敗
   */
  private recordFailure(
    serviceKey: string,
    provider: string,
    model: string | undefined,
    error: any,
  ): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    const config = this.defaultCircuitBreakerConfig;

    health.failureCount++;
    health.lastFailureTime = Date.now();
    health.successCount = 0;

    if (health.state === 'closed' && health.failureCount >= config.failureThreshold) {
      health.state = 'open';
      this.logger.error(`斷路器開啟: ${serviceKey} (失敗次數: ${health.failureCount})`);
    } else if (health.state === 'half-open') {
      health.state = 'open';
      this.logger.error(`斷路器重新開啟: ${serviceKey}`);
    }
  }

  /**
   * 增加活躍請求計數
   */
  private incrementActiveRequests(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.activeRequests++;
  }

  /**
   * 減少活躍請求計數
   */
  private decrementActiveRequests(serviceKey: string, provider: string, model?: string): void {
    const health = this.getServiceHealth(serviceKey, provider, model);
    health.activeRequests = Math.max(0, health.activeRequests - 1);
  }

  /**
   * 獲取服務健康狀態
   */
  private getServiceHealth(serviceKey: string, provider: string, model?: string): ServiceHealth {
    if (!this.serviceHealthMap.has(serviceKey)) {
      this.serviceHealthMap.set(serviceKey, {
        provider,
        model,
        state: 'closed',
        failureCount: 0,
        successCount: 0,
        lastFailureTime: 0,
        lastSuccessTime: Date.now(),
        activeRequests: 0,
      });
    }
    return this.serviceHealthMap.get(serviceKey)!;
  }

  /**
   * 生成服務鍵
   */
  private getServiceKey(provider: string, model?: string): string {
    return model ? `${provider}:${model}` : provider;
  }

  /**
   * 計算重試延遲
   */
  private calculateDelay(attempt: number, config: RetryConfig): number {
    const exponentialDelay = config.baseDelay * Math.pow(config.backoffMultiplier, attempt);
    const clampedDelay = Math.min(exponentialDelay, config.maxDelay);

    // 添加抖動以避免驚群效應
    const jitter = clampedDelay * config.jitterFactor * Math.random();

    return Math.floor(clampedDelay + jitter);
  }

  /**
   * 休眠函數
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  /**
   * 獲取服務健康狀態報告
   */
  getHealthReport(): Record<string, ServiceHealth> {
    const report: Record<string, ServiceHealth> = {};
    this.serviceHealthMap.forEach((health, key) => {
      report[key] = { ...health };
    });
    return report;
  }

  /**
   * 手動重置斷路器
   */
  resetCircuitBreaker(provider: string, model?: string): boolean {
    const serviceKey = this.getServiceKey(provider, model);
    const health = this.serviceHealthMap.get(serviceKey);

    if (health) {
      health.state = 'closed';
      health.failureCount = 0;
      health.successCount = 0;
      this.logger.log(`手動重置斷路器: ${serviceKey}`);
      return true;
    }

    return false;
  }

  /**
   * 設定並發限制
   */
  setConcurrencyLimit(provider: string, limit: number): void {
    this.concurrencyLimits.set(provider, limit);
    this.logger.log(`設定 ${provider} 並發限制為 ${limit}`);
  }

  /**
   * 執行服務降級
   */
  async executeWithFallback<T>(
    primaryOperation: () => Promise<T>,
    fallbackOperation: () => Promise<T>,
    provider: string,
    model?: string,
  ): Promise<T> {
    try {
      return await this.executeWithErrorHandling(primaryOperation, provider, model);
    } catch (error) {
      this.logger.warn(`主要服務失敗，使用備用方案: ${provider}/${model || 'default'}`);

      try {
        const result = await fallbackOperation();

        // 拋出服務降級警告但返回結果
        this.logger.warn('服務已降級運行');
        return result;
      } catch (fallbackError) {
        this.logger.error('備用方案也失敗了', fallbackError);
        throw AiErrorMapper.mapError(fallbackError, provider, model);
      }
    }
  }
}
