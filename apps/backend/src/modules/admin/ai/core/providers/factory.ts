import { Injectable } from '@nestjs/common';
import { BaseAiProvider } from './base/base.provider';
import { PrismaClient, AiBotProviderType } from '@prisma/client';
import { OpenAiProvider } from './implementations/openai.provider';
import { <PERSON><PERSON><PERSON>ider } from './implementations/claude.provider';
import { GeminiProvider } from './implementations/gemini.provider';
import { OpenAiCompatibleProvider } from './implementations/openai-compatible.provider';

@Injectable()
export class AiProviderFactory {
  createProvider(providerType: AiBotProviderType, apiKey: string, apiUrl?: string): BaseAiProvider {
    switch (providerType) {
      case AiBotProviderType.OPENAI:
        return new OpenAiProvider(apiKey, apiUrl);
      case AiBotProviderType.CLAUDE:
        return new ClaudeProvider(apiKey, apiUrl);
      case AiBotProviderType.GEMINI:
        return new GeminiProvider(apiKey, apiUrl);
      case AiBotProviderType.OPENAI_COMPATIBLE:
        return new OpenAiCompatibleProvider(apiKey, apiUrl!);
      default:
        throw new Error(`Unsupported provider type: ${providerType}`);
    }
  }
}

export { AiBotProviderType };
