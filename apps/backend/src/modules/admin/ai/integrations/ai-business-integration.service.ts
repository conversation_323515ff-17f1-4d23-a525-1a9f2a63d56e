import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { EncryptionService } from '@/modules/core/encryption/encryption.service';
import { AiProviderFactory } from '../core/providers/factory';
import {
  AiMessage,
  AiExecuteOptions,
  VisionAnalysisOptions,
} from '../core/providers/base/base.provider';
import { Prisma, ai_bots as AiBot, ai_models as AiModel, ai_keys as AiKey } from '@prisma/client';
import {
  ProjectAnalysisRequest,
  ProjectAnalysisResult,
  PhotoAnalysisRequest,
  PhotoAnalysisResult,
  WorkflowOptimizationRequest,
  WorkflowOptimizationResult,
  BusinessFeature,
  BusinessVisionAnalysisOptions as ImportedBusinessVisionAnalysisOptions,
  BusinessAiExecuteOptions as ImportedBusinessAiExecuteOptions,
} from './types/business.types';

// 定義通用的訊息介面
export interface BusinessAiMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

// 定義執行選項介面
export interface BusinessAiExecuteOptions extends ImportedBusinessAiExecuteOptions {
  model?: string;
  responseFormat?: 'TEXT' | 'JSON_OBJECT';
}

// 定義視覺分析選項介面
export interface BusinessVisionAnalysisOptions extends ImportedBusinessVisionAnalysisOptions {
  imageUrl: string;
  prompt: string;
  model?: string;
  responseFormat?: 'TEXT' | 'JSON_OBJECT';
  detail?: 'low' | 'high' | 'auto';
}

type FullBot = AiBot & { ai_keys: AiKey; ai_models: AiModel };

@Injectable()
export class AiBusinessIntegrationService {
  private readonly logger = new Logger(AiBusinessIntegrationService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly aiProviderFactory: AiProviderFactory,
  ) {}

  async analyzeProject(request: ProjectAnalysisRequest): Promise<ProjectAnalysisResult> {
    const prompt = this.buildProjectAnalysisPrompt(request);
    const resultJsonString = await this.executeTextAnalysis(
      'project_analysis',
      request.tenant_id,
      prompt,
      { confidence: request.confidence },
    );
    const result = JSON.parse(resultJsonString);
    const fullResult: ProjectAnalysisResult = { ...result, projectId: request.project_id };
    await this.saveAnalysisResult(fullResult);
    return fullResult;
  }

  async analyzePhoto(request: PhotoAnalysisRequest): Promise<PhotoAnalysisResult> {
    const prompt = this.buildPhotoAnalysisPrompt(request);
    const visionOptions: BusinessVisionAnalysisOptions = {
      tenant_id: request.tenant_id,
      project_id: request.project_id,
      photo_url: request.photo_url,
      analysis_type: request.analysis_type,
      context: request.context,
      confidence: request.confidence,
      imageUrl: request.photo_url,
      prompt: prompt,
    };
    const resultJsonString = await this.executeVisionAnalysis(prompt, visionOptions);
    return JSON.parse(resultJsonString) as PhotoAnalysisResult;
  }

  async optimizeWorkflow(
    request: WorkflowOptimizationRequest,
  ): Promise<WorkflowOptimizationResult> {
    const prompt = this.buildWorkflowOptimizationPrompt(request);
    const resultJsonString = await this.executeTextAnalysis(
      'workflow_optimization',
      request.tenant_id,
      prompt,
      { confidence: request.confidence },
    );
    return JSON.parse(resultJsonString) as WorkflowOptimizationResult;
  }

  private async getBotForFeature(feature: BusinessFeature, tenant_id?: string): Promise<FullBot> {
    const where: Prisma.ai_botsWhereInput = {
      is_enabled: true,
      scope: tenant_id ? 'TENANT_TEMPLATE' : 'SYSTEM',
      scene: feature,
    };
    const aiBot = await this.prisma.ai_bots.findFirst({
      where,
      include: {
        ai_keys: true,
        ai_models: true,
      },
    });

    if (!aiBot || !aiBot.ai_keys || !aiBot.ai_models) {
      throw new NotFoundException(`找不到適用於功能 '${feature}' 的 AI Bot。請檢查系統設定。`);
    }
    return aiBot as FullBot;
  }

  private async executeVisionAnalysis(
    prompt: string,
    options: BusinessVisionAnalysisOptions,
  ): Promise<any> {
    this.logger.debug(
      `Executing vision analysis for tenant ${options.tenant_id}, project ${options.project_id}`,
    );
    const { tenant_id, photo_url } = options;
    const aiBot = await this.getBotForFeature('photo_analysis', tenant_id);

    const decryptedApiKey = this.encryptionService.decrypt(aiBot.ai_keys.api_key);
    const provider = this.aiProviderFactory.createProvider(
      aiBot.provider_type,
      decryptedApiKey,
      aiBot.ai_keys.api_url || undefined,
    );

    if (!provider.executeVisionAnalysis) {
      throw new BadRequestException(
        `The configured AI provider (${aiBot.provider_type}) does not support vision analysis.`,
      );
    }

    const model = aiBot.ai_models.model_name || 'gpt-4-vision-preview';
    const visionOptions: VisionAnalysisOptions = {
      imageUrl: photo_url,
      prompt: prompt,
      model,
      temperature: options.temperature ?? 0.3,
      maxTokens: options.maxTokens ?? 2048,
    };

    return provider.executeVisionAnalysis(visionOptions);
  }

  private async executeTextAnalysis(
    feature: BusinessFeature,
    tenant_id: string,
    prompt: string,
    options: BusinessAiExecuteOptions,
  ): Promise<any> {
    this.logger.debug(`Executing text analysis for tenant ${tenant_id}`);
    const aiBot = await this.getBotForFeature(feature, tenant_id);

    const decryptedApiKey = this.encryptionService.decrypt(aiBot.ai_keys.api_key);
    const provider = this.aiProviderFactory.createProvider(
      aiBot.provider_type,
      decryptedApiKey,
      aiBot.ai_keys.api_url || undefined,
    );

    const model = aiBot.ai_models.model_name || 'gpt-3.5-turbo';
    const executeOptions: AiExecuteOptions = {
      model,
      temperature: aiBot.temperature ?? 0.3,
      maxTokens: options.maxTokens ?? 1024,
    };

    const messages: AiMessage[] = [
      { role: 'system', content: prompt },
      { role: 'user', content: 'Please perform the analysis based on the system prompt.' },
    ];
    return provider.execute(messages, executeOptions);
  }

  private async saveAnalysisResult(result: ProjectAnalysisResult): Promise<void> {
    this.logger.log(`Analysis result saved for project ${result.projectId}`);
  }

  private buildProjectAnalysisPrompt(request: ProjectAnalysisRequest): string {
    return `Analyze project ${request.project_id}. Details: ${JSON.stringify(request)}`;
  }

  private buildPhotoAnalysisPrompt(request: PhotoAnalysisRequest): string {
    return `Analyze photo at ${request.photo_url} for ${request.analysis_type}. Context: ${request.context}`;
  }

  private buildWorkflowOptimizationPrompt(request: WorkflowOptimizationRequest): string {
    return `Optimize workflow for tenant ${request.tenant_id}. Details: ${JSON.stringify(request)}`;
  }
}
