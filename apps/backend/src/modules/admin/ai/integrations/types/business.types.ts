// Business AI Integration Types

export type BusinessFeature = 'project_analysis' | 'photo_analysis' | 'workflow_optimization';

export interface ProjectAnalysisRequest {
  project_id: string;
  tenant_id: string;
  analysis_type: 'status' | 'risk' | 'performance' | 'optimization';
  include_sub_projects?: boolean;
  include_tasks?: boolean;
  include_progress?: boolean;
  confidence?: number;
}

export interface ProjectAnalysisResult {
  projectId: string;
  analysisType: string;
  status: string;
  risks: Array<{
    type: string;
    severity: 'low' | 'medium' | 'high';
    description: string;
    recommendations: string[];
  }>;
  performance: {
    completionRate: number;
    efficiency: number;
    timeToCompletion: number;
  };
  recommendations: string[];
  confidence: number;
  timestamp: string;
}

export interface PhotoAnalysisRequest {
  photo_url: string;
  project_id: string;
  tenant_id: string;
  analysis_type: 'progress' | 'quality' | 'safety' | 'equipment';
  context?: string;
  confidence?: number;
}

export interface PhotoAnalysisResult {
  photoUrl: string;
  projectId: string;
  analysisType: string;
  findings: Array<{
    category: string;
    description: string;
    confidence: number;
    location?: {
      x: number;
      y: number;
      width: number;
      height: number;
    };
  }>;
  recommendations: string[];
  confidence: number;
  timestamp: string;
}

export interface WorkflowOptimizationRequest {
  tenant_id: string;
  project_id?: string;
  time_range: {
    start_date: Date;
    end_date: Date;
  };
  include_metrics?: string[];
  confidence?: number;
}

export interface WorkflowOptimizationResult {
  tenantId: string;
  projectId?: string;
  timeRange: {
    startDate: string;
    endDate: string;
  };
  currentMetrics: {
    taskCompletionRate: number;
    averageTimeSpent: number;
    costAnalysis: number;
    resourceUtilization: number;
  };
  optimizations: Array<{
    area: string;
    currentValue: number;
    optimizedValue: number;
    improvement: number;
    recommendations: string[];
  }>;
  confidence: number;
  timestamp: string;
}

export interface BusinessVisionAnalysisOptions {
  tenant_id: string;
  project_id: string;
  photo_url: string;
  analysis_type: string;
  context?: string;
  confidence?: number;
  temperature?: number;
  maxTokens?: number;
}

export interface BusinessAiExecuteOptions {
  confidence?: number;
  temperature?: number;
  maxTokens?: number;
}
