import {
  Controller,
  Post,
  Body,
  HttpException,
  HttpStatus,
  Get,
  Put,
  Delete,
  Param,
  Query,
  Request,
  UseGuards,
  Logger,
  Req,
  HttpCode,
  ValidationPipe,
  ForbiddenException,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { AiBotsService } from './ai-bots.service';
import {
  CreateBotDto,
  UpdateBotDto,
  TestBotDto,
  OptimizePromptDto,
  ExecuteBotDto,
} from './dto/bot.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { CurrentUser } from '../../../core/auth/decorators/current-user.decorator';
import { PrismaClient, AiBotScope } from '@prisma/client';
import { CheckPolicies } from '../../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { JwtUser } from '../../../../types/jwt-user.type';
import { PoliciesGuard } from '../../../../casl/guards/permission.guard';

// 定義 User 類型
interface User {
  id: string;
  email: string;
  name?: string;
}

@ApiTags('admin/ai/bots')
@UseGuards(JwtAuthGuard, PoliciesGuard)
@Controller('admin/ai/bots')
export class AiBotsController {
  private readonly logger = new Logger(AiBotsController.name);

  constructor(private readonly aiBotsService: AiBotsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_BOT))
  @ApiOperation({ summary: '讀取所有 AI Bot' })
  findAll(
    @Query('scope') scope?: AiBotScope,
    @Query('tenant_id') tenant_id?: string,
    @Query('workspace_id') workspace_id?: string,
  ) {
    return this.aiBotsService.findAll(scope, tenant_id, workspace_id);
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_BOT))
  @ApiOperation({ summary: '根據 ID 讀取 AI Bot' })
  async findOne(@Param('id') id: string) {
    return this.aiBotsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.AI_BOT))
  @ApiOperation({ summary: '建立新 AI Bot' })
  async create(@Body(ValidationPipe) createBotDto: CreateBotDto, @CurrentUser() user: JwtUser) {
    return this.aiBotsService.create(createBotDto, user.id);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.AI_BOT))
  @ApiOperation({ summary: '更新 AI Bot' })
  async update(
    @Param('id') id: string,
    @Body(ValidationPipe) updateBotDto: UpdateBotDto,
    @CurrentUser() user: JwtUser,
  ) {
    return this.aiBotsService.update(id, updateBotDto, user.id);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.AI_BOT))
  @ApiOperation({ summary: '刪除 AI Bot' })
  async remove(@Param('id') id: string) {
    return this.aiBotsService.delete(id);
  }

  @Post('test')
  @ApiOperation({ summary: '測試 Bot 設定' })
  async testBot(@Body() dto: TestBotDto) {
    return await this.aiBotsService.testBot(dto.bot_id, dto.message, dto.prompt, dto.temperature);
  }

  @Post('optimize-prompt')
  @ApiOperation({ summary: '優化提示詞' })
  async optimizePrompt(@Body() dto: OptimizePromptDto) {
    return await this.aiBotsService.optimizePrompt(dto);
  }

  @Post(':id/chat')
  @ApiOperation({ summary: '使用指定 Bot 進行對話' })
  async chat(
    @Param('id') id: string,
    @Body()
    body: {
      message: string;
      temperature?: number;
      prompt?: string;
      systemPrompt?: string;
    },
  ) {
    this.logger.log(`使用 Bot ${id} 進行對話，訊息: ${body.message}`);
    try {
      const response = await this.aiBotsService.testBot(
        id,
        body.message,
        body.prompt,
        body.temperature,
        body.systemPrompt,
      );
      return response;
    } catch (error) {
      this.logger.error(`Bot ${id} 對話失敗: ${error.message}`, error.stack);
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: '對話失敗',
          error: error.message,
          timestamp: new Date().toISOString(),
          details: JSON.stringify(body, null, 2),
        },
        HttpStatus.BAD_REQUEST,
      );
    }
  }

  @Post(':id/execute')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.EXECUTE, Subjects.AI_BOT))
  @HttpCode(200)
  @ApiOperation({ summary: '執行 Bot' })
  async execute(@Param('id') id: string, @Body() body: ExecuteBotDto) {
    try {
      const response = await this.aiBotsService.execute(id, body);
      return response;
    } catch (error) {
      this.logger.error(`執行 Bot ${id} 失敗:`, error);
      throw error;
    }
  }

  @Put(':id/status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.AI_BOT))
  @ApiOperation({ summary: '更新 Bot 啟用狀態' })
  async updateStatus(@Param('id') id: string, @Body('isEnabled') isEnabled: boolean) {
    return this.aiBotsService.updateStatus(id, isEnabled);
  }
}
