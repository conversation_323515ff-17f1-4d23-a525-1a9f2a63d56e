import { Modu<PERSON> } from '@nestjs/common';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { EncryptionModule } from '@/modules/core/encryption/encryption.module';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { AiBotsController } from './ai-bots.controller';
import { AiBotsService } from './ai-bots.service';
import { AiProviderFactory } from '../core/providers/factory';

@Module({
  imports: [PrismaModule, EncryptionModule, AuthModule],
  controllers: [AiBotsController],
  providers: [AiBotsService, AiProviderFactory],
  exports: [AiBotsService],
})
export class AiBotsModule {}
