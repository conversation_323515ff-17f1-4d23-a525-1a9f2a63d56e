import { Test, TestingModule } from '@nestjs/testing';
import { AiBotsController } from '../ai-bots.controller';
import { AiBotsService } from '../ai-bots.service';
import { PrismaService } from '../../../../core/prisma/prisma.service';
import { CaslAbilityFactory } from '../../../../../casl/ability/casl-ability.factory';
import { PoliciesGuard } from '../../../../../casl/guards/permission.guard';
import { PermissionCheckerService } from '../../../../../casl/services/permission-checker.service';
import { Reflector } from '@nestjs/core';
import { JwtAuthGuard } from '../../../../core/auth/guards/auth.guard';
import { CreateBotDto, UpdateBotDto, ExecuteBotDto } from '../dto/bot.dto';
import { AiBotScope, AiBotProviderType, AiBotResponseFormat } from '@prisma/client';

// Mock PrismaService
const mockPrismaService = {
  ai_bots: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
    create: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
  },
  ai_models: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
  ai_keys: {
    findMany: jest.fn(),
    findUnique: jest.fn(),
  },
};

describe('AiBotsController', () => {
  let controller: AiBotsController;
  let service: AiBotsService;
  let prismaService: PrismaService;
  let caslAbilityFactory: CaslAbilityFactory;
  let permissionCheckerService: PermissionCheckerService;

  const mockUser = {
    id: 'user-123',
    sub: 'user-123',
    email: '<EMAIL>',
    user_type: 'system',
    tenant_id: null,
    role: 'SUPER_ADMIN',
  };

  const mockAiBot = {
    id: 'bot-123',
    name: 'Test Bot',
    description: 'Test Description',
    scope: AiBotScope.SYSTEM,
    provider_type: AiBotProviderType.OPENAI,
    model_id: 'model-123',
    key_id: 'key-123',
    provider_config_override: null,
    system_prompt: 'Test prompt',
    temperature: 0.7,
    max_tokens: 1000,
    response_format: AiBotResponseFormat.TEXT,
    is_enabled: true,
    is_template: false,
    scene: 'test',
    tenant_id: null,
    workspace_id: null,
    created_at: new Date(),
    updated_at: new Date(),
    created_by: 'user-123',
    updated_by: null,
  };

  const mockExecuteResponse = {
    response: 'Test AI response',
    usage: {
      input_tokens: 10,
      output_tokens: 20,
      total_tokens: 30,
    },
    model: 'gpt-4',
    provider: 'openai',
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [AiBotsController],
      providers: [
        {
          provide: AiBotsService,
          useValue: {
            findAll: jest.fn().mockResolvedValue([mockAiBot]),
            findOne: jest.fn().mockResolvedValue(mockAiBot),
            create: jest.fn().mockResolvedValue(mockAiBot),
            update: jest.fn().mockResolvedValue(mockAiBot),
            delete: jest.fn().mockResolvedValue(mockAiBot),
            execute: jest.fn().mockResolvedValue(mockExecuteResponse),
            testBot: jest.fn().mockResolvedValue(mockExecuteResponse),
            optimizePrompt: jest.fn().mockResolvedValue({ optimizedPrompt: 'Optimized prompt' }),
            updateStatus: jest.fn().mockResolvedValue(mockAiBot),
          },
        },
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
        {
          provide: CaslAbilityFactory,
          useValue: {
            createForUser: jest.fn().mockResolvedValue({
              can: jest.fn().mockReturnValue(true),
              rules: [],
            }),
          },
        },
        {
          provide: PermissionCheckerService,
          useValue: {
            checkPermission: jest.fn().mockResolvedValue({ granted: true }),
            canManage: jest.fn().mockResolvedValue(true),
            canRead: jest.fn().mockResolvedValue(true),
            canCreate: jest.fn().mockResolvedValue(true),
            canUpdate: jest.fn().mockResolvedValue(true),
            canDelete: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: Reflector,
          useValue: {
            get: jest.fn().mockReturnValue([]),
            getAllAndOverride: jest.fn().mockReturnValue([]),
            getAllAndMerge: jest.fn().mockReturnValue([]),
          },
        },
        {
          provide: PoliciesGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
        {
          provide: JwtAuthGuard,
          useValue: {
            canActivate: jest.fn().mockResolvedValue(true),
          },
        },
      ],
    })
      .overrideGuard(JwtAuthGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .overrideGuard(PoliciesGuard)
      .useValue({
        canActivate: jest.fn().mockReturnValue(true),
      })
      .compile();

    controller = module.get<AiBotsController>(AiBotsController);
    service = module.get<AiBotsService>(AiBotsService);
    prismaService = module.get<PrismaService>(PrismaService);
    caslAbilityFactory = module.get<CaslAbilityFactory>(CaslAbilityFactory);
    permissionCheckerService = module.get<PermissionCheckerService>(PermissionCheckerService);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
    expect(prismaService).toBeDefined();
    expect(caslAbilityFactory).toBeDefined();
    expect(permissionCheckerService).toBeDefined();
  });

  describe('findAll', () => {
    it('should return an array of AI bots', async () => {
      const result = await controller.findAll();
      expect(result).toEqual([mockAiBot]);
      expect(service.findAll).toHaveBeenCalled();
    });
  });

  describe('findOne', () => {
    it('should return a single AI bot', async () => {
      const result = await controller.findOne('bot-123');
      expect(result).toEqual(mockAiBot);
      expect(service.findOne).toHaveBeenCalledWith('bot-123');
    });
  });

  describe('create', () => {
    it('should create a new AI bot', async () => {
      const createDto: CreateBotDto = {
        name: 'Test Bot',
        description: 'Test Description',
        scope: AiBotScope.SYSTEM,
        provider_type: AiBotProviderType.OPENAI,
        model_id: 'model-123',
        key_id: 'key-123',
        system_prompt: 'Test prompt',
        temperature: 0.7,
        max_tokens: 1000,
        response_format: AiBotResponseFormat.TEXT,
        is_enabled: true,
        scene: 'test',
        tenant_id: 'tenant-123',
      };

      const result = await controller.create(createDto, mockUser as any);
      expect(result).toEqual(mockAiBot);
      expect(service.create).toHaveBeenCalledWith(createDto, mockUser.id);
    });
  });

  describe('update', () => {
    it('should update an AI bot', async () => {
      const updateDto: UpdateBotDto = {
        name: 'Updated Bot',
        description: 'Updated Description',
      };

      const result = await controller.update('bot-123', updateDto, mockUser as any);
      expect(result).toEqual(mockAiBot);
      expect(service.update).toHaveBeenCalledWith('bot-123', updateDto, mockUser.id);
    });
  });

  describe('remove', () => {
    it('should remove an AI bot', async () => {
      const result = await controller.remove('bot-123');
      expect(result).toEqual(mockAiBot);
      expect(service.delete).toHaveBeenCalledWith('bot-123');
    });
  });

  describe('execute', () => {
    it('should execute an AI bot', async () => {
      const executeDto: ExecuteBotDto = {
        messages: [{ role: 'user', content: 'Test input' }],
        temperature: 0.7,
      };

      const result = await controller.execute('bot-123', executeDto);
      expect(result).toEqual(mockExecuteResponse);
      expect(service.execute).toHaveBeenCalledWith('bot-123', executeDto);
    });
  });
});
