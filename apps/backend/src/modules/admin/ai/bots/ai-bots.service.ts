import { Injectable, BadRequestException, NotFoundException, Logger } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { EncryptionService } from '../../../core/encryption/encryption.service';
import { AiProviderFactory } from '../core/providers/factory';
import { AiMessage, AiExecuteOptions } from '../core/providers/base/base.provider';
import { CreateBotDto, UpdateBotDto, OptimizePromptDto, ExecuteBotDto } from './dto/bot.dto';
import { v4 as uuidv4 } from 'uuid';
import {
  ai_bots as AiBot,
  AiBotScope,
  AiBotProviderType,
  AiBotResponseFormat,
  Prisma,
  ai_models as AiModel,
  ai_keys as AiKey,
} from '@prisma/client';

type FullBot = AiBot & { ai_models: AiModel; ai_keys: AiK<PERSON> };

@Injectable()
export class AiBotsService {
  private readonly logger = new Logger(AiBotsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly encryptionService: EncryptionService,
    private readonly aiProviderFactory: AiProviderFactory,
  ) {}

  async findAll(scope?: AiBotScope, tenant_id?: string, workspace_id?: string): Promise<AiBot[]> {
    const where: Prisma.ai_botsWhereInput = {};
    if (scope) where.scope = scope;
    if (tenant_id) where.tenant_id = tenant_id;
    if (workspace_id) where.workspace_id = workspace_id;

    return this.prisma.ai_bots.findMany({
      where,
      include: { ai_keys: true, ai_models: true },
      orderBy: { created_at: 'desc' },
    });
  }

  async findOne(id: string): Promise<FullBot> {
    const bot = await this.prisma.ai_bots.findUnique({
      where: { id },
      include: { ai_keys: true, ai_models: true },
    });

    if (!bot || !bot.ai_models || !bot.ai_keys) {
      throw new NotFoundException(`Bot with ID ${id} or its relations not found`);
    }

    return bot as FullBot;
  }

  async create(createBotDto: CreateBotDto, userId: string): Promise<AiBot> {
    const now = new Date();
    return this.prisma.ai_bots.create({
      data: {
        id: uuidv4(),
        name: createBotDto.name,
        description: createBotDto.description,
        scope: createBotDto.scope,
        provider_type: createBotDto.provider_type,
        model_id: createBotDto.model_id,
        key_id: createBotDto.key_id,
        provider_config_override: createBotDto.provider_config_override,
        system_prompt: createBotDto.system_prompt,
        temperature: createBotDto.temperature,
        max_tokens: createBotDto.max_tokens,
        response_format: createBotDto.response_format ?? AiBotResponseFormat.TEXT,
        is_enabled: createBotDto.is_enabled ?? true,
        is_template: createBotDto.is_template ?? false,
        scene: createBotDto.scene,
        tenant_id: createBotDto.tenant_id,
        workspace_id: createBotDto.workspace_id,
        created_by: userId,
        updated_by: userId,
        created_at: now,
        updated_at: now,
      },
      include: { ai_keys: true, ai_models: true },
    });
  }

  async update(id: string, updateBotDto: UpdateBotDto, userId: string): Promise<AiBot> {
    await this.findOne(id);
    const data: Prisma.ai_botsUpdateInput = { ...updateBotDto, updated_by: userId };
    return this.prisma.ai_bots.update({
      where: { id },
      data,
      include: { ai_keys: true, ai_models: true },
    });
  }

  async delete(id: string): Promise<AiBot> {
    await this.findOne(id);
    return this.prisma.ai_bots.delete({ where: { id } });
  }

  async testBot(
    botId: string,
    message: string,
    prompt?: string,
    temperature?: number,
    systemPrompt?: string,
  ) {
    const bot = await this.findOne(botId);
    return {
      success: true,
      response: `Test response from ${bot.name}: ${message}`,
      model: bot.ai_models.model_name,
      temperature: temperature ?? bot.temperature,
      systemPrompt: systemPrompt ?? bot.system_prompt,
    };
  }

  async optimizePrompt(dto: OptimizePromptDto) {
    return {
      success: true,
      originalPrompt: dto.prompt,
      optimizedPrompt: `Optimized: ${dto.prompt}`,
      suggestions: ['Make it more specific', 'Add context', 'Clarify the goal'],
    };
  }

  async execute(id: string, executeDto: ExecuteBotDto) {
    const messages = executeDto.messages || [];
    if (messages.length === 0) {
      throw new BadRequestException('No message provided for execution.');
    }
    return this.executeBot(id, messages, {
      model: '',
      temperature: executeDto.temperature,
    });
  }

  async updateStatus(id: string, is_enabled: boolean): Promise<AiBot> {
    await this.findOne(id);
    return this.prisma.ai_bots.update({
      where: { id },
      data: { is_enabled },
    });
  }

  async findDefaultBot(tenant_id: string, scope: AiBotScope): Promise<FullBot | null> {
    const bot = await this.prisma.ai_bots.findFirst({
      where: { tenant_id, scope, is_enabled: true },
      include: { ai_keys: true, ai_models: true },
      orderBy: { created_at: 'asc' },
    });
    return bot as FullBot | null;
  }

  async executeBot(botId: string, messages: AiMessage[], options: Partial<AiExecuteOptions> = {}) {
    const bot = await this.findOne(botId);

    if (!bot.is_enabled) {
      throw new BadRequestException('Bot is disabled');
    }

    const decryptedApiKey = this.encryptionService.decrypt(bot.ai_keys.api_key);
    const provider = this.aiProviderFactory.createProvider(
      bot.provider_type,
      decryptedApiKey,
      bot.ai_keys.api_url || undefined,
    );

    const executeOptions = this.buildExecuteOptions(bot, options);
    return provider.execute(messages, executeOptions);
  }

  private buildExecuteOptions(bot: FullBot, options: Partial<AiExecuteOptions>): AiExecuteOptions {
    return {
      model: bot.ai_models.model_name,
      temperature: options.temperature ?? bot.temperature ?? 0.7,
      maxTokens: options.maxTokens ?? (bot.max_tokens ? Number(bot.max_tokens) : undefined),
      responseFormat: options.responseFormat ?? bot.response_format,
    };
  }
}
