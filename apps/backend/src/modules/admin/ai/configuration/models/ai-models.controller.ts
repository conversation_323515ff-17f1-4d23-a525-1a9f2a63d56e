import {
  Controller,
  Get,
  Post,
  Body,
  Param,
  Delete,
  Put,
  Logger,
  UseGuards,
  HttpCode,
} from '@nestjs/common';
import { JwtAuthGuard } from '../../../../core/auth/guards/auth.guard';
import { AiModelsService } from './ai-models.service';
import { CreateModelDto, UpdateModelDto } from './dto/ai-model.dto';
import { ApiTags, ApiOperation } from '@nestjs/swagger';
import { PoliciesGuard } from '../../../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../../../casl/decorators/check-policies.decorator';
import { Actions, Subjects } from '@horizai/permissions';
import { AppAbility } from '../../../../../types/models/casl.model';

@ApiTags('admin/ai/models')
@Controller('admin/ai/models')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class AiModelsController {
  private readonly logger = new Logger(AiModelsController.name);

  constructor(private readonly aiModelsService: AiModelsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_MODEL))
  @ApiOperation({ summary: '讀取所有 AI 模型' })
  async findAll() {
    return this.aiModelsService.findAll();
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.AI_MODEL))
  @ApiOperation({ summary: '根據 ID 讀取 AI 模型' })
  async findOne(@Param('id') id: string) {
    return this.aiModelsService.findOne(id);
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.AI_MODEL))
  @ApiOperation({ summary: '建立新 AI 模型' })
  async create(@Body() createModelDto: CreateModelDto) {
    return this.aiModelsService.create(createModelDto);
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.AI_MODEL))
  @ApiOperation({ summary: '更新 AI 模型' })
  async update(@Param('id') id: string, @Body() updateModelDto: UpdateModelDto) {
    return this.aiModelsService.update(id, updateModelDto);
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.AI_MODEL))
  @ApiOperation({ summary: '刪除 AI 模型' })
  async remove(@Param('id') id: string) {
    return this.aiModelsService.remove(id);
  }

  @Put(':id/status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.AI_MODEL))
  @ApiOperation({ summary: '更新 AI 模型啟用狀態' })
  async updateStatus(@Param('id') id: string, @Body('is_enabled') is_enabled: boolean) {
    return this.aiModelsService.updateStatus(id, is_enabled);
  }

  @Post('fetch-from-providers')
  @HttpCode(200)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.MANAGE, Subjects.AI_MODEL))
  @ApiOperation({ summary: '從 AI 提供商獲取模型列表並保存到資料庫' })
  async fetchFromProviders() {
    this.logger.log('開始從供應商擷取模型列表');
    return await this.aiModelsService.fetchFromProviders();
  }

  @Post('sync-pricing')
  @HttpCode(200)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.MANAGE, Subjects.AI_MODEL))
  @ApiOperation({
    summary: '從 AI 提供商官方網站獲取模型價格和 Token 容量並更新資料庫',
  })
  async syncModelPricing() {
    this.logger.log('開始從供應商同步模型價格');
    return await this.aiModelsService.syncModelPricing();
  }
}
