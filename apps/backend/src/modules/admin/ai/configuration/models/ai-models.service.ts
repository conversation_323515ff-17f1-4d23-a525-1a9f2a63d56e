import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { CreateModelDto, UpdateModelDto } from './dto/ai-model.dto';
import { Prisma, ai_models as AiModel, AiBotProviderType } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';
import * as crypto from 'crypto';
import { AiPriceCrawlerService, ModelPriceInfo } from '../pricing/ai-price-crawler.service';
import { AiProviderFactory } from '../../core/providers/factory';

// 定義結果型別
export interface ProviderResult {
  provider: string;
  created: number;
  existed: number;
  failed?: boolean;
  error?: string;
}

// 定義價格錯誤型別
export interface PriceError {
  provider: string;
  modelName: string;
  error: string;
}

@Injectable()
export class AiModelsService {
  private readonly logger = new Logger(AiModelsService.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly priceCrawler: AiPriceCrawlerService,
    private readonly providerFactory: AiProviderFactory,
  ) {}

  /**
   * 列出所有 AI 模型，可篩選 provider 和 status
   */
  async findAll(): Promise<AiModel[]> {
    return this.prisma.ai_models.findMany({
      orderBy: [{ provider: 'asc' }, { model_name: 'asc' }],
    });
  }

  /**
   * 根據 ID 查找單一 AI 模型
   */
  async findOne(id: string): Promise<AiModel> {
    const model = await this.prisma.ai_models.findUnique({
      where: { id },
    });

    if (!model) {
      throw new NotFoundException(`AI model with ID ${id} not found`);
    }

    return model;
  }

  /**
   * 建立新的 AI 模型
   */
  async create(data: CreateModelDto): Promise<AiModel> {
    try {
      const existingModel = await this.prisma.ai_models.findUnique({
        where: {
          provider_model_name: {
            provider: data.provider,
            model_name: data.model_name,
          },
        },
      });

      if (existingModel) {
        throw new BadRequestException(
          'A model with the same name already exists for this provider.',
        );
      }

      const dbData = {
        provider: data.provider,
        model_name: data.model_name,
        display_name: data.display_name,
        input_price_per_1k_tokens: data.input_price_per_1k_tokens,
        output_price_per_1k_tokens: data.output_price_per_1k_tokens,
        currency: data.currency,
        context_window_tokens: data.context_window_tokens,
        notes: data.notes,
        is_enabled: false,
        price_last_updated_at: new Date(),
      };

      return await this.prisma.ai_models.create({
        data: {
          id: uuidv4(),
          ...dbData,
        },
      });
    } catch (error) {
      this.logger.error(`Failed to create model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create model: ${error.message}`);
    }
  }

  /**
   * 更新 AI 模型
   */
  async update(id: string, data: UpdateModelDto): Promise<AiModel> {
    const existingModel = await this.findOne(id);

    try {
      return await this.prisma.ai_models.update({
        where: { id },
        data: {
          ...data,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to update model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update model: ${error.message}`);
    }
  }

  /**
   * 刪除 AI 模型
   */
  async remove(id: string): Promise<AiModel> {
    const existingModel = await this.findOne(id);

    return await this.prisma.ai_models.delete({
      where: { id },
    });
  }

  /**
   * 更新模型狀態
   */
  async updateStatus(id: string, is_enabled: boolean): Promise<AiModel> {
    const existingModel = await this.findOne(id);

    return await this.prisma.ai_models.update({
      where: { id },
      data: { is_enabled },
    });
  }

  /**
   * 批量建立模型
   */
  async bulkCreate(provider: string, models: any[]): Promise<{ created: number; existed: number }> {
    let created = 0;
    let existed = 0;

    for (const model of models) {
      try {
        const existingModel = await this.prisma.ai_models.findUnique({
          where: {
            provider_model_name: {
              provider: provider,
              model_name: model.modelName,
            },
          },
        });

        if (!existingModel) {
          await this.prisma.ai_models.create({
            data: {
              id: uuidv4(),
              provider: provider,
              model_name: model.modelName,
              display_name: model.displayName || model.modelName,
              input_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              output_price_per_1k_tokens: 0, // 初始值，將通過 syncModelPricing 更新
              is_enabled: false, // 預設為停用
              currency: 'USD',
              price_last_updated_at: new Date(),
            },
          });
          created++;
        } else {
          existed++;
        }
      } catch (error) {
        this.logger.error(`建立模型 ${provider}:${model.modelName} 時發生錯誤: ${error.message}`);
        // 繼續處理其他模型，不中斷整個流程
      }
    }

    return { created, existed };
  }

  /**
   * 從 AI 提供商獲取模型列表並保存到資料庫
   * POST /api/admin/ai/models/fetchList
   */
  async fetchFromProviders(): Promise<{
    results: Array<{
      provider: string;
      created: number;
      existed: number;
      failed?: boolean;
      error?: string;
    }>;
  }> {
    // 獲取所有啟用的 API 金鑰
    const keys = await this.prisma.ai_keys.findMany({
      where: { is_enabled: true },
    });

    if (keys.length === 0) {
      throw new BadRequestException('沒有啟用的 API 金鑰可用，請先新增並啟用至少一個 API 金鑰');
    }

    type ResultItem = {
      provider: string;
      created: number;
      existed: number;
      failed?: boolean;
      error?: string;
    };

    const results: ResultItem[] = [];
    const providerTypes = {
      openai: 'OPENAI' as AiBotProviderType,
      anthropic: 'CLAUDE' as AiBotProviderType,
      'google-gemini': 'GEMINI' as AiBotProviderType,
      'openai-compatible': 'OPENAI_COMPATIBLE' as AiBotProviderType,
    };

    // 用於追蹤已處理的提供商，避免重複處理
    const processedProviders = new Set<string>();

    // 依序處理每個金鑰
    for (const key of keys) {
      // 如果此提供商已處理，則跳過
      if (processedProviders.has(key.provider)) {
        continue;
      }

      try {
        // 使用 API Key（假設已經是解密的）
        const decryptedKey = key.api_key;

        // 對照 provider 與 AiBotProviderType
        const providerType = providerTypes[key.provider as keyof typeof providerTypes];
        if (!providerType) {
          results.push({
            provider: key.provider,
            created: 0,
            existed: 0,
            failed: true,
            error: `不支援的提供商: ${key.provider}`,
          });
          continue;
        }

        // 建立提供商實例
        const provider = this.providerFactory.createProvider(
          providerType,
          decryptedKey,
          key.api_url || undefined,
        );

        // 獲取可用模型列表
        const models = await provider.getAvailableModels();

        // 將模型保存到資料庫
        const result = await this.bulkCreate(key.provider, models);

        results.push({
          provider: key.provider,
          created: result.created,
          existed: result.existed,
        });

        // 標記此提供商已處理
        processedProviders.add(key.provider);
      } catch (error) {
        this.logger.error(`從 ${key.provider} 獲取模型失敗: ${error.message}`, error.stack);
        results.push({
          provider: key.provider,
          created: 0,
          existed: 0,
          failed: true,
          error: `獲取模型失敗: ${error.message}`,
        });
      }
    }

    // 同步完成後，立即同步價格和 token 信息
    try {
      await this.syncModelPricing();
    } catch (error) {
      this.logger.warn('模型同步後自動同步價格失敗：', error.message);
    }

    return { results };
  }

  /**
   * 同步模型價格資訊
   */
  async syncModelPricing(): Promise<{
    success: boolean;
    updated: number;
    skipped: number;
    notFound: { provider: string; modelName: string }[];
    errors?: PriceError[];
  }> {
    try {
      // 先取得資料庫中所有已存在的模型
      const existingModels = await this.prisma.ai_models.findMany({
        select: {
          id: true,
          provider: true,
          model_name: true,
        },
      });

      // 建立快速查詢映射表
      const modelMap = new Map<string, string>(); // key: provider:model_name, value: id
      existingModels.forEach((model) => {
        const key = `${model.provider}:${model.model_name}`;
        modelMap.set(key, model.id);
      });

      // 使用爬蟲服務獲取所有模型的價格資訊
      const modelPrices = await this.priceCrawler.fetchAllModelPrices();

      // 更新資料庫中的模型價格
      let updatedCount = 0;
      let skippedCount = 0;
      const notFoundModels: { provider: string; modelName: string }[] = [];
      const errors: PriceError[] = [];

      for (const priceInfo of modelPrices) {
        try {
          // 產生查詢鍵
          const lookupKey = `${priceInfo.provider}:${priceInfo.modelName}`;
          const existingModelId = modelMap.get(lookupKey);

          if (existingModelId) {
            // 檢查價格是否有變動，避免不必要的更新
            const existingModel = await this.prisma.ai_models.findUnique({
              where: { id: existingModelId },
              select: {
                input_price_per_1k_tokens: true,
                output_price_per_1k_tokens: true,
                context_window_tokens: true,
              },
            });

            const priceChanged =
              Number(existingModel?.input_price_per_1k_tokens) !==
                priceInfo.inputPricePer1kTokens ||
              Number(existingModel?.output_price_per_1k_tokens) !==
                priceInfo.outputPricePer1kTokens ||
              (priceInfo.contextWindowTokens &&
                existingModel?.context_window_tokens !== priceInfo.contextWindowTokens);

            if (priceChanged) {
              // 更新現有模型的價格資訊
              await this.prisma.ai_models.update({
                where: { id: existingModelId },
                data: {
                  input_price_per_1k_tokens: priceInfo.inputPricePer1kTokens,
                  output_price_per_1k_tokens: priceInfo.outputPricePer1kTokens,
                  ...(priceInfo.contextWindowTokens && {
                    context_window_tokens: priceInfo.contextWindowTokens,
                  }),
                  price_last_updated_at: new Date(),
                },
              });
              updatedCount++;
              this.logger.log(
                `更新模型 ${priceInfo.provider}:${priceInfo.modelName} 的價格資訊成功`,
              );
            } else {
              skippedCount++;
              this.logger.debug(
                `模型 ${priceInfo.provider}:${priceInfo.modelName} 價格無變動，已跳過`,
              );
            }
          } else {
            // 記錄不存在的模型
            notFoundModels.push({
              provider: priceInfo.provider,
              modelName: priceInfo.modelName,
            });
            this.logger.warn(
              `模型 ${priceInfo.provider}:${priceInfo.modelName} 在系統中不存在，已跳過更新`,
            );
          }
        } catch (error) {
          this.logger.error(
            `更新模型 ${priceInfo.provider}:${priceInfo.modelName} 的價格時發生錯誤: ${error.message}`,
          );
          errors.push({
            provider: priceInfo.provider,
            modelName: priceInfo.modelName,
            error: error.message,
          });
        }
      }

      return {
        success: true,
        updated: updatedCount,
        skipped: skippedCount,
        notFound: notFoundModels,
        ...(errors.length > 0 && { errors }),
      };
    } catch (error) {
      this.logger.error(`同步模型價格時發生錯誤: ${error.message}`);
      return {
        success: false,
        updated: 0,
        skipped: 0,
        notFound: [],
        errors: [{ provider: 'general', modelName: 'all', error: error.message }],
      };
    }
  }
}
