import { Controller, Get, Query, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { AiUsageService } from './ai-usage.service';
import { AiUsageQueryDto, DetailedAiUsageStatisticsDto } from './dto/ai-usage.dto';

@ApiTags('admin/ai/usage')
@ApiBearerAuth()
@UseGuards(JwtAuthGuard)
@Controller('admin/ai/usage')
export class AiUsageController {
  constructor(private readonly aiUsageService: AiUsageService) {}

  @Get('statistics')
  @ApiOperation({ summary: '讀取 AI 使用統計' })
  async getStatistics(@Query() query: AiUsageQueryDto): Promise<DetailedAiUsageStatisticsDto> {
    return this.aiUsageService.getDetailedStatistics(query);
  }
}
