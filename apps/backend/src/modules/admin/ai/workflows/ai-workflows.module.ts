import { Modu<PERSON> } from '@nestjs/common';
import { AiWorkflowsService } from './ai-workflows.service';
import { AiWorkflowsController } from './ai-workflows.controller';
import { PrismaModule } from '@/modules/core/prisma/prisma.module';
import { AuthModule } from '@/modules/core/auth/auth.module';
import { CaslModule } from '@/casl/casl.module';

@Module({
  imports: [PrismaModule, AuthModule, CaslModule],
  controllers: [AiWorkflowsController],
  providers: [AiWorkflowsService],
  exports: [AiWorkflowsService],
})
export class AiWorkflowsModule {}
