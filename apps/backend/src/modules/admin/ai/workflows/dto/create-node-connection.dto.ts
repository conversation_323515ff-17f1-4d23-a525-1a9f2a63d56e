import { IsString, IsOptional, IsObject, IsBoolean } from 'class-validator';

export class CreateNodeConnectionDto {
  @IsString()
  source_node_id: string;

  @IsString()
  target_node_id: string;

  @IsOptional()
  @IsString()
  source_port?: string;

  @IsOptional()
  @IsString()
  target_port?: string;

  @IsOptional()
  @IsObject()
  config?: any;

  @IsOptional()
  @IsBoolean()
  is_enabled?: boolean = true;
}
