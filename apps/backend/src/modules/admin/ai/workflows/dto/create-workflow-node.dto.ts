import {
  IsString,
  IsO<PERSON>al,
  IsEnum,
  IsNumber,
  IsObject,
  IsBoolean,
  IsInt,
} from 'class-validator';
import { WorkflowNodeType } from '@prisma/client';

export class CreateWorkflowNodeDto {
  @IsString()
  name: string;

  @IsOptional()
  @IsString()
  description?: string;

  @IsEnum(WorkflowNodeType)
  node_type: WorkflowNodeType;

  @IsOptional()
  @IsNumber()
  position_x?: number = 0;

  @IsOptional()
  @IsNumber()
  position_y?: number = 0;

  @IsObject()
  config: any;

  @IsOptional()
  @IsInt()
  execution_order?: number;

  @IsOptional()
  @IsBoolean()
  is_enabled?: boolean = true;
}
