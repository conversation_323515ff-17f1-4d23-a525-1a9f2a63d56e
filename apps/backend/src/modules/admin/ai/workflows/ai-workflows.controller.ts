import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  UseGuards,
  Request,
} from '@nestjs/common';
import { AiWorkflowsService } from './ai-workflows.service';
import {
  CreateWorkflowDto,
  UpdateWorkflowDto,
  CreateWorkflowNodeDto,
  UpdateWorkflowNodeDto,
  CreateNodeConnectionDto,
  WorkflowQueryDto,
} from './dto';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { RolesGuard } from '@/modules/core/auth/guards/roles.guard';
import { Roles } from '@/modules/core/auth/decorators/roles.decorator';
import { Role } from '@/common/enums/role.enum';
import { ApiOperation } from '@nestjs/swagger';

@Controller('admin/ai/workflows')
@UseGuards(JwtAuthGuard, RolesGuard)
@Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
export class AiWorkflowsController {
  constructor(private readonly aiWorkflowsService: AiWorkflowsService) {}

  // 工作流程 CRUD 操作
  @Post()
  create(@Body() createWorkflowDto: CreateWorkflowDto, @Request() req: any) {
    return this.aiWorkflowsService.create(createWorkflowDto, req.user.id);
  }

  @Get()
  @ApiOperation({ summary: '查詢 AI 工作流程' })
  findAll(@Query() query: WorkflowQueryDto) {
    return this.aiWorkflowsService.findAll({
      tenant_id: query.tenant_id,
      workspaceId: query.workspaceId,
      status: query.status,
      visibility: query.visibility,
      isPublished: query.isPublished,
    });
  }

  @Get('node-types')
  getAvailableNodeTypes() {
    return this.aiWorkflowsService.getAvailableNodeTypes();
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.aiWorkflowsService.findOne(id);
  }

  @Patch(':id')
  update(
    @Param('id') id: string,
    @Body() updateWorkflowDto: UpdateWorkflowDto,
    @Request() req: any,
  ) {
    return this.aiWorkflowsService.update(id, updateWorkflowDto, req.user.id);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req: any) {
    return this.aiWorkflowsService.remove(id, req.user.id);
  }

  // 工作流程發布管理
  @Post(':id/publish')
  publish(@Param('id') id: string, @Request() req: any) {
    return this.aiWorkflowsService.publish(id, req.user.id);
  }

  @Post(':id/unpublish')
  unpublish(@Param('id') id: string, @Request() req: any) {
    return this.aiWorkflowsService.unpublish(id, req.user.id);
  }

  @Post(':id/duplicate')
  duplicate(
    @Param('id') id: string,
    @Body() data: { name: string; description?: string },
    @Request() req: any,
  ) {
    return this.aiWorkflowsService.duplicate(id, req.user.id, data);
  }

  // 工作流程節點管理
  @Post(':workflowId/nodes')
  createNode(
    @Param('workflowId') workflowId: string,
    @Body() createNodeDto: CreateWorkflowNodeDto,
    @Request() req: any,
  ) {
    return this.aiWorkflowsService.createNode(workflowId, createNodeDto, req.user.id);
  }

  @Patch('nodes/:nodeId')
  updateNode(
    @Param('nodeId') nodeId: string,
    @Body() updateNodeDto: UpdateWorkflowNodeDto,
    @Request() req: any,
  ) {
    return this.aiWorkflowsService.updateNode(nodeId, updateNodeDto);
  }

  @Delete('nodes/:nodeId')
  removeNode(@Param('nodeId') nodeId: string, @Request() req: any) {
    return this.aiWorkflowsService.removeNode(nodeId, req.user.id);
  }

  // 節點連接管理
  @Post('connections')
  createConnection(@Body() createConnectionDto: CreateNodeConnectionDto, @Request() req: any) {
    return this.aiWorkflowsService.createConnection(createConnectionDto, req.user.id);
  }

  @Delete('connections/:connectionId')
  removeConnection(@Param('connectionId') connectionId: string, @Request() req: any) {
    return this.aiWorkflowsService.removeConnection(connectionId, req.user.id);
  }
}
