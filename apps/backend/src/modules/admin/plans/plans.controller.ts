import { Controller, Get, Post, Body, Put, Param, Delete, UseGuards } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth, ApiParam } from '@nestjs/swagger';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { Roles } from '../../core/auth/decorators/roles.decorator';
import { Role } from '../../../common/enums/role.enum';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { PlansService } from './plans.service';
import { CreatePlanDto } from './dto/create-plan.dto';
import { UpdatePlanDto } from './dto/update-plan.dto';

@ApiTags('admin/plans')
@ApiBearerAuth()
@Controller('admin/plans')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class PlansController {
  constructor(private readonly plansService: PlansService) {}

  @Post()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.PLAN))
  @ApiOperation({ summary: '建立訂閱方案' })
  @ApiResponse({ status: 201, description: '訂閱方案建立成功' })
  async create(@Body() createPlanDto: CreatePlanDto) {
    const plan = await this.plansService.create(createPlanDto);
    return { data: plan };
  }

  @Get()
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PLAN))
  @ApiOperation({ summary: '取得所有訂閱方案' })
  @ApiResponse({ status: 200, description: '成功取得訂閱方案列表' })
  async findAll() {
    const plans = await this.plansService.findAll();
    return { data: plans };
  }

  @Get(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PLAN))
  @ApiOperation({ summary: '取得單一訂閱方案' })
  @ApiResponse({ status: 200, description: '成功取得訂閱方案資訊' })
  @ApiParam({ name: 'id', description: '方案ID' })
  findOne(@Param('id') id: string) {
    return this.plansService.findOne(id);
  }

  @Put(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.PLAN))
  @ApiOperation({ summary: '更新訂閱方案' })
  @ApiResponse({ status: 200, description: '訂閱方案更新成功' })
  @ApiParam({ name: 'id', description: '方案ID' })
  update(@Param('id') id: string, @Body() updatePlanDto: UpdatePlanDto) {
    return this.plansService.update(id, updatePlanDto);
  }

  @Delete(':id')
  @Roles(Role.SUPER_ADMIN, Role.SYSTEM_ADMIN)
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.PLAN))
  @ApiOperation({ summary: '刪除訂閱方案' })
  @ApiResponse({ status: 200, description: '訂閱方案刪除成功' })
  @ApiParam({ name: 'id', description: '方案ID' })
  remove(@Param('id') id: string) {
    return this.plansService.remove(id);
  }
}
