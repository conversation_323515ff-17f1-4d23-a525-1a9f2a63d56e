import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateAiModelDto } from './dto/create-ai-model.dto';
import { UpdateAiModelDto } from './dto/update-ai-model.dto';
import { ai_models as AiModel } from '@prisma/client';
import { v4 as uuidv4 } from 'uuid';

@Injectable()
export class AiModelsService {
  private readonly logger = new Logger(AiModelsService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 取得所有 AI 模型
   */
  async findAll(tenantId?: string): Promise<AiModel[]> {
    try {
      const where = tenantId ? { tenant_id: tenantId } : {};
      
      return await this.prisma.ai_models.findMany({
        where,
        include: {
          ai_keys: true,
          tenants: true,
        },
        orderBy: { created_at: 'desc' },
      });
    } catch (error) {
      this.logger.error(`Failed to fetch models: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch models: ${error.message}`);
    }
  }

  /**
   * 根據 ID 取得單一 AI 模型
   */
  async findOne(id: string): Promise<AiModel> {
    try {
      const model = await this.prisma.ai_models.findUnique({
        where: { id },
        include: {
          ai_keys: true,
          tenants: true,
        },
      });

      if (!model) {
        throw new NotFoundException(`AI model with ID ${id} not found`);
      }

      return model;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to fetch model: ${error.message}`);
    }
  }

  /**
   * 建立新的 AI 模型
   */
  async create(data: CreateAiModelDto): Promise<AiModel> {
    try {
      const existingModel = await this.prisma.ai_models.findUnique({
        where: {
          provider_model_name: {
            provider: data.provider,
            model_name: data.model_name,
          },
        },
      });

      if (existingModel) {
        throw new ConflictException('A model with the same name already exists for this provider.');
      }

      const dbData = {
        provider: data.provider,
        model_name: data.model_name,
        display_name: data.display_name,
        input_price_per_1k_tokens: data.input_price_per_1k_tokens || 0,
        output_price_per_1k_tokens: data.output_price_per_1k_tokens || 0,
        currency: data.currency || 'USD',
        context_window_tokens: data.context_window_tokens,
        notes: data.notes,
        is_enabled: data.is_enabled || false,
        tenant_id: data.tenant_id || null,
        ai_key_id: data.ai_key_id || null,
        config: data.config || null,
        price_last_updated_at: new Date(),
      };

      return await this.prisma.ai_models.create({
        data: {
          id: uuidv4(),
          ...dbData,
        },
      });
    } catch (error) {
      if (error instanceof ConflictException) {
        throw error;
      }
      this.logger.error(`Failed to create model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to create model: ${error.message}`);
    }
  }

  /**
   * 更新 AI 模型
   */
  async update(id: string, data: UpdateAiModelDto): Promise<AiModel> {
    const existingModel = await this.findOne(id);

    try {
      return await this.prisma.ai_models.update({
        where: { id },
        data: {
          ...data,
          updated_at: new Date(),
        },
      });
    } catch (error) {
      this.logger.error(`Failed to update model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to update model: ${error.message}`);
    }
  }

  /**
   * 刪除 AI 模型
   */
  async remove(id: string): Promise<void> {
    const existingModel = await this.findOne(id);

    try {
      await this.prisma.ai_models.delete({
        where: { id },
      });
    } catch (error) {
      this.logger.error(`Failed to delete model: ${error.message}`, error.stack);
      throw new BadRequestException(`Failed to delete model: ${error.message}`);
    }
  }

  /**
   * 批量建立模型
   */
  async bulkCreate(provider: string, models: any[]): Promise<{ created: number; existed: number }> {
    let created = 0;
    let existed = 0;

    for (const model of models) {
      try {
        const existingModel = await this.prisma.ai_models.findUnique({
          where: {
            provider_model_name: {
              provider: provider,
              model_name: model.modelName,
            },
          },
        });

        if (!existingModel) {
          await this.prisma.ai_models.create({
            data: {
              id: uuidv4(),
              provider: provider,
              model_name: model.modelName,
              display_name: model.displayName || model.modelName,
              input_price_per_1k_tokens: 0,
              output_price_per_1k_tokens: 0,
              is_enabled: false,
              currency: 'USD',
              price_last_updated_at: new Date(),
            },
          });
          created++;
        } else {
          existed++;
        }
      } catch (error) {
        this.logger.error(`建立模型 ${provider}:${model.modelName} 時發生錯誤: ${error.message}`);
      }
    }

    return { created, existed };
  }
}
