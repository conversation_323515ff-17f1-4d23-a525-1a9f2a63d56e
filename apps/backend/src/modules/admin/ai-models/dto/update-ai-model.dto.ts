import { ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNumber, IsOptional, IsString } from 'class-validator';
import { Type } from 'class-transformer';

export class UpdateAiModelDto {
  @ApiPropertyOptional({ description: '顯示名稱' })
  @IsString()
  @IsOptional()
  display_name?: string;

  @ApiPropertyOptional({ description: '是否啟用' })
  @IsBoolean()
  @IsOptional()
  is_enabled?: boolean;

  @ApiPropertyOptional({ description: '輸入 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  input_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '輸出 tokens 每 1k 的價格' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  output_price_per_1k_tokens?: number;

  @ApiPropertyOptional({ description: '貨幣' })
  @IsString()
  @IsOptional()
  currency?: string;

  @ApiPropertyOptional({ description: 'Context Window 大小 (tokens)' })
  @IsNumber()
  @IsOptional()
  @Type(() => Number)
  context_window_tokens?: number;

  @ApiPropertyOptional({ description: '備註' })
  @IsString()
  @IsOptional()
  notes?: string;

  @ApiPropertyOptional({ description: '租戶 ID (留空表示系統級)' })
  @IsString()
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({ description: 'AI Key ID (關聯的 API Key)' })
  @IsString()
  @IsOptional()
  ai_key_id?: string;

  @ApiPropertyOptional({ description: '模型配置 (JSON 格式)' })
  @IsOptional()
  config?: any;
}
