import { Controller, Get, Param, ParseIntPipe, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBearerAuth } from '@nestjs/swagger';
import { DashboardService } from './dashboard.service';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import { DashboardStats, Activity } from '@auth/shared/types';

@ApiTags('Admin - Dashboard')
@ApiBearerAuth()
@Controller('admin/dashboard')
export class DashboardController {
  constructor(private readonly dashboardService: DashboardService) {}

  @Get('stats')
  @CheckPolicies((ability: AppAbility) => 
    ability.can(Actions.MANAGE, 'all') || 
    ability.can(Actions.READ, Subjects.DASHBOARD) || 
    ability.can(Actions.READ, Subjects.DASHBOARD_STATS)
  )
  @ApiOperation({ summary: '讀取儀表板統計資料' })
  @ApiResponse({
    status: 200,
    description: '成功讀取儀表板統計資料',
    schema: {
      type: 'object',
      properties: {
        tenantsCount: { type: 'number', example: 24 },
        usersCount: { type: 'number', example: 142 },
        ordersCount: { type: 'number', example: 36 },
        plansCount: { type: 'number', example: 5 },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  getStats() {
    return this.dashboardService.getStats();
  }

  @Get('recent-tenants')
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, 'all') || ability.can(Actions.READ, Subjects.DASHBOARD) || ability.can(Actions.READ, Subjects.DASHBOARD_RECENT_TENANTS),
  )
  @ApiOperation({ summary: '讀取最近租戶' })
  @ApiResponse({
    status: 200,
    description: '成功讀取最近租戶資料',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          name: { type: 'string' },
          status: { type: 'string' },
          created_at: { type: 'string', format: 'date-time' },
        },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  getRecentTenants() {
    return this.dashboardService.getRecentTenants();
  }

  @Get('recent-orders')
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, 'all') || ability.can(Actions.READ, Subjects.DASHBOARD) || ability.can(Actions.READ, Subjects.DASHBOARD_RECENT_ORDERS),
  )
  @ApiOperation({ summary: '讀取最近訂單' })
  @ApiResponse({
    status: 200,
    description: '成功讀取最近訂單資料',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'string' },
          status: { type: 'string' },
          amount: { type: 'number' },
          created_at: { type: 'string', format: 'date-time' },
          tenant: {
            type: 'object',
            properties: {
              name: { type: 'string' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  getRecentOrders() {
    return this.dashboardService.getRecentOrders();
  }

  @Get('active-users')
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, 'all') || ability.can(Actions.READ, Subjects.DASHBOARD) || ability.can(Actions.READ, Subjects.DASHBOARD_ACTIVE_USERS),
  )
  @ApiOperation({ summary: '讀取活躍使用者統計' })
  @ApiResponse({
    status: 200,
    description: '成功讀取活躍使用者統計',
    schema: {
      type: 'object',
      properties: {
        activeUsers: { type: 'number', example: 89 },
        totalUsers: { type: 'number', example: 142 },
        percentage: { type: 'number', example: 63 },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  getActiveUsers() {
    return this.dashboardService.getActiveUsers();
  }

  @Get('revenue')
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, 'all') || ability.can(Actions.READ, Subjects.DASHBOARD) || ability.can(Actions.READ, Subjects.DASHBOARD_REVENUE),
  )
  @ApiOperation({ summary: '讀取收入統計' })
  @ApiResponse({
    status: 200,
    description: '成功讀取收入統計',
    schema: {
      type: 'object',
      properties: {
        totalRevenue: { type: 'number', example: 24560 },
        currentMonthRevenue: { type: 'number', example: 4350 },
        monthlyRevenue: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              month: { type: 'string', example: '2025-04' },
              value: { type: 'number', example: 3520 },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  async getRevenue(): Promise<{
    totalRevenue: number;
    currentMonthRevenue: number;
    monthlyRevenue: DashboardStats[];
  }> {
    return this.dashboardService.getRevenue();
  }

  @Get('activity')
  @CheckPolicies((ability: AppAbility) =>
    ability.can(Actions.MANAGE, 'all') || ability.can(Actions.READ, Subjects.DASHBOARD) || ability.can(Actions.READ, Subjects.DASHBOARD_ACTIVITY),
  )
  @ApiOperation({ summary: '讀取系統活動統計' })
  @ApiResponse({
    status: 200,
    description: '成功讀取系統活動統計',
    schema: {
      type: 'object',
      properties: {
        summary: {
          type: 'object',
          properties: {
            newTenants: { type: 'number', example: 5 },
            newUsers: { type: 'number', example: 18 },
            newSubscriptions: { type: 'number', example: 12 },
            activeUsers: { type: 'number', example: 87 },
          },
        },
        activityLogs: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              date: { type: 'string', format: 'date' },
              users: { type: 'number' },
              tenants: { type: 'number' },
              subscriptions: { type: 'number' },
              total: { type: 'number' },
            },
          },
        },
      },
    },
  })
  @ApiResponse({ status: 403, description: '權限不足' })
  async getActivity(): Promise<{
    summary: {
      newTenants: number;
      newUsers: number;
      newSubscriptions: number;
      activeUsers: number;
    };
    activityLogs: Activity[];
  }> {
    return this.dashboardService.getActivity();
  }
}
