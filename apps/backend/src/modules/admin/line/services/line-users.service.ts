import { Injectable, Logger } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { line_users, Prisma } from '@prisma/client';
import { Profile } from '@line/bot-sdk';

@Injectable()
export class LineUsersService {
  private readonly logger = new Logger(LineUsersService.name);

  constructor(private readonly prisma: PrismaService) {}

  async findByLineUserId(lineUserId: string): Promise<line_users | null> {
    return this.prisma.line_users.findUnique({
      where: { line_user_id: lineUserId },
    });
  }

  async findOrCreateUser(
    lineUserId: string,
    getProfile: () => Promise<Profile>,
  ): Promise<line_users> {
    const existingUser = await this.findByLineUserId(lineUserId);
    if (existingUser) {
      return existingUser;
    }

    this.logger.log(`Creating new Line user for ID: ${lineUserId}`);
    try {
      const profile = await getProfile();
      const newUser = await this.prisma.line_users.create({
        data: {
          line_user_id: lineUserId,
          display_name: profile.displayName,
          picture_url: profile.pictureUrl,
          status_message: profile.statusMessage,
          last_interaction_at: new Date(),
        },
      });
      this.logger.log(`Successfully created new Line user: ${newUser.id}`);
      return newUser;
    } catch (error) {
      this.logger.error(
        `Failed to fetch profile or create user for ${lineUserId}: ${error.message}`,
        error.stack,
      );
      throw new Error('Could not create Line user.');
    }
  }

  async handleFollowEvent(
    lineUserId: string,
    getProfile: () => Promise<Profile>,
  ): Promise<line_users> {
    const user = await this.findByLineUserId(lineUserId);
    if (user) {
      this.logger.log(`User ${lineUserId} re-followed. Updating status.`);
      return this.prisma.line_users.update({
        where: { id: user.id },
        data: { is_following: true, last_interaction_at: new Date() },
      });
    } else {
      this.logger.log(`New user followed: ${lineUserId}. Creating record.`);
      return this.findOrCreateUser(lineUserId, getProfile);
    }
  }

  async handleUnfollowEvent(lineUserId: string): Promise<void> {
    const user = await this.findByLineUserId(lineUserId);
    if (user) {
      this.logger.log(`User ${lineUserId} unfollowed. Updating status.`);
      await this.prisma.line_users.update({
        where: { id: user.id },
        data: { is_following: false },
      });
    } else {
      this.logger.warn(`Received unfollow event for non-existent user: ${lineUserId}`);
    }
  }
}
