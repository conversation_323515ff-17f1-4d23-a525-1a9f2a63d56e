import { IsString, IsOptional, Is<PERSON>rray, IsBoolean, IsEnum, ValidateIf } from 'class-validator';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export enum RoleScope {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
  WORKSPACE = 'WORKSPACE',
}

export class CreateRoleDto {
  @ApiProperty({ description: '角色名稱 (系統識別碼)' })
  @IsString()
  name: string;

  @ApiProperty({ description: '角色顯示名稱' })
  @IsString()
  display_name: string;

  @ApiPropertyOptional({ description: '角色描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({
    description: '角色使用區域',
    enum: RoleScope,
    example: RoleScope.TENANT,
  })
  @IsEnum(RoleScope)
  scope: RoleScope;

  @ApiPropertyOptional({
    description: '租戶 ID (當 scope 為 TENANT 或 WORKSPACE 時需要)',
  })
  @IsString()
  @ValidateIf((o) => o.scope === RoleScope.TENANT || o.scope === RoleScope.WORKSPACE)
  @IsOptional()
  tenant_id?: string;

  @ApiPropertyOptional({
    description: '角色權限列表 (Permission IDs)',
    type: [String],
  })
  @IsArray()
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({ description: '是否為系統角色', default: false })
  @IsBoolean()
  @IsOptional()
  is_system_role?: boolean;

  @ApiPropertyOptional({ description: '父角色 ID (可選)' })
  @IsString()
  @IsOptional()
  parent_role_id?: string;
}

export class UpdateRoleDto {
  @ApiPropertyOptional({ description: '角色名稱 (系統識別碼)' })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiPropertyOptional({ description: '角色顯示名稱' })
  @IsString()
  @IsOptional()
  display_name?: string;

  @ApiPropertyOptional({ description: '角色描述' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '角色使用區域',
    enum: RoleScope,
  })
  @IsEnum(RoleScope)
  @IsOptional()
  scope?: RoleScope;

  @ApiPropertyOptional({
    description: '租戶 ID (當 scope 為 TENANT 或 WORKSPACE 時需要)',
  })
  @IsString()
  @IsOptional()
  @ValidateIf((o) => o.scope === RoleScope.TENANT || o.scope === RoleScope.WORKSPACE)
  tenant_id?: string;

  @ApiPropertyOptional({
    description: '角色權限列表 (Permission IDs)',
    type: [String],
  })
  @IsArray()
  @IsOptional()
  permissions?: string[];

  @ApiPropertyOptional({ description: '是否為系統角色' })
  @IsBoolean()
  @IsOptional()
  is_system_role?: boolean;

  @ApiPropertyOptional({ description: '父角色 ID (可選)' })
  @IsString()
  @IsOptional()
  parent_role_id?: string;
}

export class UpdateRolePermissionsDto {
  @ApiProperty({ description: '角色權限列表 (Permission IDs)', type: [String] })
  @IsArray()
  permissions: string[];
}

export class RoleResponseDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  display_name: string;

  @ApiPropertyOptional({ nullable: true })
  @IsOptional()
  description?: string | null;

  @ApiProperty()
  is_system: boolean;

  @ApiProperty({ enum: RoleScope })
  scope: RoleScope;

  @ApiPropertyOptional({ description: '租戶 ID' })
  @IsString()
  @IsOptional()
  tenant_id?: string | null;

  @ApiProperty()
  created_at: Date;

  @ApiProperty()
  updated_at: Date;
}

export class RoleWithPermissionsResponseDto extends RoleResponseDto {
  @ApiProperty({ type: [String] })
  permissions: string[];
}

export class PermissionCategoryDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiPropertyOptional()
  icon?: string;

  @ApiPropertyOptional()
  sortOrder?: number;

  @ApiPropertyOptional()
  isActive?: boolean;
}

export class PermissionDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  name: string;

  @ApiProperty()
  description: string;

  @ApiProperty()
  category_id: string;
}

export class RoleUserCountResponseDto {
  @ApiProperty()
  count: number;
}

export enum UserType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

export interface UserRoleInfo {
  user_id: string;
  user_type: UserType;
  user: {
    id: string;
    email: string;
    name?: string;
    status: string;
  };
  roles: {
    id: string;
    name: string;
    display_name: string;
    description?: string;
    scope: RoleScope;
    is_system: boolean;
    assignedAt: Date;
  }[];
  tenant?: {
    id: string;
    name: string;
  };
}

export interface RoleHierarchyInfo {
  role_id: string;
  parent_role_id: string | null;
  child_role_ids: string[];
  level: number;
  direct_permissions: string[];
  inherited_permissions: string[];
}

export interface RoleUserStatistics {
  total_users: number;
  system_users: number;
  tenant_users: number;
  users_by_tenant?: Record<string, number>;
}

export interface CompleteRoleInfo {
  role: any;
  hierarchy: RoleHierarchyInfo;
  users: {
    total: UserRoleInfo[];
    system_users: UserRoleInfo[];
    tenant_users: UserRoleInfo[];
    by_tenant: {
      tenant_id: string;
      tenant_name: string;
      users: UserRoleInfo[];
    }[];
  };
  permissions: {
    direct: string[];
    inherited: string[];
    effective: string[];
  };
  statistics: RoleUserStatistics;
  metadata: {
    last_updated: Date;
    analysis_date: Date;
  };
}

export interface RoleSummary {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  scope: RoleScope;
  is_system: boolean;
  user_count: number;
  permission_count: number;
  hierarchy_level: number;
  has_children: boolean;
  has_parent: boolean;
  created_at: Date;
  updated_at: Date;
}

export interface BatchRoleSummaryResult {
  successful: RoleSummary[];
  failed: {
    roleId: string;
    error: string;
  }[];
  total_requested: number;
  success_count: number;
  failure_count: number;
}

export interface RoleHealthCheck {
  role_id: string;
  is_healthy: boolean;
  issues: string[];
  warnings: string[];
  recommendations: string[];
  checked_at: Date;
}

export interface RoleStatistics {
  user_count: number;
  system_users: number;
  tenant_users: number;
  direct_permissions: number;
  inherited_permissions: number;
  effective_permissions: number;
  hierarchy_level: number;
  child_roles_count: number;
  has_parent_role: boolean;
}

export interface RolePermissionMappingDto {
  role_id: string;
  permission_id: string;
  granted_at: Date;
  granted_by: string;
}
