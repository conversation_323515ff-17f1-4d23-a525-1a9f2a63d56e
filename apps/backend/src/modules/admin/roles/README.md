# 角色指派和管理服務

HorizAI SaaS 平台的完整角色指派和管理系統，支援系統用戶和租戶用戶的角色管理。

## 🎯 功能特色

- **統一的角色指派管理** - 支援系統用戶和租戶用戶的角色指派
- **角色層級管理** - 支援父子角色關係和權限繼承
- **批量操作** - 支援批量角色指派、移除和替換
- **權限檢查** - 提供用戶權限檢查和驗證功能
- **角色衝突檢測** - 自動檢測和防止角色衝突
- **完整的審計記錄** - 記錄所有角色變更歷史

## 🏗️ 架構概覽

### 核心服務

1. **RoleAssignmentService** - 角色指派核心服務
2. **RoleHierarchyService** - 角色層級管理服務
3. **UserRoleService** - 用戶角色管理服務

### 資料庫結構

- `roles` - 角色定義表
- `system_user_roles` - 系統用戶角色關聯表
- `tenant_user_roles` - 租戶用戶角色關聯表
- `role_permissions` - 角色權限關聯表

## 🚀 快速開始

### 基本角色指派

```typescript
import { RoleAssignmentService, UserType } from '@/modules/admin/roles';

// 指派角色給系統用戶
await roleAssignmentService.assignRoles({
  userId: 'system-user-1',
  userType: UserType.SYSTEM,
  roleIds: ['SYSTEM_ADMIN', 'USER_MANAGER'],
  reason: '新管理員入職',
  assignedBy: 'super-admin',
});

// 指派角色給租戶用戶
await roleAssignmentService.assignRoles({
  userId: 'tenant-user-1',
  userType: UserType.TENANT,
  roleIds: ['TENANT_ADMIN'],
  tenantId: 'tenant-123',
  reason: '升級為租戶管理員',
  assignedBy: 'system-admin',
});
```

### 角色移除和替換

```typescript
// 移除特定角色
await roleAssignmentService.removeRoles({
  userId: 'user-1',
  userType: UserType.TENANT,
  roleIds: ['OLD_ROLE'],
  reason: '職位調整',
  removedBy: 'admin',
});

// 替換所有角色
await roleAssignmentService.replaceRoles({
  userId: 'user-1',
  userType: UserType.TENANT,
  roleIds: ['NEW_ROLE_1', 'NEW_ROLE_2'],
  tenantId: 'tenant-123',
  reason: '角色重組',
  assignedBy: 'admin',
});
```

### 批量操作

```typescript
// 批量角色操作
await roleAssignmentService.batchRoleOperations({
  operations: [
    {
      userId: 'user-1',
      userType: UserType.TENANT,
      action: 'assign',
      roleIds: ['ROLE_A'],
      tenantId: 'tenant-123',
    },
    {
      userId: 'user-2',
      userType: UserType.TENANT,
      action: 'remove',
      roleIds: ['ROLE_B'],
      tenantId: 'tenant-123',
    },
  ],
  reason: '組織架構調整',
  operatedBy: 'admin',
});
```

## 🔗 角色層級管理

### 設置父子角色關係

```typescript
import { RoleHierarchyService } from '@/modules/admin/roles';

// 設置父角色
await roleHierarchyService.setParentRole('MANAGER', 'ADMIN');

// 移除父角色關係
await roleHierarchyService.setParentRole('MANAGER', null);
```

### 獲取角色層級資訊

```typescript
// 獲取角色層級詳情
const hierarchy = await roleHierarchyService.getRoleHierarchy('MANAGER');
console.log({
  roleId: hierarchy.roleId,
  parentRole: hierarchy.parentRoleId,
  childRoles: hierarchy.childRoleIds,
  level: hierarchy.level,
  directPermissions: hierarchy.directPermissions,
  inheritedPermissions: hierarchy.inheritedPermissions,
});

// 獲取所有子角色
const childRoles = await roleHierarchyService.getAllChildRoles('ADMIN');

// 獲取所有父角色
const parentRoles = await roleHierarchyService.getAllParentRoles('USER');
```

## 👤 用戶角色管理

### 權限檢查

```typescript
import { UserRoleService, UserType } from '@/modules/admin/roles';

// 檢查用戶是否有特定權限
const hasPermission = await userRoleService.userHasPermission(
  'user-1',
  UserType.TENANT,
  'users:read',
);

// 檢查用戶是否有任一權限
const hasAnyPermission = await userRoleService.userHasAnyPermission('user-1', UserType.TENANT, [
  'users:read',
  'users:write',
]);

// 檢查用戶是否有所有權限
const hasAllPermissions = await userRoleService.userHasAllPermissions('user-1', UserType.TENANT, [
  'users:read',
  'users:write',
]);
```

### 獲取用戶有效權限

```typescript
// 獲取用戶的所有有效權限（包含繼承權限）
const effectivePermissions = await userRoleService.getUserEffectivePermissions(
  'user-1',
  UserType.TENANT,
);

// 獲取用戶角色層級資訊
const roleHierarchy = await userRoleService.getUserRoleHierarchy('user-1', UserType.TENANT);
```

### 用戶查詢

```typescript
// 根據範圍獲取用戶
const systemUsers = await userRoleService.getUsersByScope(RoleScope.SYSTEM);
const tenantUsers = await userRoleService.getUsersByScope(RoleScope.TENANT, 'tenant-123');

// 獲取具有特定角色的用戶
const adminUsers = await userRoleService.getUsersByRole('ADMIN');

// 獲取具有特定權限的用戶
const usersWithPermission = await userRoleService.getUsersByPermission('users:manage');
```

## 🌐 API 端點

### 角色指派 API

```bash
# 指派角色
POST /admin/role-assignment/assign
{
  "userId": "user-123",
  "userType": "tenant",
  "roleIds": ["TENANT_ADMIN"],
  "tenantId": "tenant-123",
  "reason": "升級為管理員"
}

# 移除角色
POST /admin/role-assignment/remove
{
  "userId": "user-123",
  "userType": "tenant",
  "roleIds": ["OLD_ROLE"],
  "reason": "職位調整"
}

# 替換角色
PUT /admin/role-assignment/replace
{
  "userId": "user-123",
  "userType": "tenant",
  "roleIds": ["NEW_ROLE"],
  "tenantId": "tenant-123",
  "reason": "角色重組"
}

# 批量操作
POST /admin/role-assignment/batch
{
  "operations": [
    {
      "userId": "user-1",
      "userType": "tenant",
      "action": "assign",
      "roleIds": ["ROLE_A"],
      "tenantId": "tenant-123"
    }
  ],
  "reason": "批量調整"
}
```

### 查詢 API

```bash
# 獲取用戶角色資訊
GET /admin/role-assignment/user/{userId}/{userType}

# 獲取用戶角色層級
GET /admin/role-assignment/user/{userId}/{userType}/hierarchy

# 獲取用戶有效權限
GET /admin/role-assignment/user/{userId}/{userType}/permissions

# 檢查用戶權限
POST /admin/role-assignment/check-permission
{
  "userId": "user-123",
  "userType": "tenant",
  "permissionId": "users:read"
}

# 批量檢查權限
POST /admin/role-assignment/check-permissions
{
  "userId": "user-123",
  "userType": "tenant",
  "permissionIds": ["users:read", "users:write"],
  "mode": "any"
}
```

### 層級管理 API

```bash
# 設置父角色
PUT /admin/role-assignment/hierarchy/parent
{
  "roleId": "MANAGER",
  "parentRoleId": "ADMIN"
}

# 獲取角色層級資訊
GET /admin/role-assignment/hierarchy/{roleId}

# 驗證層級結構
GET /admin/role-assignment/hierarchy/validate
```

## 🔄 統一角色管理 API

統一角色管理控制器提供了聚合型 API 端點：

```
GET    /admin/roles/system-overview           # 系統概覽
GET    /admin/roles/suggestions               # 最佳化建議
GET    /admin/roles/compare/:id1/:id2         # 角色比較
GET    /admin/roles/:id/complete              # 完整角色資訊
GET    /admin/roles/:id/summary               # 角色摘要
POST   /admin/roles/batch-summaries           # 批量摘要
GET    /admin/roles/:id/health-check          # 健康檢查
GET    /admin/roles/:id/usage-analysis        # 使用分析
POST   /admin/roles/batch-health-check        # 批量健康檢查
```

## 🔒 權限控制

所有 API 端點都受到 CASL 權限系統保護：

- **角色管理操作** - 需要 `manage:Role` 權限
- **角色查詢操作** - 需要 `read:Role` 權限
- **租戶隔離** - 自動驗證租戶上下文

## 🧪 測試

### 單元測試

```bash
# 運行角色指派服務測試
npm test role-assignment.service.spec.ts

# 運行角色層級服務測試
npm test role-hierarchy.service.spec.ts

# 運行用戶角色服務測試
npm test user-role.service.spec.ts
```

### 整合測試

```bash
# 運行完整的角色管理測試套件
npm test modules/admin/roles
```

## 🚨 最佳實踐

### 1. 角色設計原則

- **最小權限原則** - 只授予必要的權限
- **角色分離** - 系統角色和租戶角色分開管理
- **層級設計** - 合理設計角色繼承關係

### 2. 安全考量

```typescript
// ✅ 好的做法 - 驗證租戶上下文
await roleAssignmentService.assignRoles({
  userId: 'tenant-user-1',
  userType: UserType.TENANT,
  roleIds: ['TENANT_ADMIN'],
  tenantId: user.tenantId, // 確保租戶匹配
});

// ❌ 避免 - 跨租戶角色指派
await roleAssignmentService.assignRoles({
  userId: 'tenant-user-1',
  userType: UserType.TENANT,
  roleIds: ['TENANT_ADMIN'],
  tenantId: 'different-tenant', // 安全風險
});
```

### 3. 效能最佳化

```typescript
// ✅ 使用批量操作
await roleAssignmentService.batchRoleOperations({
  operations: multipleOperations,
  reason: 'Bulk update',
});

// ❌ 避免循環調用
for (const operation of operations) {
  await roleAssignmentService.assignRoles(operation); // 效能差
}
```

### 4. 錯誤處理

```typescript
try {
  const result = await roleAssignmentService.assignRoles(request);
  if (!result.success) {
    logger.error('Role assignment failed:', result.error);
    // 處理失敗情況
  }
} catch (error) {
  logger.error('Unexpected error:', error);
  // 處理異常
}
```

## 📊 監控和日誌

### 關鍵指標

- 角色指派成功率
- 權限檢查響應時間
- 角色衝突檢測次數
- 批量操作效能

### 日誌級別

- `DEBUG` - 詳細的操作過程
- `INFO` - 成功的操作記錄
- `WARN` - 角色衝突和異常情況
- `ERROR` - 操作失敗和系統錯誤

## 🔄 與 CASL 權限系統整合

角色指派服務與 CASL 權限系統完全整合：

1. **自動權限同步** - 角色變更時自動更新用戶權限
2. **即時權限檢查** - 支援即時的權限驗證
3. **快取管理** - 提供權限快取同步功能
4. **審計追蹤** - 完整的權限變更記錄

這個系統為 HorizAI SaaS 平台提供了強健、靈活且安全的角色管理基礎設施！

## 重構 API 端點變更說明 (2024-06-20)

為了簡化架構並統一角色管理相關 API，我們進行了以下變更：

### 合併的控制器

1. 將 `UnifiedRoleController` (`/admin/unified-roles/...`) 合併到 `RolesController` (`/admin/roles/...`)

### 端點映射對照表

| 舊端點                                               | 新端點                                        | 功能               |
| ---------------------------------------------------- | --------------------------------------------- | ------------------ |
| `GET /admin/unified-roles/system-overview`           | `GET /admin/roles/system-overview`            | 獲取角色系統概覽   |
| `GET /admin/unified-roles/optimization-suggestions`  | `GET /admin/roles/suggestions`                | 獲取角色最佳化建議 |
| `GET /admin/unified-roles/compare/:id1/:id2`         | `GET /admin/roles/compare/:id1/:id2`          | 比較兩個角色       |
| `GET /admin/unified-roles/roles/:id/complete`        | `GET /admin/roles/:id/complete`               | 獲取角色完整資訊   |
| `GET /admin/unified-roles/roles/:id/summary`         | `GET /admin/roles/:id/summary`                | 獲取角色摘要資訊   |
| `POST /admin/unified-roles/roles/batch-summaries`    | `POST /admin/roles/batch-summaries`           | 批量獲取角色摘要   |
| `GET /admin/unified-roles/roles/:id/health-check`    | `GET /admin/roles/:id/health-check`           | 角色健康檢查       |
| `GET /admin/unified-roles/roles/:id/usage-analysis`  | `GET /admin/roles/:id/usage-analysis`         | 角色使用分析       |
| `POST /admin/unified-roles/roles/batch-health-check` | `POST /admin/roles/batch-health-check`        | 批量角色健康檢查   |

### 身份驗證與權限變更

- 所有端點現在統一使用 `JwtAuthGuard` 和 `PoliciesGuard`
- `PoliciesGuard` 負責權限驗證，替代之前的 `RolesGuard`
- JWT 解析時會自動建立 CASL Ability 物件並掛載到 `req.ability`

### 前端整合說明

- 前端呼叫 API 時需修改端點路徑
- 使用 Cookie-based 認證 (通過 @horizai/auth 套件)
- /me 端點現在會包含完整的權限資訊
