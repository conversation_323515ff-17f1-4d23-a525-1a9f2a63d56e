import { Test, TestingModule } from '@nestjs/testing';
import { NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { RoleAssignmentService } from './role-assignment.service';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { UserType } from '../types/role.types';
import { RoleScope } from '@prisma/client';

describe('RoleAssignmentService', () => {
  let service: RoleAssignmentService;
  let prismaService: PrismaService;

  const mockPrismaService = {
    system_users: {
      findUnique: jest.fn(),
    },
    tenant_users: {
      findUnique: jest.fn(),
    },
    roles: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    system_user_roles: {
      findMany: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenant_user_roles: {
      findMany: jest.fn(),
      createMany: jest.fn(),
      deleteMany: jest.fn(),
    },
    tenants: {
      findUnique: jest.fn(),
    },
    $transaction: jest.fn(),
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        RoleAssignmentService,
        {
          provide: PrismaService,
          useValue: mockPrismaService,
        },
      ],
    }).compile();

    service = module.get<RoleAssignmentService>(RoleAssignmentService);
    prismaService = module.get<PrismaService>(PrismaService);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('assignRoles', () => {
    const mockSystemUser = {
      id: 'system-user-1',
      email: '<EMAIL>',
      name: 'System Admin',
      status: 'ACTIVE',
    };

    const mockSystemRole = {
      id: 'system-role-1',
      name: 'SYSTEM_ADMIN',
      displayName: '系統管理員',
      scope: RoleScope.SYSTEM,
      isSystem: true,
      tenantId: null,
    };

    it('should successfully assign roles to system user', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([mockSystemRole]);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            findMany: jest.fn().mockResolvedValue([]),
            createMany: jest.fn().mockResolvedValue({ count: 1 }),
          },
        });
      });

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['system-role-1'],
        reason: 'Initial setup',
        assigned_by: 'admin',
      };

      // Act
      const result = await service.assignRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.user_id).toBe('system-user-1');
      expect(result.user_type).toBe(UserType.SYSTEM);
      expect(result.assigned_roles).toEqual(['system-role-1']);
      expect(mockPrismaService.system_users.findUnique).toHaveBeenCalledWith({
        where: { id: 'system-user-1' },
      });
      expect(mockPrismaService.roles.findMany).toHaveBeenCalledWith({
        where: { id: { in: ['system-role-1'] } },
      });
    });

    it('should throw NotFoundException when user does not exist', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(null);

      const request = {
        user_id: 'non-existent-user',
        user_type: UserType.SYSTEM,
        role_ids: ['system-role-1'],
      };

      // Act & Assert
      await expect(service.assignRoles(request)).rejects.toThrow(NotFoundException);
    });

    it('should throw NotFoundException when role does not exist', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([]); // No roles found

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['non-existent-role'],
      };

      // Act & Assert
      await expect(service.assignRoles(request)).rejects.toThrow(NotFoundException);
    });

    it('should throw BadRequestException for scope mismatch', async () => {
      // Arrange
      const tenantRole = {
        ...mockSystemRole,
        id: 'tenant-role-1',
        scope: RoleScope.TENANT,
      };

      mockPrismaService.system_users.findUnique.mockResolvedValue(mockSystemUser);
      mockPrismaService.roles.findMany.mockResolvedValue([tenantRole]);

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['tenant-role-1'],
      };

      // Act & Assert
      await expect(service.assignRoles(request)).rejects.toThrow(BadRequestException);
    });
  });

  describe('removeRoles', () => {
    const mockUserRole = {
      id: 'user-role-1',
      system_user_id: 'system-user-1',
      role_id: 'system-role-1',
      role: {
        id: 'system-role-1',
        name: 'SYSTEM_ADMIN',
      },
    };

    it('should successfully remove roles from user', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue({
        id: 'system-user-1',
        tenantId: null,
      });
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([mockUserRole]);
      mockPrismaService.system_user_roles.deleteMany.mockResolvedValue({ count: 1 });

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['system-role-1'],
        reason: 'Role change',
        removed_by: 'admin',
      };

      // Act
      const result = await service.removeRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.removed_roles).toEqual(['system-role-1']);
      expect(mockPrismaService.system_user_roles.deleteMany).toHaveBeenCalledWith({
        where: {
          system_user_id: 'system-user-1',
          role_id: { in: ['system-role-1'] },
        },
      });
    });

    it('should throw BadRequestException when user does not have specified roles', async () => {
      // Arrange
      mockPrismaService.system_users.findUnique.mockResolvedValue({
        id: 'system-user-1',
        tenant_id: null,
      });
      mockPrismaService.system_user_roles.findMany.mockResolvedValue([]); // No roles

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['system-role-1'],
      };

      // Act & Assert
      await expect(service.removeRoles(request)).rejects.toThrow(BadRequestException);
    });
  });

  describe('replaceRoles', () => {
    it('should successfully replace user roles', async () => {
      // Arrange
      const currentRoles = [
        { id: 'old-role-1', name: 'OLD_ROLE' },
        { id: 'old-role-2', name: 'OLD_ROLE_2' },
      ];

      mockPrismaService.system_user_roles.findMany.mockResolvedValue(
        currentRoles.map((role) => ({ role })),
      );
      mockPrismaService.roles.findMany.mockResolvedValue([
        { id: 'new-role-1', scope: RoleScope.SYSTEM },
      ]);
      mockPrismaService.$transaction.mockImplementation(async (callback) => {
        return callback({
          system_user_roles: {
            deleteMany: jest.fn(),
            createMany: jest.fn(),
          },
        });
      });

      const request = {
        user_id: 'system-user-1',
        user_type: UserType.SYSTEM,
        role_ids: ['new-role-1'],
        reason: 'Role restructure',
        assigned_by: 'admin',
      };

      // Act
      const result = await service.replaceRoles(request);

      // Assert
      expect(result.success).toBe(true);
      expect(result.assigned_roles).toEqual(['new-role-1']);
      expect(result.removed_roles).toEqual(['old-role-1', 'old-role-2']);
    });
  });

  describe('getUserRoleInfo', () => {
    it('should return user role information', async () => {
      // Arrange
      const mockUser = {
        id: 'system-user-1',
        email: '<EMAIL>',
        name: 'System Admin',
        status: 'ACTIVE',
      };

      const mockUserRoles = [
        {
          role: {
            id: 'system-role-1',
            name: 'SYSTEM_ADMIN',
            displayName: '系統管理員',
            description: '系統管理員角色',
            scope: RoleScope.SYSTEM,
            isSystem: true,
          },
          created_at: new Date(),
        },
      ];

      mockPrismaService.system_users.findUnique.mockResolvedValue(mockUser);
      mockPrismaService.system_user_roles.findMany.mockResolvedValue(mockUserRoles);

      // Act
      const result = await service.getUserRoleInfo('system-user-1', UserType.SYSTEM);

      // Assert
      expect(result.user_id).toBe('system-user-1');
      expect(result.user_type).toBe(UserType.SYSTEM);
      expect(result.user).toEqual(mockUser);
      expect(result.roles).toHaveLength(1);
      expect(result.roles[0].id).toBe('system-role-1');
    });
  });
});
