import {
  Injectable,
  Logger,
  NotFoundException,
  BadRequestException,
  ConflictException,
} from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import {
  UserType,
  RoleAssignmentRequest,
  RoleRemovalRequest,
  BatchRoleOperationRequest,
  RoleAssignmentResult,
  BatchRoleOperationResult,
  UserRoleInfo,
  RoleConflictCheck,
} from '../types/role.types';
import { RoleScope } from '@prisma/client';
import { Prisma } from '@prisma/client';

/**
 * 角色指派服務
 * 負責管理用戶角色的指派、移除和驗證
 */
@Injectable()
export class RoleAssignmentService {
  private readonly logger = new Logger(RoleAssignmentService.name);

  constructor(private readonly prisma: PrismaService) {}

  /**
   * 指派角色給用戶
   */
  async assignRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult> {
    const { user_id, user_type, role_ids, tenant_id, reason, assigned_by } = request;

    this.logger.debug(`Assigning roles to user ${user_id} (${user_type}): ${role_ids.join(', ')}`);

    // 1. 驗證用戶存在
    await this.validateUser(user_id, user_type, tenant_id);

    // 2. 獲取要指派的角色和用戶現有角色
    const [rolesToAssign, userCurrentRoles] = await Promise.all([
      this.getValidRoles(role_ids, user_type, tenant_id),
      this.getUserRoles(user_id, user_type),
    ]);
    const currentRoleIds = new Set(userCurrentRoles.map((r) => r.id));

    // 3. 檢查角色衝突
    const allRoles = [...userCurrentRoles, ...rolesToAssign];
    const conflictCheck = await this.checkRoleConflicts(
      user_id,
      user_type,
      allRoles.map((r) => r.id),
      tenant_id,
    );
    if (conflictCheck.has_conflict) {
      throw new ConflictException(
        `角色衝突: ${conflictCheck.conflicts.map((c) => c.message).join(', ')}`,
      );
    }

    // 4. 過濾掉已存在的角色
    const newRoleIds = rolesToAssign
      .map((r) => r.id)
      .filter((roleId) => !currentRoleIds.has(roleId));
    if (newRoleIds.length === 0) {
      this.logger.log(`User ${user_id} already has all requested roles.`);
      return {
        success: true,
        user_id,
        user_type,
        assigned_roles: [],
        removed_roles: [],
        message: '用戶已擁有所有請求的角色',
        timestamp: new Date(),
      };
    }

    // 5. 執行資料庫操作
    await this.executeRoleAssignmentInTransaction(user_id, user_type, newRoleIds, tenant_id);

    this.logger.log(
      `Successfully assigned ${newRoleIds.length} roles to user ${user_id} (${user_type})`,
    );

    return {
      success: true,
      user_id,
      user_type,
      assigned_roles: newRoleIds,
      removed_roles: [],
      message: '角色指派成功',
      timestamp: new Date(),
    };
  }

  /**
   * 移除用戶角色
   */
  async removeRoles(request: RoleRemovalRequest): Promise<RoleAssignmentResult> {
    const { user_id, user_type, role_ids, reason, removed_by } = request;

    this.logger.debug(`Removing roles from user ${user_id} (${user_type}): ${role_ids.join(', ')}`);

    // 1. 驗證用戶存在
    await this.validateUser(user_id, user_type);

    // 2. 獲取用戶目前的角色
    const currentRoles = await this.getUserRoles(user_id, user_type);
    const currentRoleIds = new Set(currentRoles.map((r) => r.id));

    // 3. 過濾出實際要移除的角色
    const rolesToRemove = role_ids.filter((roleId) => currentRoleIds.has(roleId));

    if (rolesToRemove.length === 0) {
      throw new BadRequestException('用戶沒有指定的角色');
    }

    // 4. 執行資料庫操作
    if (user_type === UserType.SYSTEM) {
      await this.prisma.system_user_roles.deleteMany({
        where: { system_user_id: user_id, role_id: { in: rolesToRemove } },
      });
    } else {
      await this.prisma.tenant_user_roles.deleteMany({
        where: { tenant_user_id: user_id, role_id: { in: rolesToRemove } },
      });
    }

    this.logger.log(
      `Successfully removed ${rolesToRemove.length} roles from user ${user_id} (${user_type})`,
    );

    return {
      success: true,
      user_id,
      user_type,
      assigned_roles: [],
      removed_roles: rolesToRemove,
      message: '角色移除成功',
      timestamp: new Date(),
    };
  }

  /**
   * 替換用戶角色
   */
  async replaceRoles(request: RoleAssignmentRequest): Promise<RoleAssignmentResult> {
    const { user_id, user_type, role_ids, tenant_id, reason, assigned_by } = request;

    this.logger.debug(
      `Replacing roles for user ${user_id} (${user_type}) with: ${role_ids.join(', ')}`,
    );

    try {
      await this.validateUser(user_id, user_type, tenant_id);

      const rolesToAssign = await this.getValidRoles(role_ids, user_type, tenant_id);
      const currentRoles = await this.getUserRoles(user_id, user_type);
      const currentRoleIds = currentRoles.map((r) => r.id);

      await this.prisma.$transaction(async (tx) => {
        if (user_type === UserType.SYSTEM) {
          await tx.system_user_roles.deleteMany({ where: { system_user_id: user_id } });
        } else {
          await tx.tenant_user_roles.deleteMany({ where: { tenant_user_id: user_id } });
        }
        await this.executeRoleAssignmentInTransaction(user_id, user_type, role_ids, tenant_id, tx);
      });

      this.logger.log(`Successfully replaced roles for user ${user_id} (${user_type})`);

      return {
        success: true,
        user_id,
        user_type,
        assigned_roles: role_ids,
        removed_roles: currentRoleIds,
        message: '角色替換成功',
        timestamp: new Date(),
      };
    } catch (error) {
      this.logger.error(
        `Failed to replace roles for user ${user_id} (${user_type}):`,
        error.message,
      );

      return {
        success: false,
        user_id,
        user_type,
        assigned_roles: [],
        removed_roles: [],
        message: `角色替換失敗: ${error.message}`,
        timestamp: new Date(),
      };
    }
  }

  /**
   * 批量角色操作
   */
  async batchRoleOperations(request: BatchRoleOperationRequest): Promise<BatchRoleOperationResult> {
    const { operations, reason, operated_by } = request;

    this.logger.debug(`Executing batch role operations: ${operations.length} operations`);
    const results: RoleAssignmentResult[] = [];
    let successCount = 0;

    for (const operation of operations) {
      let result: RoleAssignmentResult;
      try {
        switch (operation.action) {
          case 'assign':
            result = await this.assignRoles({
              user_id: operation.user_id,
              user_type: operation.user_type,
              role_ids: operation.role_ids,
              tenant_id: operation.tenant_id,
              reason,
              assigned_by: operated_by,
            });
            break;
          case 'remove':
            result = await this.removeRoles({
              user_id: operation.user_id,
              user_type: operation.user_type,
              role_ids: operation.role_ids,
              reason,
              removed_by: operated_by,
            });
            break;
          case 'replace':
            result = await this.replaceRoles({
              user_id: operation.user_id,
              user_type: operation.user_type,
              role_ids: operation.role_ids,
              tenant_id: operation.tenant_id,
              reason,
              assigned_by: operated_by,
            });
            break;
          default:
            throw new Error(`Unsupported batch action: ${operation.action}`);
        }
        if (result.success) successCount++;
        results.push(result);
      } catch (error) {
        results.push({
          success: false,
          user_id: operation.user_id,
          user_type: operation.user_type,
          assigned_roles: [],
          removed_roles: [],
          message: error.message,
          timestamp: new Date(),
        });
      }
    }

    return {
      total_operations: operations.length,
      success_count: successCount,
      failure_count: operations.length - successCount,
      results,
      timestamp: new Date(),
    };
  }

  /**
   * 獲取用戶角色資訊
   */
  async getUserRoleInfo(userId: string, userType: UserType): Promise<UserRoleInfo> {
    const tenantId =
      userType === UserType.TENANT ? await this.getUserTenantId(userId, userType) : undefined;

    // 驗證用戶存在
    await this.validateUserExists(userId, userType, tenantId);

    // 獲取用戶基本資訊
    const user = await this.getUserBasicInfo(userId, userType);

    // 獲取用戶角色
    const roles = await this.getUserRolesWithDetails(userId, userType);

    // 獲取租戶資訊（如果是租戶用戶）
    let tenant;
    if (userType === UserType.TENANT && tenantId) {
      tenant = await this.prisma.tenants.findUnique({
        where: { id: tenantId },
        select: { id: true, name: true },
      });
    }

    return {
      user_id: userId,
      user_type: userType,
      user,
      roles,
      tenant,
    };
  }

  // ==================== 私有方法 ====================

  /**
   * 驗證用戶是否存在
   */
  private async validateUserExists(
    userId: string,
    userType: UserType,
    tenant_id?: string,
  ): Promise<void> {
    if (userType === UserType.SYSTEM) {
      const user = await this.prisma.system_users.findUnique({
        where: { id: userId },
      });
      if (!user) {
        throw new NotFoundException(`系統用戶 ${userId} 不存在`);
      }
    } else {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
      });
      if (!user) {
        throw new NotFoundException(`租戶用戶 ${userId} 不存在`);
      }
      if (tenant_id && user.tenant_id !== tenant_id) {
        throw new BadRequestException(`用戶 ${userId} 不屬於租戶 ${tenant_id}`);
      }
    }
  }

  /**
   * 驗證角色是否存在且適用
   */
  private async validateRoles(
    roleIds: string[],
    userType: UserType,
    tenant_id?: string,
  ): Promise<void> {
    const roles = await this.prisma.roles.findMany({
      where: { id: { in: roleIds } },
    });

    if (roles.length !== roleIds.length) {
      const foundRoleIds = roles.map((r) => r.id);
      const missingRoleIds = roleIds.filter((id) => !foundRoleIds.includes(id));
      throw new NotFoundException(`角色不存在: ${missingRoleIds.join(', ')}`);
    }

    // 檢查角色範圍是否適用
    for (const role of roles) {
      if (userType === UserType.SYSTEM) {
        if (role.scope !== RoleScope.SYSTEM) {
          throw new BadRequestException(`角色 ${role.name} (${role.scope}) 不適用於系統用戶`);
        }
      } else {
        if (role.scope === RoleScope.SYSTEM) {
          throw new BadRequestException(`角色 ${role.name} (${role.scope}) 不適用於租戶用戶`);
        }
        if (
          (role.scope === RoleScope.TENANT || role.scope === RoleScope.WORKSPACE) &&
          role.tenant_id &&
          role.tenant_id !== tenant_id
        ) {
          throw new BadRequestException(`角色 ${role.name} 不屬於租戶 ${tenant_id}`);
        }
      }
    }
  }

  /**
   * 檢查角色衝突
   */
  private async checkRoleConflicts(
    userId: string,
    userType: UserType,
    roleIds: string[],
    tenant_id?: string,
  ): Promise<RoleConflictCheck> {
    const allRoles = await this.prisma.roles.findMany({ where: { id: { in: roleIds } } });
    const conflicts: Array<{
      type: 'scope_mismatch' | 'hierarchy_violation' | 'permission_conflict' | 'tenant_mismatch';
      message: string;
      conflicting_roles: string[];
    }> = [];
    const suggestions: string[] = [];

    const hasSystemRole = allRoles.some((r) => r.scope === RoleScope.SYSTEM);
    const hasNonSystemRole = allRoles.some((r) => r.scope !== RoleScope.SYSTEM);
    if (hasSystemRole && hasNonSystemRole) {
      conflicts.push({
        type: 'scope_mismatch',
        message: '不能同時擁有系統角色和非系統角色',
        conflicting_roles: allRoles.map((r) => r.id),
      });
    }

    if (userType === UserType.TENANT && tenant_id) {
      const tenantMismatchRoles = allRoles.filter(
        (r) => r.scope !== RoleScope.SYSTEM && r.tenant_id && r.tenant_id !== tenant_id,
      );
      if (tenantMismatchRoles.length > 0) {
        conflicts.push({
          type: 'tenant_mismatch',
          message: '角色不屬於用戶的租戶',
          conflicting_roles: tenantMismatchRoles.map((r) => r.id),
        });
      }
    }

    return {
      has_conflict: conflicts.length > 0,
      conflicts,
      suggestions,
    };
  }

  /**
   * 執行角色指派
   */
  private async executeRoleAssignment(
    userId: string,
    userType: UserType,
    roleIds: string[],
    tenant_id?: string,
  ): Promise<void> {
    await this.prisma.$transaction(async (tx) => {
      await this.executeRoleAssignmentInTransaction(userId, userType, roleIds, tenant_id, tx);
    });
  }

  /**
   * 在事務中執行角色指派
   */
  private async executeRoleAssignmentInTransaction(
    userId: string,
    userType: UserType,
    newRoleIds: string[],
    tenantId?: string,
    tx?: Prisma.TransactionClient,
  ): Promise<void> {
    const prisma = tx || this.prisma;
    if (newRoleIds.length === 0) return;

    if (userType === UserType.SYSTEM) {
      await prisma.system_user_roles.createMany({
        data: newRoleIds.map((roleId) => ({
          id: `${userId}-${roleId}-${Date.now()}`,
          system_user_id: userId,
          role_id: roleId,
        })),
      });
    } else {
      await prisma.tenant_user_roles.createMany({
        data: newRoleIds.map((roleId) => ({
          id: `${userId}-${roleId}-${Date.now()}`,
          tenant_user_id: userId,
          role_id: roleId,
        })),
      });
    }
  }

  /**
   * 獲取用戶角色
   */
  private async getUserRoles(userId: string, userType: UserType): Promise<any[]> {
    if (userType === UserType.SYSTEM) {
      const userRoles = await this.prisma.system_user_roles.findMany({
        where: { system_user_id: userId },
        include: { role: true },
      });
      return userRoles.map((ur) => ur.role);
    } else {
      const userRoles = await this.prisma.tenant_user_roles.findMany({
        where: { tenant_user_id: userId },
        include: { role: true },
      });
      return userRoles.map((ur) => ur.role);
    }
  }

  /**
   * 獲取用戶角色詳細資訊
   */
  private async getUserRolesWithDetails(userId: string, userType: UserType): Promise<any[]> {
    if (userType === UserType.SYSTEM) {
      const userRoles = await this.prisma.system_user_roles.findMany({
        where: { system_user_id: userId },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              display_name: true,
              description: true,
              scope: true,
              is_system: true,
            },
          },
        },
        orderBy: { created_at: 'asc' },
      });
      return userRoles.map((ur) => ({
        ...ur.role,
        assignedAt: ur.created_at,
      }));
    } else {
      const userRoles = await this.prisma.tenant_user_roles.findMany({
        where: { tenant_user_id: userId },
        include: {
          role: {
            select: {
              id: true,
              name: true,
              display_name: true,
              description: true,
              scope: true,
              is_system: true,
            },
          },
        },
        orderBy: { created_at: 'asc' },
      });
      return userRoles.map((ur) => ({
        ...ur.role,
        assignedAt: ur.created_at,
      }));
    }
  }

  /**
   * 獲取用戶基本資訊
   */
  private async getUserBasicInfo(userId: string, userType: UserType): Promise<any> {
    if (userType === UserType.SYSTEM) {
      const user = await this.prisma.system_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          status: true,
        },
      });
      return user;
    } else {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          name: true,
          status: true,
        },
      });
      return user;
    }
  }

  /**
   * 獲取用戶的租戶 ID
   */
  private async getUserTenantId(userId: string, userType: UserType): Promise<string | undefined> {
    if (userType === UserType.TENANT) {
      const user = await this.prisma.tenant_users.findUnique({
        where: { id: userId },
        select: { tenant_id: true },
      });
      return user?.tenant_id;
    }
    return undefined;
  }

  /**
   * 記錄角色變更歷史
   */
  private async recordRoleChange(
    userId: string,
    userType: UserType,
    changeType: 'assign' | 'remove' | 'replace',
    previousRoles: string[],
    newRoles: string[],
    reason?: string,
    changedBy?: string,
    tenant_id?: string,
  ): Promise<void> {
    // 這裡可以實作角色變更歷史記錄
    // 由於沒有專門的歷史表，我們可以使用日誌記錄
    this.logger.log(
      `Role change recorded: User ${userId} (${userType}) - ${changeType} - ` +
        `Previous: [${previousRoles.join(', ')}] -> New: [${newRoles.join(', ')}] - ` +
        `Reason: ${reason || 'N/A'} - Changed by: ${changedBy || 'System'} - ` +
        `Tenant: ${tenant_id || 'N/A'}`,
    );
  }

  private async getValidRoles(roleIds: string[], userType: UserType, tenantId?: string) {
    const roles = await this.prisma.roles.findMany({
      where: { id: { in: roleIds } },
    });

    // 檢查角色是否存在
    if (roles.length !== roleIds.length) {
      const foundRoleIds = roles.map((r) => r.id);
      const missingRoleIds = roleIds.filter((id) => !foundRoleIds.includes(id));
      throw new NotFoundException(`角色不存在: ${missingRoleIds.join(', ')}`);
    }

    // 檢查角色範圍是否適用
    for (const role of roles) {
      if (userType === UserType.SYSTEM) {
        if (role.scope !== RoleScope.SYSTEM) {
          throw new BadRequestException(`角色 ${role.name} (${role.scope}) 不適用於系統用戶`);
        }
      } else {
        if (role.scope === RoleScope.SYSTEM) {
          throw new BadRequestException(`角色 ${role.name} (${role.scope}) 不適用於租戶用戶`);
        }
        if (
          (role.scope === RoleScope.TENANT || role.scope === RoleScope.WORKSPACE) &&
          role.tenant_id &&
          role.tenant_id !== tenantId
        ) {
          throw new BadRequestException(`角色 ${role.name} 不屬於租戶 ${tenantId}`);
        }
      }
    }

    return roles;
  }

  private async validateUser(userId: string, userType: UserType, tenantId?: string): Promise<void> {
    if (userType === UserType.SYSTEM) {
      const user = await this.prisma.system_users.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`系統用戶 ${userId} 不存在`);
      }
    } else {
      const user = await this.prisma.tenant_users.findUnique({ where: { id: userId } });
      if (!user) {
        throw new NotFoundException(`租戶用戶 ${userId} 不存在`);
      }
      if (tenantId && user.tenant_id !== tenantId) {
        throw new BadRequestException(`用戶 ${userId} 不屬於租戶 ${tenantId}`);
      }
    }
  }
}
