import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  Query,
  Req,
  ForbiddenException,
  Logger,
} from '@nestjs/common';
import { RolesService } from '../services/roles.service';
import {
  CreateRoleDto,
  UpdateRoleDto,
  UpdateRolePermissionsDto,
  RoleResponseDto,
  RoleWithPermissionsResponseDto,
  RoleUserCountResponseDto,
  RoleScope,
  PermissionDto,
  PermissionCategoryDto,
} from '../dto/role.dto';
import { JwtAuthGuard } from '../../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../../types/models/casl.model';
import { Actions, Subjects } from '@horizai/permissions';
import {
  ApiTags,
  ApiBearerAuth,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';
import { Request } from 'express';

@ApiTags('admin/roles')
@ApiBearerAuth()
@Controller('admin/roles')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class RolesController {
  private readonly logger = new Logger(RolesController.name);

  constructor(private readonly rolesService: RolesService) {}

  @Get('permissions')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({
    summary: '讀取所有權限',
    description: '取得系統中所有權限的列表，用於權限矩陣顯示',
  })
  @ApiResponse({
    status: 200,
    description: '成功讀取權限列表',
    type: [PermissionDto],
  })
  async getAllPermissions(): Promise<PermissionDto[]> {
    return this.rolesService.getAllPermissions();
  }

  @Get('permissions/categories')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({
    summary: '讀取所有權限分類',
    description: '取得系統中所有權限分類的列表，用於權限矩陣分組顯示',
  })
  @ApiResponse({
    status: 200,
    description: '成功讀取權限分類列表',
    type: [PermissionCategoryDto],
  })
  async getAllPermissionCategories(): Promise<PermissionCategoryDto[]> {
    return this.rolesService.getAllPermissionCategories();
  }

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '讀取所有角色',
    description: '取得系統中所有角色的列表，可依 scope 篩選',
  })
  @ApiQuery({
    name: 'scope',
    required: false,
    enum: RoleScope,
    description: '角色區域過濾 (SYSTEM, TENANT, WORKSPACE)',
  })
  @ApiResponse({
    status: 200,
    description: '成功讀取角色列表',
    type: [RoleResponseDto],
  })
  async findAll(@Query('scope') scope?: RoleScope): Promise<RoleResponseDto[]> {
    const roles = await this.rolesService.findAll(scope);
    return roles.map((role) => ({
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description ?? undefined,
      is_system: role.is_system,
      scope: role.scope as RoleScope,
      tenant_id: role.tenant_id,
      created_at: role.created_at,
      updated_at: role.updated_at,
    }));
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '讀取單個角色詳情',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '成功讀取角色詳情',
    type: RoleWithPermissionsResponseDto,
  })
  async findOne(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<RoleWithPermissionsResponseDto> {
    const roleDetail = await this.rolesService.findOne(id);

    const ability = req.ability;
    if (!ability || !ability.can(Actions.READ, roleDetail as any)) {
      throw new ForbiddenException('您沒有權限讀取此角色的詳細資訊。');
    }

    // 獲取角色的權限
    const completeInfo = await this.rolesService.getCompleteRoleInfo(id);
    const permissions = completeInfo.permissions.effective;

    return {
      id: roleDetail.id,
      name: roleDetail.name,
      display_name: roleDetail.display_name,
      description: roleDetail.description ?? undefined,
      is_system: roleDetail.is_system,
      scope: roleDetail.scope as RoleScope,
      tenant_id: roleDetail.tenant_id,
      created_at: roleDetail.created_at,
      updated_at: roleDetail.updated_at,
      permissions: permissions || [],
    };
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.ROLE))
  @ApiOperation({
    summary: '建立新角色',
  })
  @ApiResponse({
    status: 201,
    description: '角色建立成功',
    type: RoleResponseDto,
  })
  async create(@Body() createRoleDto: CreateRoleDto): Promise<RoleResponseDto> {
    const role = await this.rolesService.create(createRoleDto);
    return {
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description ?? undefined,
      is_system: role.is_system,
      scope: role.scope as RoleScope,
      tenant_id: role.tenant_id,
      created_at: role.created_at,
      updated_at: role.updated_at,
    };
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.ROLE))
  @ApiOperation({
    summary: '更新角色',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色更新成功',
    type: RoleResponseDto,
  })
  async update(
    @Param('id') id: string,
    @Body() updateRoleDto: UpdateRoleDto,
  ): Promise<RoleResponseDto> {
    const role = await this.rolesService.update(id, updateRoleDto);
    return {
      id: role.id,
      name: role.name,
      display_name: role.display_name,
      description: role.description ?? undefined,
      is_system: role.is_system,
      scope: role.scope as RoleScope,
      tenant_id: role.tenant_id,
      created_at: role.created_at,
      updated_at: role.updated_at,
    };
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.ROLE))
  @ApiOperation({
    summary: '刪除角色',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色刪除成功',
  })
  async remove(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<{ success: boolean; message: string }> {
    const role = await this.rolesService.findOne(id);

    const ability = req.ability;
    if (!ability || !ability.can(Actions.DELETE, role as any)) {
      throw new ForbiddenException('您沒有權限刪除此角色。');
    }

    return this.rolesService.remove(id);
  }

  @Put(':id/permissions')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.ROLE))
  @ApiOperation({
    summary: '更新角色權限',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色權限更新成功',
  })
  async updateRolePermissions(
    @Param('id') id: string,
    @Body() updatePermissionsDto: UpdateRolePermissionsDto,
    @Req() req: Request,
  ): Promise<{ success: boolean; message: string }> {
    const role = await this.rolesService.findOne(id);

    const ability = req.ability;
    if (!ability || !ability.can(Actions.UPDATE, role as any)) {
      throw new ForbiddenException('您沒有權限更新此角色的權限。');
    }

    return this.rolesService.updateRolePermissions(id, updatePermissionsDto.permissions);
  }

  @Get(':id/users/count')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '獲取角色使用者數量',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '成功獲取角色使用者數量',
    type: RoleUserCountResponseDto,
  })
  async getUserCount(
    @Param('id') id: string,
    @Req() req: Request,
  ): Promise<RoleUserCountResponseDto> {
    const role = await this.rolesService.findOne(id);

    const ability = req.ability;
    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException('您沒有權限讀取此角色的使用者數量。');
    }

    const count = await this.rolesService.getUserCount(id);
    return { count };
  }

  // ==================== 統一角色管理高級功能 ====================

  /**
   * 獲取角色完整資訊
   */
  @Get(':id/complete')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '獲取角色完整資訊',
    description: '獲取角色的完整資訊，包含定義、層級、用戶、權限和統計資料',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色完整資訊',
    type: Object,
  })
  async getCompleteRoleInfo(@Param('id') id: string, @Req() req: Request) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException('您沒有權限讀取此角色的完整資訊。');
    }

    return this.rolesService.getCompleteRoleInfo(id);
  }

  /**
   * 獲取角色摘要資訊
   */
  @Get(':id/summary')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '獲取角色摘要資訊',
    description: '獲取角色的摘要資訊，適用於列表顯示',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色摘要資訊',
    type: Object,
  })
  async getRoleSummary(@Param('id') id: string, @Req() req: Request) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException('您沒有權限讀取此角色的摘要資訊。');
    }

    return this.rolesService.getRoleSummary(id);
  }

  /**
   * 批量獲取角色摘要
   */
  @Post('batch-summaries')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '批量獲取角色摘要',
    description: '批量獲取多個角色的摘要資訊',
  })
  @ApiResponse({
    status: 200,
    description: '批量角色摘要結果',
    type: Object,
  })
  async getBatchRoleSummaries(@Query('roleIds') roleIds: string, @Req() req: Request) {
    const ability = req.ability;

    if (!ability || !ability.can(Actions.READ, Subjects.ROLE)) {
      throw new ForbiddenException('您沒有權限讀取角色摘要資訊。');
    }

    const roleIdArray = roleIds.split(',').map((id) => id.trim());
    return this.rolesService.getBatchRoleSummaries(roleIdArray);
  }

  /**
   * 角色健康檢查
   */
  @Get(':id/health-check')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '角色健康檢查',
    description: '檢查角色的完整性和一致性',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色健康檢查結果',
    type: Object,
  })
  async performRoleHealthCheck(@Param('id') id: string, @Req() req: Request) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException('您沒有權限執行此角色的健康檢查。');
    }

    return this.rolesService.performRoleHealthCheck(id);
  }

  /**
   * 批量健康檢查
   */
  @Post('batch-health-check')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '批量角色健康檢查',
    description: '對多個角色進行健康檢查',
  })
  @ApiResponse({
    status: 200,
    description: '批量健康檢查結果',
    type: Object,
  })
  async batchHealthCheck(@Query('roleIds') roleIds: string, @Req() req: Request) {
    const ability = req.ability;

    if (!ability || !ability.can(Actions.READ, Subjects.ROLE)) {
      throw new ForbiddenException('您沒有權限執行批量健康檢查。');
    }

    const roleIdArray = roleIds.split(',').map((id) => id.trim());

    const results = await Promise.all(
      roleIdArray.map((roleId) => this.rolesService.performRoleHealthCheck(roleId)),
    );

    // 統計結果
    const healthy = results.filter((r) => r.status === 'excellent' || r.status === 'good').length;
    const unhealthy = results.length - healthy;

    // 收集常見問題
    const allIssues = results.flatMap((r) => r.issues);
    const allWarnings = results.flatMap((r) => r.warnings);

    const issueCount = allIssues.reduce(
      (acc, issue) => {
        acc[issue] = (acc[issue] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const warningCount = allWarnings.reduce(
      (acc, warning) => {
        acc[warning] = (acc[warning] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>,
    );

    const commonIssues = Object.entries(issueCount)
      .filter(([, count]) => (count as number) > 1)
      .map(([issue]) => issue);

    const commonWarnings = Object.entries(warningCount)
      .filter(([, count]) => (count as number) > 1)
      .map(([warning]) => warning);

    return {
      results,
      summary: {
        total: results.length,
        healthy,
        unhealthy,
        commonIssues,
        commonWarnings,
      },
    };
  }

  /**
   * 角色系統概覽
   */
  @Get('system-overview')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '角色系統概覽',
    description: '獲取整個角色系統的概覽資訊',
  })
  @ApiResponse({
    status: 200,
    description: '角色系統概覽',
    type: Object,
  })
  async getSystemOverview(@Req() req: Request) {
    const ability = req.ability;

    if (!ability || !ability.can(Actions.READ, Subjects.ROLE)) {
      throw new ForbiddenException('您沒有權限讀取系統概覽。');
    }

    return this.rolesService.getSystemOverview();
  }

  /**
   * 角色建議
   */
  @Get('suggestions')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '獲取角色最佳化建議',
    description: '分析角色系統並提供最佳化建議',
  })
  @ApiQuery({ name: 'includeAutoApplicable', required: false, type: Boolean })
  @ApiQuery({ name: 'priority', required: false, enum: ['low', 'medium', 'high'] })
  @ApiResponse({
    status: 200,
    description: '角色最佳化建議',
    type: Object,
  })
  async getRoleSuggestions(
    @Query('includeAutoApplicable') includeAutoApplicable?: boolean,
    @Query('priority') priority?: 'low' | 'medium' | 'high',
    @Req() req?: Request,
  ) {
    const ability = req?.ability;

    if (!ability || !ability.can(Actions.READ, Subjects.ROLE)) {
      throw new ForbiddenException('您沒有權限讀取角色建議。');
    }

    return this.rolesService.getRoleOptimizationSuggestions({
      includeAutoApplicable,
      priority,
    });
  }

  /**
   * 角色比較
   */
  @Get('compare/:id1/:id2')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '比較兩個角色',
    description: '比較兩個角色的權限、用戶和層級差異',
  })
  @ApiParam({ name: 'id1', description: '第一個角色ID' })
  @ApiParam({ name: 'id2', description: '第二個角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色比較結果',
    type: Object,
  })
  async compareRoles(@Param('id1') id1: string, @Param('id2') id2: string, @Req() req: Request) {
    const ability = req.ability;

    if (!ability || !ability.can(Actions.READ, Subjects.ROLE)) {
      throw new ForbiddenException('您沒有權限比較角色。');
    }

    return this.rolesService.compareRoles(id1, id2);
  }

  /**
   * 角色使用分析
   */
  @Get(':id/usage-analysis')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.ROLE))
  @ApiOperation({
    summary: '角色使用分析',
    description: '分析角色的使用模式、影響範圍和風險評估',
  })
  @ApiParam({ name: 'id', description: '角色ID' })
  @ApiResponse({
    status: 200,
    description: '角色使用分析結果',
    type: Object,
  })
  async analyzeRoleUsage(@Param('id') id: string, @Req() req: Request) {
    const ability = req.ability;
    const role = await this.rolesService.findOne(id);

    if (!ability || !ability.can(Actions.READ, role as any)) {
      throw new ForbiddenException('您沒有權限分析此角色的使用情況。');
    }

    return this.rolesService.analyzeRoleUsage(id);
  }
}
