import { RoleScope } from '../dto/role.dto';

/**
 * 用戶類型枚舉
 */
export enum UserType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 角色指派請求介面
 */
export interface RoleAssignmentRequest {
  /**
   * 用戶 ID
   */
  user_id: string;

  /**
   * 用戶類型
   */
  user_type: UserType;

  /**
   * 角色 ID 陣列
   */
  role_ids: string[];

  /**
   * 租戶 ID（租戶用戶必填）
   */
  tenant_id?: string;

  /**
   * 指派原因
   */
  reason?: string;

  /**
   * 指派者 ID
   */
  assigned_by?: string;
}

/**
 * 角色移除請求介面
 */
export interface RoleRemovalRequest {
  /**
   * 用戶 ID
   */
  user_id: string;

  /**
   * 用戶類型
   */
  user_type: UserType;

  /**
   * 要移除的角色 ID 陣列
   */
  role_ids: string[];

  /**
   * 移除原因
   */
  reason?: string;

  /**
   * 移除者 ID
   */
  removed_by?: string;
}

/**
 * 批量角色操作請求介面
 */
export interface BatchRoleOperationRequest {
  /**
   * 用戶操作陣列
   */
  operations: Array<{
    user_id: string;
    user_type: UserType;
    action: 'assign' | 'remove' | 'replace';
    role_ids: string[];
    tenant_id?: string;
  }>;

  /**
   * 操作原因
   */
  reason?: string;

  /**
   * 操作者 ID
   */
  operated_by?: string;
}

/**
 * 角色指派結果介面
 */
export interface RoleAssignmentResult {
  /**
   * 是否成功
   */
  success: boolean;

  /**
   * 用戶 ID
   */
  user_id: string;

  /**
   * 用戶類型
   */
  user_type: UserType;

  /**
   * 指派的角色 ID 陣列
   */
  assigned_roles: string[];

  /**
   * 移除的角色 ID 陣列
   */
  removed_roles: string[];

  /**
   * 成功或錯誤訊息
   */
  message?: string;

  /**
   * 錯誤訊息（如果有）
   */
  error?: string;

  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 批量角色操作結果介面
 */
export interface BatchRoleOperationResult {
  /**
   * 總操作數
   */
  total_operations: number;

  /**
   * 成功操作數
   */
  success_count: number;

  /**
   * 失敗操作數
   */
  failure_count: number;

  /**
   * 個別操作結果
   */
  results: RoleAssignmentResult[];

  /**
   * 整體錯誤訊息（如果有）
   */
  error?: string;

  /**
   * 操作時間戳
   */
  timestamp: Date;
}

/**
 * 用戶角色資訊介面
 */
export interface UserRoleInfo {
  /**
   * 用戶 ID
   */
  user_id: string;

  /**
   * 用戶類型
   */
  user_type: UserType;

  /**
   * 用戶基本資訊
   */
  user: {
    id: string;
    email: string;
    name?: string;
    status: string;
  };

  /**
   * 角色陣列
   */
  roles: Array<{
    id: string;
    name: string;
    display_name: string;
    description?: string;
    scope: RoleScope;
    is_system: boolean;
    assignedAt: Date;
  }>;

  /**
   * 租戶資訊（租戶用戶）
   */
  tenant?: {
    id: string;
    name: string;
  };
}

/**
 * 角色衝突檢查結果介面
 */
export interface RoleConflictCheck {
  /**
   * 是否有衝突
   */
  has_conflict: boolean;

  /**
   * 衝突詳情
   */
  conflicts: Array<{
    type: 'scope_mismatch' | 'hierarchy_violation' | 'permission_conflict' | 'tenant_mismatch';
    message: string;
    conflicting_roles: string[];
  }>;

  /**
   * 建議
   */
  suggestions: string[];
}

/**
 * 角色層級資訊介面
 */
export interface RoleHierarchyInfo {
  /**
   * 角色 ID
   */
  role_id: string;

  /**
   * 角色名稱
   */
  role_name: string;

  /**
   * 父角色 ID
   */
  parent_role_id: string | null;

  /**
   * 子角色 ID 陣列
   */
  child_role_ids: string[];

  /**
   * 層級深度
   */
  level: number;

  /**
   * 繼承的權限
   */
  inherited_permissions: string[];

  /**
   * 直接權限
   */
  direct_permissions: string[];
}

/**
 * 角色變更歷史介面
 */
export interface RoleChangeHistory {
  /**
   * 變更 ID
   */
  id: string;

  /**
   * 用戶 ID
   */
  user_id: string;

  /**
   * 用戶類型
   */
  user_type: UserType;

  /**
   * 變更類型
   */
  change_type: 'assign' | 'remove' | 'replace';

  /**
   * 變更前的角色
   */
  previous_roles: string[];

  /**
   * 變更後的角色
   */
  new_roles: string[];

  /**
   * 變更原因
   */
  reason?: string;

  /**
   * 變更者 ID
   */
  changed_by: string;

  /**
   * 變更時間
   */
  changed_at: Date;

  /**
   * 租戶 ID（如果適用）
   */
  tenant_id?: string;
}

/**
 * 增強的角色資訊介面
 */
export interface EnhancedRoleInfo {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  scope: RoleScope;
  is_system: boolean;
  tenant_id?: string;
  parent_role_id?: string;
  created_at: Date;
  updated_at: Date;
  permissions: string[];
  hierarchy?: RoleHierarchyInfo;
  effective_permissions?: string[];
}

/**
 * 角色摘要資訊介面
 */
export interface RoleSummary {
  id: string;
  name: string;
  display_name: string;
  description?: string;
  scope: RoleScope;
  is_system: boolean;
  user_count: number;
  permission_count: number;
  hierarchy_level: number;
  has_children: boolean;
  has_parent: boolean;
  created_at: Date;
  updated_at: Date;
}

/**
 * 角色用戶統計詳情介面
 */
export interface RoleUserStatistics {
  /**
   * 總用戶數
   */
  total_users: number;

  /**
   * 系統用戶數
   */
  system_users: number;

  /**
   * 租戶用戶數
   */
  tenant_users: number;

  /**
   * 按租戶分類的用戶數量
   */
  users_by_tenant?: Record<string, number>;
}

/**
 * 角色統計資訊介面
 */
export interface RoleStatistics {
  /**
   * 總用戶數
   */
  user_count: number;

  /**
   * 系統用戶數
   */
  system_users: number;

  /**
   * 租戶用戶數
   */
  tenant_users: number;

  /**
   * 直接權限數量
   */
  direct_permissions: number;

  /**
   * 繼承權限數量
   */
  inherited_permissions: number;

  /**
   * 有效權限數量
   */
  effective_permissions: number;

  /**
   * 層級深度
   */
  hierarchy_level: number;

  /**
   * 子角色數量
   */
  child_roles_count: number;

  /**
   * 是否有父角色
   */
  has_parent_role: boolean;
}

/**
 * 完整角色資訊介面
 */
export interface CompleteRoleInfo {
  role: EnhancedRoleInfo;
  hierarchy: RoleHierarchyInfo;
  users: {
    total: UserRoleInfo[];
    system_users: UserRoleInfo[];
    tenant_users: UserRoleInfo[];
    by_tenant: Array<{
      tenant_id: string;
      tenant_name: string;
      users: UserRoleInfo[];
    }>;
  };
  permissions: {
    direct: string[];
    inherited: string[];
    effective: string[];
  };
  statistics: RoleUserStatistics;
  metadata: {
    last_updated: Date;
    data_version: string;
  };
}

/**
 * 角色健康檢查結果介面
 */
export interface RoleHealthCheck {
  role_id: string;
  is_healthy: boolean;
  issues: string[];
  warnings: string[];
  recommendations: string[];
  checked_at: Date;
}

/**
 * 角色使用分析結果介面
 */
export interface RoleUsageAnalysis {
  role_id: string;
  role_name: string;
  impact_scope: {
    direct_users: number;
    indirect_users: number;
    affected_roles: number;
    permission_scope: number;
  };
  usage_patterns: {
    is_actively_used: boolean;
    is_hierarchy_root: boolean;
    is_hierarchy_leaf: boolean;
    is_isolated: boolean;
    has_system_users: boolean;
    has_tenant_users: boolean;
    is_multi_tenant: boolean;
  };
  risk_assessment: {
    deletion_risk: 'low' | 'medium' | 'high';
    modification_risk: 'low' | 'medium' | 'high';
    security_risk: 'low' | 'medium' | 'high';
  };
  hierarchy: {
    level: number;
    parent_roles: string[];
    child_roles: string[];
  };
  recommendations: string[];
  analyzed_at: Date;
}

/**
 * 批量角色摘要結果介面
 */
export interface BatchRoleSummaryResult {
  successful: RoleSummary[];
  failed: Array<{
    role_id: string;
    error: string;
  }>;
  total_requested: number;
  success_count: number;
  failure_count: number;
}

/**
 * 角色操作結果介面
 */
export interface RoleOperationResult {
  success: boolean;
  role_id: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove';
  message?: string;
  error?: string;
  timestamp: Date;
}

/**
 * 角色驗證結果介面
 */
export interface RoleValidationResult {
  is_valid: boolean;
  errors: Array<{
    field: string;
    message: string;
    code: string;
  }>;
  warnings: Array<{
    field: string;
    message: string;
    code: string;
  }>;
}

/**
 * 角色搜索條件介面
 */
export interface RoleSearchCriteria {
  name?: string;
  display_name?: string;
  scope?: RoleScope;
  is_system?: boolean;
  tenant_id?: string;
  has_users?: boolean;
  has_permissions?: boolean;
  hierarchy_level?: number;
  created_after?: Date;
  created_before?: Date;
}

/**
 * 角色搜索結果介面
 */
export interface RoleSearchResult {
  roles: RoleSummary[];
  total_count: number;
  page_size: number;
  current_page: number;
  total_pages: number;
  has_next_page: boolean;
  has_previous_page: boolean;
}

/**
 * 角色比較結果介面
 */
export interface RoleComparisonResult {
  role1: RoleSummary;
  role2: RoleSummary;
  differences: {
    permissions: {
      only_in_role1: string[];
      only_in_role2: string[];
      common: string[];
    };
    users: {
      only_in_role1: number;
      only_in_role2: number;
      common: number;
    };
    hierarchy: {
      level_difference: number;
      different_parents: boolean;
      different_children: boolean;
    };
  };
  similarity: {
    permission_similarity: number; // 0-1
    user_similarity: number; // 0-1
    overall_similarity: number; // 0-1
  };
  recommendations: string[];
}

/**
 * 角色審計記錄介面
 */
export interface RoleAuditRecord {
  id: string;
  role_id: string;
  role_name: string;
  operation: 'create' | 'update' | 'delete' | 'assign' | 'remove' | 'permission_change';
  operator_id: string;
  operator_type: UserType;
  changes: Record<
    string,
    {
      before: any;
      after: any;
    }
  >;
  reason?: string;
  timestamp: Date;
  ip_address?: string;
  user_agent?: string;
}

/**
 * 角色權限矩陣介面
 */
export interface RolePermissionMatrix {
  roles: Array<{
    id: string;
    name: string;
    display_name: string;
  }>;
  permissions: Array<{
    id: string;
    action: string;
    subject: string;
    category: string;
  }>;
  matrix: Record<string, Record<string, boolean>>; // role_id -> permission_id -> has_permission
  effective_matrix: Record<string, Record<string, boolean>>; // 包含繼承權限
}

/**
 * 角色建議介面
 */
export interface RoleSuggestion {
  type: 'create' | 'merge' | 'split' | 'delete' | 'restructure';
  title: string;
  description: string;
  affected_roles: string[];
  expected_benefits: string[];
  potential_risks: string[];
  priority: 'low' | 'medium' | 'high';
  estimated_effort: 'low' | 'medium' | 'high';
  auto_applicable: boolean;
}

/**
 * 角色最佳化建議介面
 */
export interface RoleOptimizationSuggestions {
  suggestions: RoleSuggestion[];
  summary: {
    total_suggestions: number;
    high_priority: number;
    auto_applicable: number;
    estimated_impact: 'low' | 'medium' | 'high';
  };
  generated_at: Date;
}

/**
 * 角色匯出配置介面
 */
export interface RoleExportConfig {
  include_users: boolean;
  include_permissions: boolean;
  include_hierarchy: boolean;
  include_statistics: boolean;
  format: 'json' | 'csv' | 'xlsx';
  scope?: RoleScope;
  tenant_id?: string;
}

/**
 * 角色匯入配置介面
 */
export interface RoleImportConfig {
  overwrite_existing: boolean;
  validate_hierarchy: boolean;
  validate_permissions: boolean;
  dry_run: boolean;
  default_scope: RoleScope;
  default_tenant_id?: string;
}

/**
 * 角色匯入結果介面
 */
export interface RoleImportResult {
  total_processed: number;
  successful: number;
  failed: number;
  skipped: number;
  errors: Array<{
    row: number;
    role_id?: string;
    error: string;
  }>;
  warnings: Array<{
    row: number;
    role_id?: string;
    warning: string;
  }>;
  summary: {
    roles_created: number;
    roles_updated: number;
    permissions_assigned: number;
    hierarchy_relations_created: number;
  };
}
