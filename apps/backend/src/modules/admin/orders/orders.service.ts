import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../core/prisma/prisma.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import {
  Order,
  OrderDetail,
  OrderHistoryItem,
  OrderStatus,
  PaymentStatus,
} from './interfaces/order.interface';
import { v4 as uuidv4 } from 'uuid';
import { Prisma, orders } from '@prisma/client';
import { Decimal } from 'decimal.js';

@Injectable()
export class OrdersService {
  constructor(private readonly prisma: PrismaService) {}

  async create(createOrderDto: CreateOrderDto, createdBy: string): Promise<orders> {
    const {
      tenantId,
      planId,
      startDate,
      period,
      endDate: endDateDto,
      tenantName,
      planName,
      amount,
      numberOfSubscribers,
      billingCycle,
      remarks,
    } = createOrderDto;

    const [tenant, plan] = await Promise.all([
      this.prisma.tenants.findUnique({ where: { id: tenantId } }),
      this.prisma.plans.findUnique({ where: { id: planId } }),
    ]);

    if (!tenant) throw new BadRequestException(`租戶不存在: ${tenantId}`);
    if (!plan) throw new BadRequestException(`方案不存在: ${planId}`);

    const endDate = endDateDto ? new Date(endDateDto) : new Date(startDate);
    if (!endDateDto) {
      endDate.setMonth(endDate.getMonth() + period);
    }

    return this.prisma.orders.create({
      data: {
        id: uuidv4(),
        tenant_id: tenantId,
        plan_id: planId,
        tenant_name: tenantName,
        plan_name: planName,
        amount: amount,
        period: period,
        number_of_subscribers: numberOfSubscribers,
        start_date: new Date(startDate),
        end_date: endDate,
        billing_cycle: billingCycle || 'monthly',
        remarks: remarks,
        status: 'pending',
        created_at: new Date(),
        updated_at: new Date(),
        order_histories: {
          create: {
            id: uuidv4(),
            type: 'CREATE',
            status: 'pending',
            description: '訂單已建立',
            by: createdBy,
          },
        },
      },
    });
  }

  async findAll(params: {
    search?: string;
    status?: string;
    startDate?: string;
    endDate?: string;
  }) {
    const { search, status, startDate, endDate } = params;
    const where: Prisma.ordersWhereInput = {};

    if (search) {
      where.OR = [
        { tenant_name: { contains: search, mode: 'insensitive' } },
        { plan_name: { contains: search, mode: 'insensitive' } },
      ];
    }
    if (status) where.status = status;
    if (startDate)
      where.created_at = {
        ...(where.created_at as Prisma.DateTimeFilter),
        gte: new Date(startDate),
      };
    if (endDate)
      where.created_at = { ...(where.created_at as Prisma.DateTimeFilter), lte: new Date(endDate) };

    return this.prisma.orders.findMany({
      where,
      orderBy: { created_at: 'desc' },
    });
  }

  async findOne(id: string) {
    const order = await this.prisma.orders.findUnique({
      where: { id },
      include: {
        order_histories: { orderBy: { created_at: 'desc' } },
        tenants: true,
        plans: true,
        payments: true,
      },
    });

    if (!order) {
      throw new NotFoundException(`訂單不存在: ${id}`);
    }
    return order;
  }

  async update(id: string, updateOrderDto: UpdateOrderDto, updatedBy: string) {
    const existingOrder = await this.findOne(id);
    const updateData: Prisma.ordersUpdateInput = {};
    const changes: string[] = [];

    const fieldsToUpdate: (keyof UpdateOrderDto)[] = [
      'tenantName',
      'planName',
      'amount',
      'period',
      'numberOfSubscribers',
      'startDate',
      'endDate',
      'status',
      'remarks',
      'billingCycle',
      'paymentMethod',
      'paymentStatus',
      'contactName',
      'contactEmail',
    ];

    const fieldToDbCol: Record<string, string> = {
      tenantName: 'tenant_name',
      planName: 'plan_name',
      numberOfSubscribers: 'number_of_subscribers',
      startDate: 'start_date',
      endDate: 'end_date',
      billingCycle: 'billing_cycle',
      paymentMethod: 'payment_method',
      paymentStatus: 'payment_status',
      contactName: 'contact_name',
      contactEmail: 'contact_email',
    };

    for (const field of fieldsToUpdate) {
      const dbField = fieldToDbCol[field] || field;
      if (updateOrderDto[field] !== undefined && updateOrderDto[field] !== existingOrder[dbField]) {
        updateData[dbField] = updateOrderDto[field];
        changes.push(`${field} updated`);
      }
    }

    if (Object.keys(updateData).length > 0) {
      updateData.updated_at = new Date();
      await this.prisma.orders.update({ where: { id }, data: updateData });

      if (changes.length > 0) {
        await this.prisma.order_histories.create({
          data: {
            id: uuidv4(),
            order_id: id,
            type: 'UPDATE',
            status: (updateData.status as string) || existingOrder.status,
            description: `訂單已更新: ${changes.join(', ')}`,
            by: updatedBy,
          },
        });
      }
    }

    return this.findOne(id);
  }

  async updateStatus(id: string, status: string, updatedBy: string) {
    const existingOrder = await this.findOne(id);
    if (existingOrder.status === status) return existingOrder;

    await this.prisma.orders.update({
      where: { id },
      data: { status, updated_at: new Date() },
    });

    await this.prisma.order_histories.create({
      data: {
        id: uuidv4(),
        order_id: id,
        type: 'STATUS_CHANGE',
        status,
        description: `狀態由 ${existingOrder.status} 更新為 ${status}`,
        by: updatedBy,
      },
    });

    return this.findOne(id);
  }

  async remove(id: string) {
    await this.findOne(id);
    return this.prisma.orders.delete({ where: { id } });
  }

  // 輔助方法
  private getHistoryTypeByStatus(status: OrderStatus): 'info' | 'success' | 'warning' | 'danger' {
    switch (status) {
      case OrderStatus.COMPLETED:
        return 'success';
      case OrderStatus.CANCELLED:
        return 'danger';
      default:
        return 'info';
    }
  }

  private getStatusMessage(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.COMPLETED:
        return '訂單已完成';
      case OrderStatus.CANCELLED:
        return '訂單已取消';
      case OrderStatus.PENDING:
        return '訂單待處理';
      default:
        return '訂單狀態已更新';
    }
  }

  private getStatusLabel(status: OrderStatus): string {
    switch (status) {
      case OrderStatus.COMPLETED:
        return '已完成';
      case OrderStatus.CANCELLED:
        return '已取消';
      case OrderStatus.PENDING:
        return '待處理';
      default:
        return status;
    }
  }

  private mapToOrderResponse(order: any): Order {
    return {
      id: order.id,
      tenant_name: order.tenant_name,
      plan_name: order.plan_name,
      amount: order.amount,
      period: order.period,
      number_of_subscribers: order.number_of_subscribers,
      start_date: order.start_date.toISOString ? order.start_date.toISOString() : order.start_date,
      end_date: order.end_date.toISOString ? order.end_date.toISOString() : order.end_date,
      status: order.status,
      billing_cycle: order.billing_cycle || 'monthly',
      created_at: order.created_at.toISOString ? order.created_at.toISOString() : order.created_at,
      updated_at: order.updated_at.toISOString ? order.updated_at.toISOString() : order.updated_at,
      remarks: order.remarks,
    };
  }

  private mapToOrderDetailResponse(order: any): OrderDetail {
    const orderHistory: OrderHistoryItem[] = (order.order_histories || []).map((history) => {
      // 處理顯示日期，確保以正確的格式顯示
      const dateDisplay = this.formatDateTime(history.created_at);

      return {
        type: history.type,
        date: dateDisplay,
        status: history.status,
        description: history.description,
        by: history.by,
      };
    });

    return {
      id: order.id,
      tenant_name: order.tenant_name,
      plan_name: order.plan_name,
      amount: order.amount,
      period: order.period,
      number_of_subscribers: order.number_of_subscribers,
      start_date: order.start_date.toISOString ? order.start_date.toISOString() : order.start_date,
      end_date: order.end_date.toISOString ? order.end_date.toISOString() : order.end_date,
      status: order.status,
      billing_cycle: order.billing_cycle || 'monthly',
      created_at: order.created_at.toISOString ? order.created_at.toISOString() : order.created_at,
      updated_at: order.updated_at.toISOString ? order.updated_at.toISOString() : order.updated_at,
      remarks: order.remarks,
      // 付款相關信息
      payment_method: order.payment_method || '未指定',
      payment_status: order.payment_status || 'pending',
      // 聯絡人相關信息
      contact_name: order.contact_name || '未指定',
      contact_email: order.contact_email || '未指定',
      // 租戶詳細信息
      tenant: order.tenant
        ? {
            id: order.tenant.id,
            name: order.tenant.name,
            contact_name: order.tenant.contact_name || '未指定',
            contact_email: order.tenant.contact_email || '未指定',
          }
        : {
            id: order.id,
            name: order.tenant_name,
            contact_name: order.contact_name || '未指定',
            contact_email: order.contact_email || '未指定',
          },
      // 方案詳細信息
      plan: order.plan
        ? {
            name: order.plan.name,
            description: order.plan.description,
            price: order.plan.price,
            billing_cycle: order.plan.billing_cycle || 'monthly',
            users_limit: order.plan.max_users || order.number_of_subscribers,
            workspaces_limit: order.plan.max_projects || 1,
          }
        : {
            name: order.plan_name,
            description: '未提供方案詳細描述',
            price: order.amount,
            billing_cycle: 'monthly',
            users_limit: order.number_of_subscribers,
            workspaces_limit: 1,
          },
      // 訂單歷史記錄
      order_history: orderHistory,
    };
  }

  private formatDate(dateString: string): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '無效日期';
      return date.toISOString().split('T')[0]; // 格式化為 YYYY-MM-DD
    } catch (e) {
      return '無效日期';
    }
  }

  private formatDateTime(dateString: string | Date): string {
    try {
      const date = new Date(dateString);
      if (isNaN(date.getTime())) return '無效日期';
      return date.toISOString(); // 保留完整的 ISO 格式用於前端顯示
    } catch (e) {
      return '無效日期';
    }
  }
}
