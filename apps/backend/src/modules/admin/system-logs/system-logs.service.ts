import { Injectable } from '@nestjs/common';
import { SystemLogService } from '@/common/services/system-log.service';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { SystemLogQueryDto, SystemLogExportQueryDto } from './dto/system-log.dto';

export interface LogFilters {
  level?: string;
  userId?: string;
  action?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  searchQuery?: string;
  limit?: number;
  offset?: number;
}

@Injectable()
export class SystemLogsService {
  constructor(
    private readonly systemLogService: SystemLogService,
    private readonly prisma: PrismaService,
  ) {}

  /**
   * 查詢系統日誌列表
   * @param queryDto 查詢參數
   * @returns 日誌列表和總數
   */
  async getLogs(queryDto: SystemLogQueryDto) {
    const filters: LogFilters = {
      level: queryDto.level,
      userId: queryDto.user_id,
      action: queryDto.action,
      startDate: queryDto.start_date,
      endDate: queryDto.end_date,
      status: queryDto.status,
      searchQuery: queryDto.search_query,
      limit: parseInt(queryDto.limit || '50'),
      offset: parseInt(queryDto.offset || '0'),
    };

    // 記錄接收到的參數用於除錯
    console.log('SystemLogsService 接收到的 query 參數:', queryDto);

    // 清理空值和空字串，但保留數字型參數
    const cleanedFilters = { ...filters };
    Object.keys(cleanedFilters).forEach((key) => {
      const value = cleanedFilters[key];
      if (key === 'limit' || key === 'offset') {
        // 保留數字型參數
        return;
      }
      if (value === undefined || value === null || value === '') {
        delete cleanedFilters[key];
      }
    });

    console.log('SystemLogsService 清理後的篩選條件:', cleanedFilters);

    const [logs, total] = await Promise.all([
      this.systemLogService.findLogs(cleanedFilters),
      this.systemLogService.countLogs(cleanedFilters),
    ]);

    return { total, logs };
  }

  /**
   * 匯出系統日誌
   * @param exportDto 匯出參數
   * @returns 日誌資料
   */
  async exportLogs(exportDto: SystemLogExportQueryDto) {
    const filters = {
      level: exportDto.level,
      userId: exportDto.user_id,
      action: exportDto.action,
      startDate: exportDto.start_date,
      endDate: exportDto.end_date,
      status: exportDto.status,
      searchQuery: exportDto.search_query,
    };

    // 清理空值和空字串
    const cleanedFilters = { ...filters };
    Object.keys(cleanedFilters).forEach((key) => {
      const value = cleanedFilters[key];
      if (value === undefined || value === null || value === '') {
        delete cleanedFilters[key];
      }
    });

    const logs = await this.systemLogService.findLogs(cleanedFilters);
    return logs;
  }

  /**
   * 生成 CSV 格式內容
   * @param logs 日誌資料
   * @returns CSV 字串
   */
  generateCSV(logs: any[]): string {
    const headers = ['時間', '等級', '使用者ID', '操作', '狀態', 'IP位址', '路徑', '方法', '訊息'];

    const csvRows = [
      headers.join(','),
      ...logs.map((log) =>
        [
          `"${log.created_at ? new Date(log.created_at).toLocaleString('zh-TW') : ''}"`,
          `"${log.level || ''}"`,
          `"${log.user_id || ''}"`,
          `"${log.action || ''}"`,
          `"${log.status || ''}"`,
          `"${log.ip || ''}"`,
          `"${log.path || ''}"`,
          `"${log.method || ''}"`,
          `"${(log.message || '').replace(/"/g, '""')}"`, // 處理CSV中的引號
        ].join(','),
      ),
    ];

    return csvRows.join('\n');
  }
}
