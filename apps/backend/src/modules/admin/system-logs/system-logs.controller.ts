import { Controller, Get, Query, UseGuards, Res } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiBearerAuth, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { JwtAuthGuard } from '@/modules/core/auth/guards/auth.guard';
import { PoliciesGuard } from '@/casl/guards/permission.guard';
import { CheckPolicies } from '@/casl/decorators/check-policies.decorator';
import { Actions, Subjects } from '@horizai/permissions';
import { SystemLogsService } from './system-logs.service';
import { Response } from 'express';
import { AppAbility } from '@/types/models/casl.model';
import {
  SystemLogQueryDto,
  SystemLogExportQueryDto,
  SystemLogListResponseDto,
} from './dto/system-log.dto';

@ApiTags('admin/system-logs')
@Controller('admin/system-logs')
@UseGuards(JwtAuthGuard, PoliciesGuard)
@ApiBearerAuth()
export class SystemLogsController {
  constructor(private readonly systemLogsService: SystemLogsService) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '查詢審計日誌列表' })
  @ApiResponse({
    status: 200,
    description: '成功取得日誌列表',
    type: SystemLogListResponseDto,
  })
  async getLogs(@Query() queryDto: SystemLogQueryDto): Promise<SystemLogListResponseDto> {
    return await this.systemLogsService.getLogs(queryDto);
  }

  @Get('export')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.SYSTEM_LOG))
  @ApiOperation({ summary: '匯出審計日誌' })
  async exportLogs(@Res() res: Response, @Query() exportDto: SystemLogExportQueryDto) {
    // 設定預設格式
    const exportFormat = exportDto.format || 'csv';

    const logs = await this.systemLogsService.exportLogs(exportDto);

    const timestamp = new Date().toISOString().split('T')[0];
    const filename = `audit-logs-${timestamp}.${exportFormat === 'excel' ? 'xlsx' : 'csv'}`;

    if (exportFormat === 'excel' || exportFormat === 'csv') {
      const csvContent = this.systemLogsService.generateCSV(logs);

      res.setHeader(
        'Content-Type',
        exportFormat === 'excel'
          ? 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
          : 'text/csv; charset=utf-8',
      );
      res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);

      // 添加BOM以支援中文
      res.send('\uFEFF' + csvContent);
    }
  }
}
