import { Module } from '@nestjs/common';
import { CaslModule } from '@/casl/casl.module';
import { CommonModule } from '@/common/common.module';
import { SystemLogsController } from './system-logs.controller';
import { SystemLogsService } from './system-logs.service';

@Module({
  imports: [CaslModule, CommonModule],
  controllers: [SystemLogsController],
  providers: [SystemLogsService],
  exports: [SystemLogsService],
})
export class SystemLogsModule {}
