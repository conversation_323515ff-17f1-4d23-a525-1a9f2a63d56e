import {
  Controller,
  Get,
  Post,
  Body,
  Put,
  Param,
  Delete,
  UseGuards,
  BadRequestException,
  Req,
  Query,
} from '@nestjs/common';
import { PermissionsService } from './permissions.service';
import {
  CreatePermissionDto,
  UpdatePermissionDto,
  PermissionResponseDto,
} from './dto/permission.dto';
import { JwtAuthGuard } from '../../core/auth/guards/auth.guard';
import { PoliciesGuard } from '../../../casl/guards/permission.guard';
import { CheckPolicies } from '../../../casl/decorators/check-policies.decorator';
import { AppAbility } from '../../../types/models/casl.model';
import { ApiTags, ApiBearerAuth, ApiOperation, ApiResponse, ApiParam } from '@nestjs/swagger';
import { PermissionSyncService } from './permission-sync.service';
import { Actions, Subjects } from '@horizai/permissions';
import { SystemLogService } from '../../../common/services/system-log.service';
import { SyncPermissionsDto, ScanPermissionsDto } from './dto/sync.dto';

@ApiTags('admin/permissions')
@ApiBearerAuth()
@Controller('admin/permissions')
@UseGuards(JwtAuthGuard, PoliciesGuard)
export class PermissionsController {
  constructor(
    private readonly permissionsService: PermissionsService,
    private readonly permissionSyncService: PermissionSyncService,
    private readonly systemLogService: SystemLogService,
  ) {}

  @Get()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({ summary: '讀取所有權限定義' })
  @ApiResponse({ status: 200, type: [PermissionResponseDto] })
  async findAll(): Promise<PermissionResponseDto[]> {
    const list = await this.permissionsService.findAll();
    return list.map((p) => ({
      id: p.id,
      action: p.action,
      subject: p.subject,
      conditions: p.conditions,
      fields: p.fields,
      description: p.description ?? undefined,
      created_at: p.created_at,
      updated_at: p.updated_at,
    }));
  }

  @Get('test')
  @ApiOperation({ summary: '測試權限端點' })
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  async testPermission() {
    return { message: '測試權限端點成功' };
  }

  @Get('sync-status')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({ summary: '獲取權限同步狀態' })
  async getSyncStatus() {
    try {
      const result = await this.permissionsService.getSyncStatus();

      return {
        success: true,
        data: result,
        message: '權限同步狀態獲取成功',
      };
    } catch (error) {
      throw new BadRequestException(`獲取權限同步狀態失敗: ${error.message}`);
    }
  }

  @Get('sync-report')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({ summary: '獲取權限同步報告' })
  async getSyncReport(@Query('format') format: 'json' | 'markdown' = 'json') {
    try {
      const result = await this.permissionsService.getSyncReport(format);

      return {
        success: true,
        data: result,
        message: '權限同步報告獲取成功',
      };
    } catch (error) {
      throw new BadRequestException(`獲取權限同步報告失敗: ${error.message}`);
    }
  }

  @Get(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  @ApiOperation({ summary: '讀取單一權限定義' })
  @ApiParam({ name: 'id', description: '權限ID' })
  @ApiResponse({ status: 200, type: PermissionResponseDto })
  async findOne(@Param('id') id: string): Promise<PermissionResponseDto> {
    const p = await this.permissionsService.findOne(id);
    return {
      id: p.id,
      action: p.action,
      subject: p.subject,
      conditions: p.conditions,
      fields: p.fields,
      description: p.description ?? undefined,
      created_at: p.created_at,
      updated_at: p.updated_at,
    };
  }

  @Post()
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.CREATE, Subjects.PERMISSION))
  @ApiOperation({ summary: '建立新的權限定義' })
  @ApiResponse({ status: 201, type: PermissionResponseDto })
  async create(@Body() dto: CreatePermissionDto): Promise<PermissionResponseDto> {
    const p = await this.permissionsService.create(dto);
    return {
      id: p.id,
      action: p.action,
      subject: p.subject,
      conditions: p.conditions,
      fields: p.fields,
      description: p.description ?? undefined,
      created_at: p.created_at,
      updated_at: p.updated_at,
    };
  }

  @Put(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.UPDATE, Subjects.PERMISSION))
  @ApiOperation({ summary: '更新權限定義' })
  @ApiParam({ name: 'id', description: '權限ID' })
  @ApiResponse({ status: 200, type: PermissionResponseDto })
  async update(
    @Param('id') id: string,
    @Body() dto: UpdatePermissionDto,
  ): Promise<PermissionResponseDto> {
    const p = await this.permissionsService.update(id, dto);
    return {
      id: p.id,
      action: p.action,
      subject: p.subject,
      conditions: p.conditions,
      fields: p.fields,
      description: p.description ?? undefined,
      created_at: p.created_at,
      updated_at: p.updated_at,
    };
  }

  @Delete(':id')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.DELETE, Subjects.PERMISSION))
  @ApiOperation({ summary: '刪除權限定義' })
  @ApiParam({ name: 'id', description: '權限ID' })
  @ApiResponse({ status: 200, description: '刪除成功' })
  async remove(@Param('id') id: string): Promise<{ success: boolean }> {
    await this.permissionsService.remove(id);
    return { success: true };
  }

  /**
   * 執行權限自動化同步
   */
  @Post('sync')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.MANAGE, Subjects.PERMISSION))
  async syncPermissions(@Body() syncDto: SyncPermissionsDto, @Req() req: any) {
    try {
      const result = await this.permissionsService.syncPermissions(syncDto);

      // 記錄審計日誌
      await this.systemLogService.logAudit({
        message: `權限同步操作: ${syncDto.dry_run ? '預覽' : '執行'}`,
        user_id: req.user.id,
        action: 'PERMISSION_SYNC',
        target_resource: 'PERMISSION',
        target_resource_id: 'all',
        ip: req.ip,
        details: {
          dry_run: syncDto.dry_run,
          force: syncDto.force,
          changes_count: result.changes?.length || 0,
        },
        path: req.path,
        method: req.method,
        status: 'SUCCESS',
      });

      return {
        success: true,
        data: result,
        message: syncDto.dry_run ? '權限同步預覽完成' : '權限同步完成',
      };
    } catch (error) {
      throw new BadRequestException(`權限同步失敗: ${error.message}`);
    }
  }

  @Post('scan')
  @CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.PERMISSION))
  async scanPermissions(@Body() scanDto: ScanPermissionsDto, @Req() req: any) {
    try {
      const result = await this.permissionsService.scanPermissions(scanDto);

      return {
        success: true,
        data: result,
        message: '權限掃描完成',
      };
    } catch (error) {
      throw new BadRequestException(`權限掃描失敗: ${error.message}`);
    }
  }
}
