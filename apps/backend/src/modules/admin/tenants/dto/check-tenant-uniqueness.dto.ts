import { ApiProperty } from '@nestjs/swagger';
import { IsString, IsOptional } from 'class-validator';

export class CheckTenantUniquenessDto {
  @ApiProperty({ description: '公司名稱' })
  @IsString()
  company_name: string;

  @ApiProperty({ description: '國家/地區代碼', default: 'TW' })
  @IsString()
  @IsOptional()
  country?: string = 'TW';

  @ApiProperty({ description: 'Email 網域', required: false })
  @IsString()
  @IsOptional()
  domain?: string;
}
