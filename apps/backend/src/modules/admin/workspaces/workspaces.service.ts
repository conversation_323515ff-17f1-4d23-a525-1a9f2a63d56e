import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '@/modules/core/prisma/prisma.service';
import { createId } from '@paralleldrive/cuid2';
import { TenantQuotaService } from '../tenants/services/tenant-quota.service';
import { Prisma } from '@prisma/client';

@Injectable()
export class WorkspacesService {
  constructor(
    private readonly prisma: PrismaService,
    private readonly tenantQuotaService: TenantQuotaService,
  ) {}

  async create(data: {
    name: string;
    description?: string;
    tenantId: string;
    ownerId: string;
    settings?: Record<string, any>;
  }) {
    try {
      return await this.prisma.workspaces.create({
        data: {
          id: createId(),
          name: data.name,
          description: data.description,
          tenant_id: data.tenantId,
          owner_id: data.ownerId,
          settings: data.settings || {},
          status: 'active',
        },
      });
    } catch (error) {
      throw new BadRequestException(`Failed to create workspace: ${error.message}`);
    }
  }

  async findAll(tenantId: string) {
    return this.prisma.workspaces.findMany({
      where: { tenant_id: tenantId },
    });
  }

  async findOne(id: string, tenantId: string) {
    const workspace = await this.prisma.workspaces.findFirst({
      where: { id, tenant_id: tenantId },
    });

    if (!workspace) {
      throw new NotFoundException(`Workspace with ID ${id} not found`);
    }

    return workspace;
  }

  async update(
    id: string,
    tenantId: string,
    data: {
      name?: string;
      description?: string;
      settings?: Record<string, any>;
      status?: string;
    },
  ) {
    try {
      const updateData: any = {};
      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined) updateData.description = data.description;
      if (data.settings !== undefined) updateData.settings = data.settings;
      if (data.status !== undefined) updateData.status = data.status;
      updateData.updated_at = new Date();
      return await this.prisma.workspaces.update({
        where: { id },
        data: updateData,
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`Workspace with ID ${id} not found`);
      }
      throw error;
    }
  }

  async remove(id: string, tenantId: string) {
    try {
      await this.findOne(id, tenantId); // Check if workspace exists
      return await this.prisma.workspaces.delete({
        where: { id },
      });
    } catch (error) {
      if (error instanceof Prisma.PrismaClientKnownRequestError && error.code === 'P2025') {
        throw new NotFoundException(`Workspace with ID ${id} not found`);
      }
      throw error;
    }
  }

  async addMember(workspaceId: string, userId: string, role: string) {
    return this.prisma.workspace_members.create({
      data: {
        workspace_id: workspaceId,
        tenant_user_id: userId,
        role: role as any,
      },
    });
  }

  async removeMember(workspaceId: string, userId: string) {
    const result = await this.prisma.workspace_members.deleteMany({
      where: {
        workspace_id: workspaceId,
        tenant_user_id: userId,
      },
    });

    if (result.count === 0) {
      throw new NotFoundException('Workspace member not found');
    }
  }

  async getMembers(workspaceId: string) {
    return this.prisma.workspace_members.findMany({
      where: { workspace_id: workspaceId },
      include: {
        tenant_users: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true,
            title: true,
            department: true,
            status: true,
            last_login_at: true,
          },
        },
      },
    });
  }

  async getMembersByTenant(workspaceId: string, tenantId: string) {
    return this.prisma.workspace_members.findMany({
      where: {
        workspace_id: workspaceId,
        tenant_users: {
          tenant_id: tenantId,
        },
      },
      include: {
        tenant_users: {
          select: {
            id: true,
            email: true,
            name: true,
            avatar: true,
            title: true,
            department: true,
            status: true,
            last_login_at: true,
            tenant_id: true,
          },
        },
      },
    });
  }

  async updateMemberRole(workspaceId: string, userId: string, role: string) {
    const result = await this.prisma.workspace_members.updateMany({
      where: {
        workspace_id: workspaceId,
        tenant_user_id: userId,
      },
      data: {
        role: role as any,
      },
    });

    if (result.count === 0) {
      throw new NotFoundException('Workspace member not found');
    }
  }

  async getWorkspacesByUser(userId: string) {
    return this.prisma.workspace_members.findMany({
      where: { tenant_user_id: userId },
      select: {
        role: true,
        workspace: {
          select: {
            id: true,
            name: true,
            description: true,
            status: true,
            tenant_id: true,
            created_at: true,
            updated_at: true,
          },
        },
      },
    });
  }

  /**
   * 獲取工作區統計資訊
   */
  async getWorkspaceStats(workspaceId: string, tenantId: string) {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const [memberCount, projectCount, recentActivity, membersByRole] = await Promise.all([
      // 成員總數
      this.prisma.workspace_members.count({
        where: { workspace_id: workspaceId },
      }),

      // 專案總數
      this.prisma.projects.count({
        where: { workspace_id: workspaceId },
      }),

      // 最近活動 (最近 7 天的專案更新)
      this.prisma.projects.count({
        where: {
          workspace_id: workspaceId,
          updated_at: {
            gte: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
          },
        },
      }),

      // 按角色分組的成員數
      this.prisma.workspace_members.groupBy({
        by: ['role'],
        where: { workspace_id: workspaceId },
        _count: {
          role: true,
        },
      }),
    ]);

    return {
      memberCount,
      projectCount,
      recentActivity,
      membersByRole: membersByRole.reduce(
        (acc, item) => {
          acc[item.role] = (item._count as any)?.role || 0;
          return acc;
        },
        {} as Record<string, number>,
      ),
      lastUpdated: new Date(),
    };
  }

  /**
   * 從模板創建工作區
   */
  async createFromTemplate(
    templateId: string,
    data: {
      name: string;
      description?: string;
      tenantId: string;
      ownerId: string;
    },
  ) {
    const template = await this.prisma.workspace_templates.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new NotFoundException('工作區模板不存在');
    }

    return this.create({
      ...data,
      settings: template.default_settings as Record<string, any>,
    });
  }

  /**
   * 批量邀請用戶到工作區
   */
  async inviteUsers(
    workspaceId: string,
    tenantId: string,
    inviteData: {
      emails: string[];
      role: string;
      message?: string;
      invitedBy: string;
    },
  ): Promise<{
    total: number;
    successful: number;
    results: Array<{
      email: string;
      status: string;
      message: string;
    }>;
  }> {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const results: Array<{
      email: string;
      status: string;
      message: string;
    }> = [];

    for (const email of inviteData.emails) {
      try {
        // 檢查用戶是否已存在於租戶中
        const existingUser = await this.prisma.tenant_users.findFirst({
          where: {
            email,
            tenant_id: tenantId,
          },
        });

        if (existingUser) {
          // 檢查是否已經是工作區成員
          const existingMember = await this.prisma.workspace_members.findFirst({
            where: {
              workspace_id: workspaceId,
              tenant_user_id: existingUser.id,
            },
          });

          if (!existingMember) {
            // 直接添加為成員
            await this.addMember(workspaceId, existingUser.id, inviteData.role);
            results.push({
              email,
              status: 'added',
              message: '用戶已直接添加到工作區',
            });
          } else {
            results.push({
              email,
              status: 'already_member',
              message: '用戶已經是工作區成員',
            });
          }
        } else {
          // 創建邀請記錄
          await this.prisma.workspace_invitations.create({
            data: {
              id: createId(),
              workspace_id: workspaceId,
              email,
              role: inviteData.role as any, // 若有 Enum 請轉型
              invited_by: inviteData.invitedBy,
              token: createId(),
              expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
              status: 'pending',
            },
          });

          results.push({
            email,
            status: 'invited',
            message: '邀請已發送',
          });
        }
      } catch (error) {
        results.push({
          email,
          status: 'error',
          message: `邀請失敗: ${error instanceof Error ? error.message : '未知錯誤'}`,
        });
      }
    }

    return {
      total: inviteData.emails.length,
      successful: results.filter((r) => ['added', 'invited'].includes(r.status)).length,
      results,
    };
  }

  /**
   * 獲取工作區活動日誌
   */
  async getWorkspaceActivity(
    workspaceId: string,
    tenantId: string,
    options: {
      limit?: number;
      offset?: number;
      startDate?: Date;
      endDate?: Date;
    } = {},
  ) {
    // 驗證工作區存在且屬於租戶
    await this.findOne(workspaceId, tenantId);

    const { limit = 50, offset = 0, startDate, endDate } = options;

    const whereClause: any = {
      workspace_id: workspaceId,
    };

    if (startDate || endDate) {
      whereClause.created_at = {};
      if (startDate) whereClause.created_at.gte = startDate;
      if (endDate) whereClause.created_at.lte = endDate;
    }

    return this.prisma.workspace_activity_logs.findMany({
      where: whereClause,
      orderBy: {
        created_at: 'desc',
      },
      take: limit,
      skip: offset,
    });
  }

  /**
   * 複製工作區
   */
  async duplicateWorkspace(
    sourceWorkspaceId: string,
    tenantId: string,
    data: {
      name: string;
      description?: string;
      copyMembers?: boolean;
      copyProjects?: boolean;
    },
  ) {
    const sourceWorkspace = await this.findOne(sourceWorkspaceId, tenantId);

    return this.prisma.$transaction(async (tx) => {
      // 創建新工作區
      const newWorkspace = await tx.workspaces.create({
        data: {
          id: createId(),
          name: data.name,
          description: data.description || sourceWorkspace.description,
          tenant_id: tenantId,
          owner_id: sourceWorkspace.owner_id,
          settings: sourceWorkspace.settings as any,
          status: 'active',
          updated_at: new Date(),
        },
      });

      // 複製成員 (如果需要)
      if (data.copyMembers) {
        const members = await tx.workspace_members.findMany({
          where: { workspace_id: sourceWorkspaceId },
        });

        for (const member of members) {
          await tx.workspace_members.create({
            data: {
              id: createId(),
              workspace_id: newWorkspace.id,
              tenant_user_id: member.tenant_user_id,
              role: member.role,
            },
          });
        }
      }

      // 複製專案 (如果需要)
      if (data.copyProjects) {
        const projects = await tx.projects.findMany({
          where: { workspace_id: sourceWorkspaceId },
        });

        for (const project of projects) {
          await tx.projects.create({
            data: {
              id: createId(),
              name: `${project.name} (複製)`,
              description: project.description,
              tenant_id: tenantId,
              workspace_id: newWorkspace.id,
              user_id: project.user_id,
              status: 'active',
            },
          });
        }
      }

      return newWorkspace;
    });
  }
}
