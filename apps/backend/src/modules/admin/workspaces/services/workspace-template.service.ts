import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../../../core/prisma/prisma.service';
import { createId } from '@paralleldrive/cuid2';

export interface WorkspaceTemplate {
  id: string;
  name: string;
  description?: string | null;
  defaultSettings: Record<string, any> | null;
  defaultMemberRole: string;
  isSystem: boolean;
  tenant_id?: string | null;
  createdBy: string;
  created_at: Date;
  updated_at: Date;
}

@Injectable()
export class WorkspaceTemplateService {
  constructor(private readonly prisma: PrismaService) {}

  /**
   * 創建工作區模板
   */
  async create(data: {
    name: string;
    description?: string;
    defaultSettings: Record<string, any>;
    defaultMemberRole?: string;
    isSystem?: boolean;
    tenant_id?: string;
    createdBy: string;
  }): Promise<WorkspaceTemplate> {
    const result = await this.prisma.workspace_templates.create({
      data: {
        id: createId(),
        name: data.name,
        description: data.description,
        default_settings: data.defaultSettings,
        default_member_role: data.defaultMemberRole || 'member',
        is_system: data.isSystem || false,
        tenant_id: data.tenant_id,
        created_by: data.createdBy,
      },
    });

    return {
      ...result,
      defaultSettings: result.default_settings as Record<string, any>,
      defaultMemberRole: result.default_member_role,
      isSystem: result.is_system,
      createdBy: result.created_by,
    } as WorkspaceTemplate;
  }

  /**
   * 獲取模板列表
   */
  async findAll(tenant_id?: string): Promise<WorkspaceTemplate[]> {
    const where: any = {
      OR: [
        { is_system: true }, // 系統模板
        { tenant_id: tenant_id }, // 租戶專用模板
      ],
    };

    const results = await this.prisma.workspace_templates.findMany({
      where,
      orderBy: [
        { is_system: 'desc' }, // 系統模板優先
        { created_at: 'desc' },
      ],
    });

    return results.map((result) => ({
      ...result,
      defaultSettings: result.default_settings as Record<string, any>,
      defaultMemberRole: result.default_member_role,
      isSystem: result.is_system,
      createdBy: result.created_by,
    })) as WorkspaceTemplate[];
  }

  /**
   * 獲取單個模板
   */
  async findOne(id: string, tenant_id?: string): Promise<WorkspaceTemplate> {
    const template = await this.prisma.workspace_templates.findFirst({
      where: {
        id,
        OR: [{ is_system: true }, { tenant_id: tenant_id }],
      },
    });

    if (!template) {
      throw new NotFoundException('工作區模板不存在');
    }

    return {
      ...template,
      defaultSettings: template.default_settings as Record<string, any>,
      defaultMemberRole: template.default_member_role,
      isSystem: template.is_system,
      createdBy: template.created_by,
    } as WorkspaceTemplate;
  }

  /**
   * 更新模板
   */
  async update(
    id: string,
    data: {
      name?: string;
      description?: string;
      defaultSettings?: Record<string, any>;
      defaultMemberRole?: string;
    },
    tenant_id?: string,
  ): Promise<WorkspaceTemplate> {
    // 檢查模板是否存在且有權限修改
    const template = await this.findOne(id, tenant_id);

    if (template.isSystem) {
      throw new BadRequestException('無法修改系統模板');
    }

    if (template.tenant_id !== tenant_id) {
      throw new BadRequestException('無權限修改此模板');
    }

    const result = await this.prisma.workspace_templates.update({
      where: { id },
      data: {
        ...data,
        updated_at: new Date(),
      },
    });

    return {
      ...result,
      defaultSettings: result.default_settings as Record<string, any>,
      defaultMemberRole: result.default_member_role,
      isSystem: result.is_system,
      createdBy: result.created_by,
    } as WorkspaceTemplate;
  }

  /**
   * 刪除模板
   */
  async remove(id: string, tenant_id?: string): Promise<void> {
    const template = await this.findOne(id, tenant_id);

    if (template.isSystem) {
      throw new BadRequestException('無法刪除系統模板');
    }

    if (template.tenant_id !== tenant_id) {
      throw new BadRequestException('無權限刪除此模板');
    }

    await this.prisma.workspace_templates.delete({
      where: { id },
    });
  }

  /**
   * 複製模板
   */
  async duplicate(
    sourceId: string,
    data: {
      name: string;
      description?: string;
      tenant_id?: string;
      createdBy: string;
    },
  ): Promise<WorkspaceTemplate> {
    const sourceTemplate = await this.findOne(sourceId, data.tenant_id);

    return this.create({
      name: data.name,
      description: data.description || sourceTemplate.description || undefined,
      defaultSettings: (sourceTemplate.defaultSettings as Record<string, any>) || {},
      defaultMemberRole: sourceTemplate.defaultMemberRole,
      isSystem: false, // 複製的模板不是系統模板
      tenant_id: data.tenant_id,
      createdBy: data.createdBy,
    });
  }

  /**
   * 獲取模板使用統計
   */
  async getTemplateStats(templateId: string, tenant_id?: string) {
    await this.findOne(templateId, tenant_id);

    const usageCount = await this.prisma.workspaces.count({
      where: {
        // 假設 workspaces 表有 templateId 欄位
        // templateId: templateId,
      },
    });

    return {
      templateId,
      usageCount,
      lastUsed: await this.getLastUsedDate(templateId),
    };
  }

  private async getLastUsedDate(templateId: string): Promise<Date | null> {
    const lastWorkspace = await this.prisma.workspaces.findFirst({
      where: {
        // templateId: templateId,
      },
      orderBy: {
        created_at: 'desc',
      },
      select: {
        created_at: true,
      },
    });

    return lastWorkspace?.created_at || null;
  }

  /**
   * 創建預設系統模板
   */
  async createDefaultTemplates(createdBy: string): Promise<WorkspaceTemplate[]> {
    const defaultTemplates = [
      {
        name: '基本工作區',
        description: '適合小型團隊的基本工作區設定',
        defaultSettings: {
          features: ['projects', 'tasks', 'files'],
          permissions: {
            allowGuestAccess: false,
            requireApprovalForNewMembers: true,
          },
          notifications: {
            emailNotifications: true,
            slackIntegration: false,
          },
        },
        defaultMemberRole: 'member',
      },
      {
        name: '專案管理工作區',
        description: '專為專案管理設計的工作區',
        defaultSettings: {
          features: ['projects', 'tasks', 'files', 'gantt', 'reports'],
          permissions: {
            allowGuestAccess: true,
            requireApprovalForNewMembers: false,
          },
          notifications: {
            emailNotifications: true,
            slackIntegration: true,
          },
        },
        defaultMemberRole: 'member',
      },
      {
        name: '開發團隊工作區',
        description: '適合軟體開發團隊的工作區設定',
        defaultSettings: {
          features: ['projects', 'tasks', 'files', 'code', 'ci_cd', 'issues'],
          permissions: {
            allowGuestAccess: false,
            requireApprovalForNewMembers: true,
          },
          integrations: {
            github: true,
            jira: true,
            slack: true,
          },
        },
        defaultMemberRole: 'member',
      },
    ];

    const createdTemplates: WorkspaceTemplate[] = [];
    for (const template of defaultTemplates) {
      const created = await this.create({
        ...template,
        isSystem: true,
        createdBy,
      });
      createdTemplates.push(created);
    }

    return createdTemplates;
  }
}
