import { NestFactory, Reflector } from '@nestjs/core';
import { AppModule } from './app.module';
import { ValidationPipe, Logger, ClassSerializerInterceptor } from '@nestjs/common';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { SystemLogService } from './common/services/system-log.service';
import { NestExpressApplication } from '@nestjs/platform-express';
import { IoAdapter } from '@nestjs/platform-socket.io';
import { join } from 'path';
import cookieParser from 'cookie-parser';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

// 自定義 Socket.IO 適配器
class CustomIoAdapter extends IoAdapter {
  createIOServer(port: number, options?: any): any {
    const server = super.createIOServer(port, {
      ...options,
      cors: {
        origin: process.env.ALLOWED_ORIGINS
          ? process.env.ALLOWED_ORIGINS.split(',')
          : ['http://localhost:5173', 'https://dev.horizai.com'],
        credentials: true,
        methods: ['GET', 'POST'],
      },
      path: '/socket.io/',
      transports: ['websocket', 'polling'],
    });
    return server;
  }
}

/**
 * 獲取應用程式的基礎 URL
 * @param port 應用程式端口
 * @returns 基礎 URL 字串
 */
function getBaseUrl(port?: number): string {
  if (process.env.BASE_URL) {
    return process.env.BASE_URL;
  }

  const defaultPort = port || process.env.PORT || 4000;
  return `http://localhost:${defaultPort}`;
}

async function bootstrap() {
  const startTime = Date.now();

  const app = await NestFactory.create<NestExpressApplication>(AppModule);
  const logger = new Logger('Bootstrap');

  // 配置 Socket.IO 適配器
  app.useWebSocketAdapter(new CustomIoAdapter());

  // 配置 CORS
  app.enableCors({
    origin: process.env.ALLOWED_ORIGINS
      ? process.env.ALLOWED_ORIGINS.split(',')
      : ['http://localhost:5173', 'https://dev.horizai.com'],
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS', 'PATCH', 'HEAD'],
    allowedHeaders: [
      'Content-Type',
      'Accept',
      'Authorization',
      'X-Requested-With',
      'X-HTTP-Method-Override',
      'X-XSRF-TOKEN',
      'Cookie',
      'Origin',
      'x-line-signature',
    ],
    exposedHeaders: ['Authorization'],
  });

  app.use(cookieParser());

  // 全局攔截器 - 必須在全局管道之前
  app.useGlobalInterceptors(new ClassSerializerInterceptor(app.get(Reflector)));

  // 全局管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }),
  );

  // 全局錯誤過濾器
  const systemLogService = app.get(SystemLogService);
  app.useGlobalFilters(new AllExceptionsFilter(systemLogService));

  // 啟用必要的靜態資源目錄
  app.useStaticAssets(join(__dirname, '..', 'public'));
  app.useStaticAssets(join(__dirname, '..', 'uploads'), {
    prefix: '/uploads/',
  });

  // 設定 Swagger（在全局前綴設定之前）
  const config = new DocumentBuilder()
    .setTitle('HorizAI SaaS API')
    .setDescription('HorizAI SaaS API 文件 - 完整的 API 端點與測試介面')
    .setVersion('1.0')
    .addBearerAuth(
      {
        type: 'http',
        scheme: 'bearer',
        bearerFormat: 'JWT',
        name: 'JWT',
        description: '輸入 JWT token',
        in: 'header',
      },
      'JWT-auth',
    )
    .addCookieAuth('auth_token', {
      type: 'apiKey',
      in: 'cookie',
      name: 'auth_token',
      description: 'JWT token in cookie',
    })
    .build();

  const document = SwaggerModule.createDocument(app, config);

  // 設定 Swagger UI 路徑
  SwaggerModule.setup('docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
    },
    customSiteTitle: 'HorizAI SaaS API Documentation',
    explorer: true,
  });

  // 設定全局 API 前綴
  app.setGlobalPrefix('api');

  // 額外提供 swagger json API
  const expressApp = app.getHttpAdapter().getInstance();
  expressApp.get('/swagger.json', (_req, res) => {
    res.setHeader('Content-Type', 'application/json');
    res.send(document);
  });

  logger.log(`Swagger UI 已啟用於 /docs`);
  logger.log(`Swagger JSON 已啟用於 /swagger.json`);

  const port = Number(process.env.PORT) || 4000;
  await app.listen(port);

  const duration = Date.now() - startTime;
  const baseUrl = getBaseUrl(port);

  // CLI 介面顯示
  const separator = '\n' + '='.repeat(48) + '\n';
  console.log(separator);
  console.log(`應用程式啟動完成！ (總耗時: ${duration}ms)`);
  console.log(`服務器運行於: ${baseUrl}`);
  console.log(`Swagger UI: ${baseUrl}/docs`);
  console.log(`API 文件: ${baseUrl}/swagger.json`);
  console.log(separator);
}

bootstrap();
