import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { APP_GUARD, APP_INTERCEPTOR } from '@nestjs/core';
import { EventEmitterModule } from '@nestjs/event-emitter';
import { ClsModule } from 'nestjs-cls';

// Core Modules
import { PrismaModule } from './modules/core/prisma/prisma.module';
import { StorageModule } from './modules/core/storage/storage.module';
import { AuthModule } from './modules/core/auth/auth.module';
import { JwtAuthGuard } from './modules/core/auth/guards/auth.guard';
import { EncryptionModule } from './modules/core/encryption/encryption.module';

// Admin Modules
import { AdminModule } from './modules/admin/admin.module';
import { PlansModule } from './modules/admin/plans/plans.module';
import { OrdersModule } from './modules/admin/orders/orders.module';
import { TenantsModule } from './modules/admin/tenants/tenants.module';
// TenantUsersModule 已經在 AdminModule 中導入，這裡不需要重複導入
import { TenantInterceptor } from './modules/admin/tenants/interceptors/tenant.interceptor';
import { LineModule } from './modules/admin/line/line.module';

// Workspace Modules
import { WorkspaceModule } from './modules/workspace/workspace.module';
import { ProjectsModule } from './modules/workspace/projects/projects.module';
import { PhotosModule } from './modules/workspace/photos/photos.module';
import { WorkspaceUsersModule } from './modules/workspace/users/users.module';

// Agent Module
import { AgentModule } from './modules/agent/agent.module';

// Site Module
import { SiteModule } from './modules/site/site.module';

// Common Module
import { CommonModule } from './common/common.module';
import { LoggingInterceptor } from './common/interceptors/logging.interceptor';
import { PoliciesGuard } from './casl/guards/permission.guard';

@Module({
  imports: [
    // CLS Module should be registered first
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: true,
        /* @ts-ignore */
        generateId: (req: any) => req.headers?.['x-request-id'] ?? 'some-random-id',
        setHeader: 'X-Request-Id',
      },
    }),

    // Core Configuration
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    EventEmitterModule.forRoot(),

    // Core Modules
    PrismaModule,
    StorageModule,
    AuthModule,
    EncryptionModule,

    // Admin Modules
    AdminModule,
    PlansModule,
    OrdersModule,
    TenantsModule,
    LineModule,

    // Workspace Modules
    WorkspaceModule,
    ProjectsModule,
    PhotosModule,
    WorkspaceUsersModule,

    // Agent Module
    AgentModule,

    // Site Module
    SiteModule,

    // Common Module
    CommonModule,
  ],
  providers: [
    {
      provide: APP_GUARD,
      useClass: JwtAuthGuard,
    },
    {
      provide: APP_GUARD,
      useClass: PoliciesGuard,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: TenantInterceptor,
    },
    {
      provide: APP_INTERCEPTOR,
      useClass: LoggingInterceptor,
    },
  ],
})
export class AppModule {}
