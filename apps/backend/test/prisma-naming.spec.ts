import { Prisma } from '@prisma/client';

/**
 * 檢查字串是否為 snake_case。
 * 此處的定義相對寬鬆：只要全是小寫字母、數字和底線，且不含大寫字母即可。
 * @param str - 要檢查的字串。
 * @returns 如果是 snake_case 則返回 true，否則返回 false。
 */
const isSnakeCase = (str: string): boolean => {
  // 檢查是否包含大寫字母
  if (/[A-Z]/.test(str)) {
    return false;
  }
  // 檢查是否只包含小寫字母、數字和底線
  return /^[a-z0-9_]+$/.test(str);
};

describe('Prisma Naming Convention Validation', () => {
  const models = Prisma.dmmf.datamodel.models;

  test('All model names should be in snake_case', () => {
    const modelViolations = models
      .map((model) => model.name)
      .filter((name) => !isSnakeCase(name));

    // 如果有任何不符合規範的模型，測試將失敗並列出所有違規模型名稱。
    expect(modelViolations).toEqual([]);
  });

  test('All field and relation names should be in snake_case', () => {
    const fieldViolations: string[] = [];
    models.forEach((model) => {
      model.fields.forEach((field) => {
        if (!isSnakeCase(field.name)) {
          fieldViolations.push(`${model.name}.${field.name}`);
        }
      });
    });

    // 如果有任何不符合規範的欄位，測試將失敗並列出所有違規欄位 (格式為：model_name.field_name)。
    expect(fieldViolations).toEqual([]);
  });
}); 