# HorizAI Backend

此目錄包含 HorizAI SaaS 平台的後端程式碼，使用 **NestJS** 與 **Prisma** 實作。

## 目錄結構

```text
apps/backend/
├── src/
│   ├── app.module.ts      # 根模組
│   ├── main.ts            # 進入點
│   ├── casl/              # CASL 權限控制
│   ├── common/            # 共用工具與裝飾器
│   ├── modules/           # 各業務模組
│   ├── prisma/            # Prisma 服務
│   └── types/             # 內部型別定義
├── prisma/
│   ├── schema.prisma      # 資料庫結構
│   ├── migrations/        # 遷移檔
│   ├── seed.ts            # 種子資料腳本
│   ├── init-db.ts         # 初始化腳本
│   └── templates/         # 權限與角色範本
├── scripts/               # 資料庫與環境工具
├── docs/                  # 後端相關文件
└── .env.example           # 環境變數範例
```

## 開發指令

```bash
# 安裝相依套件
pnpm install

# 啟動開發伺服器 (http://localhost:4000)
pnpm start:dev

# 資料庫遷移範例
pnpm db:create --name init
pnpm db:apply --seed
```

## 環境設定

1. 將 `.env.example` 複製為 `.env`。
2. 設定 `DATABASE_URL`、`JWT_SECRET` 等必要變數。
3. 如需初始化資料庫，可執行 `pnpm prisma migrate reset` 或 `pnpm db:reset`。

# 身份驗證與角色管理系統重構

## 主要變更

本次重構主要解決了以下問題：

1. **統一權限守衛**：

   - 移除了重複的守衛機制 (RolesGuard、EnhancedPermissionGuard、UnifiedPermissionGuard)
   - 統一使用 PoliciesGuard 進行權限檢查，簡化代碼

2. **統一 Request.ability 類型**：

   - 在 `types/express/index.ts` 中統一定義了 Request.ability 類型
   - 確保所有使用 ability 的地方具有一致的類型定義

3. **集中式權限工廠**：

   - 創建了 `AbilityFactory` 作為中央權限生成工廠
   - 與 `JwtStrategy` 整合，自動為請求附加權限

4. **角色管理 API 整合**：

   - 移除了 `UnifiedRoleController`，將功能整合到 `RolesController`
   - 增加了角色分析功能端點，提供系統概覽、健康檢查、使用分析等功能

5. **改進 @horizai/auth 整合**：
   - 確保 JWT 在 cookie 中正確設置
   - `/me` 端點現在返回完整的用戶權限信息

## 前端兼容性

本次重構保持了 API 接口的兼容性，前端代碼不需要進行大規模修改。以下是需要注意的變更：

1. 原有的 `/admin/unified-roles/**` 端點已移至 `/admin/roles/**`
2. `/me` 端點現在返回的 `abilityRules` 字段包含完整的 CASL 規則，而不是簡化版本

## 使用示例

### 訪問用戶權限

```typescript
@Get()
@UseGuards(JwtAuthGuard, PoliciesGuard)
async getProtectedResource(@Req() req: Request) {
  // req.ability 現在可以直接使用，類型為 AppAbility
  if (req.ability.can('read', 'Resource')) {
    // 用戶有讀取權限
  }
}
```

### 使用裝飾器保護路由

```typescript
@Get()
@UseGuards(JwtAuthGuard, PoliciesGuard)
@CheckPolicies((ability: AppAbility) => ability.can(Actions.READ, Subjects.RESOURCE))
async getProtectedResource() {
  // 只有具有讀取權限的用戶才能訪問
}
```

### 角色分析 API

```
GET /admin/roles/system-overview      # 系統概覽
GET /admin/roles/:id/health-check     # 角色健康檢查
GET /admin/roles/:id/usage-analysis   # 角色使用分析
```
