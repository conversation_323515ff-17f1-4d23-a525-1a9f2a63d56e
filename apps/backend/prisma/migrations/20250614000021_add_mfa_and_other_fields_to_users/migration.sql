/*
  Warnings:

  - The values [URGENT] on the enum `NotificationPriority` will be removed. If these variants are still used in the database, this will fail.
  - You are about to drop the column `created_by` on the `ai_workflows` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `albums` table. All the data in the column will be lost.
  - You are about to drop the column `photosCount` on the `albums` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `albums` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `albums` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `albums` table. All the data in the column will be lost.
  - You are about to drop the column `commentId` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `comment_mentions` table. All the data in the column will be lost.
  - You are about to drop the column `commentId` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `comment_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `authorId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `authorType` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `contentType` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `deletedAt` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `entityId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `entityType` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `isDeleted` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `isEdited` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `parentId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `threadId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `comments` table. All the data in the column will be lost.
  - You are about to drop the column `allowNotifications` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `conversationId` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `isMuted` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `isPinned` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `joinedAt` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `lastReadAt` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `lastReadMessageId` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `lastSeenAt` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `leftAt` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `mutedUntil` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `conversation_participants` table. All the data in the column will be lost.
  - You are about to drop the column `allowAudioSharing` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `allowFileSharing` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `allowImageSharing` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `allowVideoSharing` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `createdBy` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `isArchived` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `isEncrypted` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `isPrivate` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `lastActivityAt` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `lastMessageId` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `maxFileSize` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `requireApprovalForNewMembers` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `retentionDays` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `conversations` table. All the data in the column will be lost.
  - You are about to drop the column `accessType` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `accessedAt` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `fileId` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `shareId` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userAgent` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `file_access_logs` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `fileId` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `grantedAt` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `grantedBy` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `file_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `allowComment` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `allowDownload` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `currentDownloads` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `fileId` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `isActive` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `maxDownloads` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `requireAuth` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `shareToken` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `shareType` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `sharedBy` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `sharedByType` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `file_shares` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `line_auth_states` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `line_auth_states` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `line_auth_states` table. All the data in the column will be lost.
  - You are about to drop the column `failReason` on the `login_logs` table. All the data in the column will be lost.
  - You are about to drop the column `ipAddress` on the `login_logs` table. All the data in the column will be lost.
  - You are about to drop the column `loginAt` on the `login_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userAgent` on the `login_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `login_logs` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `message_attachments` table. All the data in the column will be lost.
  - You are about to drop the column `isProcessed` on the `message_attachments` table. All the data in the column will be lost.
  - You are about to drop the column `messageId` on the `message_attachments` table. All the data in the column will be lost.
  - You are about to drop the column `previewUrl` on the `message_attachments` table. All the data in the column will be lost.
  - You are about to drop the column `processingError` on the `message_attachments` table. All the data in the column will be lost.
  - You are about to drop the column `thumbnailUrl` on the `message_attachments` table. All the data in the column will be lost.
  - The `content_type` column on the `message_center_messages` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `createdAt` on the `message_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `messageId` on the `message_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `message_reactions` table. All the data in the column will be lost.
  - You are about to drop the column `conversationId` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `deletedAt` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `editedAt` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `isDeleted` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `isEdited` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `replyToId` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `senderId` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `messages` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `order_histories` table. All the data in the column will be lost.
  - You are about to drop the column `orderId` on the `order_histories` table. All the data in the column will be lost.
  - You are about to drop the column `billingCycle` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `endDate` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `numberOfSubscribers` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `planId` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `planName` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `tenantName` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `orders` table. All the data in the column will be lost.
  - You are about to drop the column `user_id` on the `password_reset_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `payments` table. All the data in the column will be lost.
  - You are about to drop the column `orderId` on the `payments` table. All the data in the column will be lost.
  - You are about to drop the column `transactionId` on the `payments` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `payments` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `permission_categories` table. All the data in the column will be lost.
  - You are about to drop the column `isActive` on the `permission_categories` table. All the data in the column will be lost.
  - You are about to drop the column `sortOrder` on the `permission_categories` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `permission_categories` table. All the data in the column will be lost.
  - You are about to drop the column `categoryId` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `isSystemDefined` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `permissions` table. All the data in the column will be lost.
  - You are about to drop the column `albumId` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `projectId` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `photos` table. All the data in the column will be lost.
  - You are about to drop the column `billingCycle` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `isPopular` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `maxProjects` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `maxStorage` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `maxUsers` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `monthlyAiCreditsLimit` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `plans` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `photoUrls` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `progressType` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `progressValue` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `projectId` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `recordedAt` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `taskId` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `progress_entries` table. All the data in the column will be lost.
  - You are about to drop the column `completedTasks` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `completionRate` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `generatedBy` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `inProgressTasks` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `overdueTasks` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `predictedCompletionDate` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `projectId` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `reportDate` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `reportType` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `riskLevel` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `totalTasks` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `progress_reports` table. All the data in the column will be lost.
  - You are about to drop the column `completedAt` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `createdById` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `projectId` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `targetDate` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `project_milestones` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `endDate` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `parentProjectId` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `projects` table. All the data in the column will be lost.
  - You are about to drop the column `systemUserId` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `tenantUserId` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `userType` on the `refresh_tokens` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `role_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `permissionId` on the `role_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `roleId` on the `role_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `role_permissions` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `displayName` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `isSystem` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `parentRoleId` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `roles` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `createdBy` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `updatedBy` on the `settings` table. All the data in the column will be lost.
  - You are about to drop the column `allowComment` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `allowDownload` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `deletedAt` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `entityId` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `entityType` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `fileExtension` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `filePath` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `fileSize` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `fileType` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `fileUrl` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `isDeleted` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `isLatestVersion` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `originalName` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `parentFileId` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `previewPath` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `thumbnailPath` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `uploaderId` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `uploaderType` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `shared_files` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `endDate` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `numberOfSubscribers` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `paymentMethod` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `planId` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `planName` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `tenantName` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `transactionId` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `subscriptions` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `errorMessage` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `targetResource` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `targetResourceId` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `system_logs` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLoginAt` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLoginIp` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLogoutAt` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `mfaEnabled` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `mfaSecret` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `passwordLastChangedAt` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `system_users` table. All the data in the column will be lost.
  - You are about to drop the column `actualHours` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `assigneeId` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `createdById` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `dueDate` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `estimatedHours` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `projectId` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `startDate` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `tasks` table. All the data in the column will be lost.
  - You are about to drop the column `paymentId` on the `tenant_credit_purchases` table. All the data in the column will be lost.
  - You are about to drop the column `pricePaid` on the `tenant_credit_purchases` table. All the data in the column will be lost.
  - You are about to drop the column `purchasedAt` on the `tenant_credit_purchases` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `tenant_credit_purchases` table. All the data in the column will be lost.
  - You are about to drop the column `acceptedAt` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `acceptedById` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `createdById` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `roleId` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `tenant_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `tenant_lifecycle_events` table. All the data in the column will be lost.
  - You are about to drop the column `eventType` on the `tenant_lifecycle_events` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `tenant_lifecycle_events` table. All the data in the column will be lost.
  - You are about to drop the column `triggeredBy` on the `tenant_lifecycle_events` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `dataTransferNote` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `dataTransferStatus` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `invitedBy` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLoginAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLoginIp` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `lastLogoutAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `leftCompanyAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `leftCompanyReason` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `mfaEnabled` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `mfaSecret` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `passwordLastChangedAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `tenant_users` table. All the data in the column will be lost.
  - You are about to drop the column `adminEmail` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `adminName` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `billingCycle` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `companySize` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `contactEmail` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `contactName` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `currentAiCredits` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `maxProjects` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `maxStorage` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `maxUsers` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `nextBillingDate` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `paymentStatus` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `planId` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `tenants` table. All the data in the column will be lost.
  - You are about to drop the column `acceptedAt` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `expiresAt` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `invitedBy` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `workspace_invitations` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `workspace_members` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `workspace_members` table. All the data in the column will be lost.
  - You are about to drop the column `userId` on the `workspace_members` table. All the data in the column will be lost.
  - You are about to drop the column `workspaceId` on the `workspace_members` table. All the data in the column will be lost.
  - The `role` column on the `workspace_members` table would be dropped and recreated. This will lead to data loss if there is data in the column.
  - You are about to drop the column `createdAt` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `createdBy` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `defaultMemberRole` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `defaultSettings` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `isSystem` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `workspace_templates` table. All the data in the column will be lost.
  - You are about to drop the column `createdAt` on the `workspaces` table. All the data in the column will be lost.
  - You are about to drop the column `ownerId` on the `workspaces` table. All the data in the column will be lost.
  - You are about to drop the column `tenantId` on the `workspaces` table. All the data in the column will be lost.
  - You are about to drop the column `updatedAt` on the `workspaces` table. All the data in the column will be lost.
  - A unique constraint covering the columns `[comment_id,user_id,user_type]` on the table `comment_mentions` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[comment_id,user_id,user_type]` on the table `comment_reactions` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[conversation_id,user_id]` on the table `conversation_participants` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[file_id,user_id,user_type]` on the table `file_permissions` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[share_token]` on the table `file_shares` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[message_id,user_id,emoji]` on the table `message_reactions` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[order_id]` on the table `payments` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[project_id,report_type,period]` on the table `progress_reports` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[role_id,permission_id]` on the table `role_permissions` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[email,tenant_id,status]` on the table `tenant_invitations` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[name]` on the table `tenants` will be added. If there are existing duplicate values, this will fail.
  - A unique constraint covering the columns `[workspace_id,tenant_user_id]` on the table `workspace_members` will be added. If there are existing duplicate values, this will fail.
  - Added the required column `tenant_id` to the `albums` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `albums` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `albums` table without a default value. This is not possible if the table is not empty.
  - Added the required column `comment_id` to the `comment_mentions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `comment_mentions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `comment_mentions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_type` to the `comment_mentions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `comment_id` to the `comment_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `comment_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `comment_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_type` to the `comment_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `author_id` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `author_type` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `entity_id` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `entity_type` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `comments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `conversation_id` to the `conversation_participants` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `conversation_participants` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `conversation_participants` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by` to the `conversations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `conversations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `workspace_id` to the `conversations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `access_type` to the `file_access_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_id` to the `file_access_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `ip_address` to the `file_access_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `file_access_logs` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_id` to the `file_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `granted_by` to the `file_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `file_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `file_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_type` to the `file_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_id` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `share_token` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `shared_by` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `shared_by_type` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `file_shares` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expires_at` to the `line_auth_states` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `line_auth_states` table without a default value. This is not possible if the table is not empty.
  - Added the required column `message_id` to the `message_attachments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `message_id` to the `message_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `message_reactions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `conversation_id` to the `messages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `sender_id` to the `messages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `messages` table without a default value. This is not possible if the table is not empty.
  - Added the required column `order_id` to the `order_histories` table without a default value. This is not possible if the table is not empty.
  - Added the required column `end_date` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `number_of_subscribers` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `plan_id` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `plan_name` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `start_date` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_name` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `orders` table without a default value. This is not possible if the table is not empty.
  - Added the required column `order_id` to the `payments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `payments` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `permission_categories` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `album_id` to the `photos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `project_id` to the `photos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `photos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `photos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `photos` table without a default value. This is not possible if the table is not empty.
  - Added the required column `max_projects` to the `plans` table without a default value. This is not possible if the table is not empty.
  - Added the required column `max_storage` to the `plans` table without a default value. This is not possible if the table is not empty.
  - Added the required column `max_users` to the `plans` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `plans` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `progress_entries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `progress_entries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `progress_entries` table without a default value. This is not possible if the table is not empty.
  - Added the required column `generated_by` to the `progress_reports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `progress_reports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `progress_reports` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by_id` to the `project_milestones` table without a default value. This is not possible if the table is not empty.
  - Added the required column `project_id` to the `project_milestones` table without a default value. This is not possible if the table is not empty.
  - Added the required column `target_date` to the `project_milestones` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `project_milestones` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `project_milestones` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `projects` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `projects` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_id` to the `projects` table without a default value. This is not possible if the table is not empty.
  - Added the required column `user_type` to the `refresh_tokens` table without a default value. This is not possible if the table is not empty.
  - Added the required column `permission_id` to the `role_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `role_id` to the `role_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `role_permissions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `display_name` to the `roles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `roles` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `settings` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_extension` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_path` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_size` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `file_type` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `original_name` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uploader_id` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `uploader_type` to the `shared_files` table without a default value. This is not possible if the table is not empty.
  - Added the required column `number_of_subscribers` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `plan_name` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `start_date` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_name` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `subscriptions` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `system_users` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by_id` to the `tasks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `project_id` to the `tasks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `tasks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `tasks` table without a default value. This is not possible if the table is not empty.
  - Added the required column `price_paid` to the `tenant_credit_purchases` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `tenant_credit_purchases` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expires_at` to the `tenant_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `role_id` to the `tenant_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `tenant_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `event_type` to the `tenant_lifecycle_events` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `tenant_lifecycle_events` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `tenant_users` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `tenant_users` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `tenants` table without a default value. This is not possible if the table is not empty.
  - Added the required column `expires_at` to the `workspace_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `invited_by` to the `workspace_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `workspace_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `workspace_id` to the `workspace_invitations` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_user_id` to the `workspace_members` table without a default value. This is not possible if the table is not empty.
  - Added the required column `workspace_id` to the `workspace_members` table without a default value. This is not possible if the table is not empty.
  - Added the required column `created_by` to the `workspace_templates` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `workspace_templates` table without a default value. This is not possible if the table is not empty.
  - Added the required column `owner_id` to the `workspaces` table without a default value. This is not possible if the table is not empty.
  - Added the required column `tenant_id` to the `workspaces` table without a default value. This is not possible if the table is not empty.
  - Added the required column `updated_at` to the `workspaces` table without a default value. This is not possible if the table is not empty.

*/
-- AlterEnum
-- This migration adds more than one value to an enum.
-- With PostgreSQL versions 11 and earlier, this is not possible
-- in a single migration. This can be worked around by creating
-- multiple migrations, each migration adding only one value to
-- the enum.


ALTER TYPE "MessageType" ADD VALUE 'RICH_TEXT';
ALTER TYPE "MessageType" ADD VALUE 'LINK';

-- AlterEnum
BEGIN;
CREATE TYPE "NotificationPriority_new" AS ENUM ('LOW', 'NORMAL', 'HIGH');
ALTER TABLE "message_center_notifications" ALTER COLUMN "priority" DROP DEFAULT;
ALTER TABLE "message_center_notifications" ALTER COLUMN "priority" TYPE "NotificationPriority_new" USING ("priority"::text::"NotificationPriority_new");
ALTER TYPE "NotificationPriority" RENAME TO "NotificationPriority_old";
ALTER TYPE "NotificationPriority_new" RENAME TO "NotificationPriority";
DROP TYPE "NotificationPriority_old";
ALTER TABLE "message_center_notifications" ALTER COLUMN "priority" SET DEFAULT 'NORMAL';
COMMIT;

-- DropForeignKey
ALTER TABLE "albums" DROP CONSTRAINT "albums_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "comment_mentions" DROP CONSTRAINT "comment_mentions_commentId_fkey";

-- DropForeignKey
ALTER TABLE "comment_mentions" DROP CONSTRAINT "comment_mentions_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "comment_mentions" DROP CONSTRAINT "comment_mentions_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "comment_reactions" DROP CONSTRAINT "comment_reactions_commentId_fkey";

-- DropForeignKey
ALTER TABLE "comment_reactions" DROP CONSTRAINT "comment_reactions_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "comment_reactions" DROP CONSTRAINT "comment_reactions_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "comments" DROP CONSTRAINT "comments_parentId_fkey";

-- DropForeignKey
ALTER TABLE "comments" DROP CONSTRAINT "comments_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "comments" DROP CONSTRAINT "comments_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "conversation_participants" DROP CONSTRAINT "conversation_participants_conversationId_fkey";

-- DropForeignKey
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_lastMessageId_fkey";

-- DropForeignKey
ALTER TABLE "conversations" DROP CONSTRAINT "conversations_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "file_access_logs" DROP CONSTRAINT "file_access_logs_fileId_fkey";

-- DropForeignKey
ALTER TABLE "file_access_logs" DROP CONSTRAINT "file_access_logs_shareId_fkey";

-- DropForeignKey
ALTER TABLE "file_access_logs" DROP CONSTRAINT "file_access_logs_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "file_permissions" DROP CONSTRAINT "file_permissions_fileId_fkey";

-- DropForeignKey
ALTER TABLE "file_permissions" DROP CONSTRAINT "file_permissions_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "file_shares" DROP CONSTRAINT "file_shares_fileId_fkey";

-- DropForeignKey
ALTER TABLE "file_shares" DROP CONSTRAINT "file_shares_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "message_attachments" DROP CONSTRAINT "message_attachments_messageId_fkey";

-- DropForeignKey
ALTER TABLE "message_reactions" DROP CONSTRAINT "message_reactions_messageId_fkey";

-- DropForeignKey
ALTER TABLE "messages" DROP CONSTRAINT "messages_conversationId_fkey";

-- DropForeignKey
ALTER TABLE "messages" DROP CONSTRAINT "messages_replyToId_fkey";

-- DropForeignKey
ALTER TABLE "order_histories" DROP CONSTRAINT "order_histories_orderId_fkey";

-- DropForeignKey
ALTER TABLE "orders" DROP CONSTRAINT "orders_planId_fkey";

-- DropForeignKey
ALTER TABLE "orders" DROP CONSTRAINT "orders_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "payments" DROP CONSTRAINT "payments_orderId_fkey";

-- DropForeignKey
ALTER TABLE "permissions" DROP CONSTRAINT "permissions_categoryId_fkey";

-- DropForeignKey
ALTER TABLE "photos" DROP CONSTRAINT "photos_albumId_fkey";

-- DropForeignKey
ALTER TABLE "photos" DROP CONSTRAINT "photos_projectId_fkey";

-- DropForeignKey
ALTER TABLE "photos" DROP CONSTRAINT "photos_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "progress_entries" DROP CONSTRAINT "progress_entries_projectId_fkey";

-- DropForeignKey
ALTER TABLE "progress_entries" DROP CONSTRAINT "progress_entries_taskId_fkey";

-- DropForeignKey
ALTER TABLE "progress_entries" DROP CONSTRAINT "progress_entries_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "progress_reports" DROP CONSTRAINT "progress_reports_projectId_fkey";

-- DropForeignKey
ALTER TABLE "progress_reports" DROP CONSTRAINT "progress_reports_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "project_milestones" DROP CONSTRAINT "project_milestones_projectId_fkey";

-- DropForeignKey
ALTER TABLE "project_milestones" DROP CONSTRAINT "project_milestones_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_parentProjectId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "projects" DROP CONSTRAINT "projects_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "refresh_tokens" DROP CONSTRAINT "refresh_tokens_systemUserId_fkey";

-- DropForeignKey
ALTER TABLE "refresh_tokens" DROP CONSTRAINT "refresh_tokens_tenantUserId_fkey";

-- DropForeignKey
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permissions_permissionId_fkey";

-- DropForeignKey
ALTER TABLE "role_permissions" DROP CONSTRAINT "role_permissions_roleId_fkey";

-- DropForeignKey
ALTER TABLE "roles" DROP CONSTRAINT "roles_parentRoleId_fkey";

-- DropForeignKey
ALTER TABLE "roles" DROP CONSTRAINT "roles_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "shared_files" DROP CONSTRAINT "shared_files_parentFileId_fkey";

-- DropForeignKey
ALTER TABLE "shared_files" DROP CONSTRAINT "shared_files_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "shared_files" DROP CONSTRAINT "shared_files_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "subscriptions" DROP CONSTRAINT "subscriptions_planId_fkey";

-- DropForeignKey
ALTER TABLE "subscriptions" DROP CONSTRAINT "subscriptions_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_projectId_fkey";

-- DropForeignKey
ALTER TABLE "tasks" DROP CONSTRAINT "tasks_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tenant_credit_purchases" DROP CONSTRAINT "tenant_credit_purchases_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tenant_invitations" DROP CONSTRAINT "tenant_invitations_roleId_fkey";

-- DropForeignKey
ALTER TABLE "tenant_invitations" DROP CONSTRAINT "tenant_invitations_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tenant_lifecycle_events" DROP CONSTRAINT "tenant_lifecycle_events_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tenant_users" DROP CONSTRAINT "tenant_users_invitedBy_fkey";

-- DropForeignKey
ALTER TABLE "tenant_users" DROP CONSTRAINT "tenant_users_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "tenants" DROP CONSTRAINT "tenants_planId_fkey";

-- DropForeignKey
ALTER TABLE "workspace_invitations" DROP CONSTRAINT "workspace_invitations_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "workspace_members" DROP CONSTRAINT "workspace_members_userId_fkey";

-- DropForeignKey
ALTER TABLE "workspace_members" DROP CONSTRAINT "workspace_members_workspaceId_fkey";

-- DropForeignKey
ALTER TABLE "workspace_templates" DROP CONSTRAINT "workspace_templates_tenantId_fkey";

-- DropForeignKey
ALTER TABLE "workspaces" DROP CONSTRAINT "workspaces_tenantId_fkey";

-- DropIndex
DROP INDEX "ai_workflows_created_by_idx";

-- DropIndex
DROP INDEX "albums_tenantId_idx";

-- DropIndex
DROP INDEX "albums_userId_idx";

-- DropIndex
DROP INDEX "comment_mentions_commentId_idx";

-- DropIndex
DROP INDEX "comment_mentions_commentId_userId_userType_key";

-- DropIndex
DROP INDEX "comment_mentions_tenantId_idx";

-- DropIndex
DROP INDEX "comment_mentions_userId_idx";

-- DropIndex
DROP INDEX "comment_mentions_workspaceId_idx";

-- DropIndex
DROP INDEX "comment_reactions_commentId_idx";

-- DropIndex
DROP INDEX "comment_reactions_commentId_userId_userType_key";

-- DropIndex
DROP INDEX "comment_reactions_tenantId_idx";

-- DropIndex
DROP INDEX "comment_reactions_userId_idx";

-- DropIndex
DROP INDEX "comment_reactions_workspaceId_idx";

-- DropIndex
DROP INDEX "comments_authorId_idx";

-- DropIndex
DROP INDEX "comments_createdAt_idx";

-- DropIndex
DROP INDEX "comments_entityType_entityId_idx";

-- DropIndex
DROP INDEX "comments_parentId_idx";

-- DropIndex
DROP INDEX "comments_tenantId_idx";

-- DropIndex
DROP INDEX "comments_threadId_idx";

-- DropIndex
DROP INDEX "comments_workspaceId_idx";

-- DropIndex
DROP INDEX "conversation_participants_conversationId_idx";

-- DropIndex
DROP INDEX "conversation_participants_conversationId_userId_key";

-- DropIndex
DROP INDEX "conversation_participants_userId_idx";

-- DropIndex
DROP INDEX "conversations_createdBy_idx";

-- DropIndex
DROP INDEX "conversations_lastActivityAt_idx";

-- DropIndex
DROP INDEX "conversations_workspaceId_idx";

-- DropIndex
DROP INDEX "file_access_logs_accessType_idx";

-- DropIndex
DROP INDEX "file_access_logs_accessedAt_idx";

-- DropIndex
DROP INDEX "file_access_logs_fileId_idx";

-- DropIndex
DROP INDEX "file_access_logs_shareId_idx";

-- DropIndex
DROP INDEX "file_access_logs_tenantId_idx";

-- DropIndex
DROP INDEX "file_access_logs_userId_userType_idx";

-- DropIndex
DROP INDEX "file_permissions_fileId_idx";

-- DropIndex
DROP INDEX "file_permissions_fileId_userId_userType_key";

-- DropIndex
DROP INDEX "file_permissions_tenantId_idx";

-- DropIndex
DROP INDEX "file_permissions_userId_userType_idx";

-- DropIndex
DROP INDEX "file_shares_expiresAt_idx";

-- DropIndex
DROP INDEX "file_shares_fileId_idx";

-- DropIndex
DROP INDEX "file_shares_shareToken_idx";

-- DropIndex
DROP INDEX "file_shares_shareToken_key";

-- DropIndex
DROP INDEX "file_shares_tenantId_idx";

-- DropIndex
DROP INDEX "line_auth_states_userId_idx";

-- DropIndex
DROP INDEX "line_message_logs_message_id_idx";

-- DropIndex
DROP INDEX "message_attachments_messageId_idx";

-- DropIndex
DROP INDEX "message_conversations_createdBy_idx";

-- DropIndex
DROP INDEX "message_reactions_messageId_idx";

-- DropIndex
DROP INDEX "message_reactions_messageId_userId_emoji_key";

-- DropIndex
DROP INDEX "message_reactions_userId_idx";

-- DropIndex
DROP INDEX "messages_conversationId_idx";

-- DropIndex
DROP INDEX "messages_createdAt_idx";

-- DropIndex
DROP INDEX "messages_senderId_idx";

-- DropIndex
DROP INDEX "password_reset_tokens_user_id_idx";

-- DropIndex
DROP INDEX "payments_orderId_key";

-- DropIndex
DROP INDEX "photos_albumId_idx";

-- DropIndex
DROP INDEX "photos_projectId_idx";

-- DropIndex
DROP INDEX "photos_tenantId_idx";

-- DropIndex
DROP INDEX "photos_userId_idx";

-- DropIndex
DROP INDEX "progress_entries_projectId_idx";

-- DropIndex
DROP INDEX "progress_entries_recordedAt_idx";

-- DropIndex
DROP INDEX "progress_entries_taskId_idx";

-- DropIndex
DROP INDEX "progress_entries_tenantId_idx";

-- DropIndex
DROP INDEX "progress_entries_userId_idx";

-- DropIndex
DROP INDEX "progress_reports_projectId_idx";

-- DropIndex
DROP INDEX "progress_reports_projectId_reportType_period_key";

-- DropIndex
DROP INDEX "progress_reports_reportDate_idx";

-- DropIndex
DROP INDEX "progress_reports_tenantId_idx";

-- DropIndex
DROP INDEX "project_milestones_projectId_idx";

-- DropIndex
DROP INDEX "project_milestones_targetDate_idx";

-- DropIndex
DROP INDEX "project_milestones_tenantId_idx";

-- DropIndex
DROP INDEX "projects_parentProjectId_idx";

-- DropIndex
DROP INDEX "projects_tenantId_idx";

-- DropIndex
DROP INDEX "projects_userId_idx";

-- DropIndex
DROP INDEX "projects_workspaceId_idx";

-- DropIndex
DROP INDEX "refresh_tokens_systemUserId_idx";

-- DropIndex
DROP INDEX "refresh_tokens_tenantUserId_idx";

-- DropIndex
DROP INDEX "refresh_tokens_userType_idx";

-- DropIndex
DROP INDEX "role_permissions_roleId_permissionId_key";

-- DropIndex
DROP INDEX "settings_createdBy_idx";

-- DropIndex
DROP INDEX "settings_updatedBy_idx";

-- DropIndex
DROP INDEX "shared_files_createdAt_idx";

-- DropIndex
DROP INDEX "shared_files_entityType_entityId_idx";

-- DropIndex
DROP INDEX "shared_files_parentFileId_idx";

-- DropIndex
DROP INDEX "shared_files_tenantId_idx";

-- DropIndex
DROP INDEX "shared_files_uploaderId_uploaderType_idx";

-- DropIndex
DROP INDEX "shared_files_workspaceId_idx";

-- DropIndex
DROP INDEX "subscriptions_planId_idx";

-- DropIndex
DROP INDEX "subscriptions_tenantId_idx";

-- DropIndex
DROP INDEX "tasks_assigneeId_idx";

-- DropIndex
DROP INDEX "tasks_projectId_idx";

-- DropIndex
DROP INDEX "tasks_tenantId_idx";

-- DropIndex
DROP INDEX "tenant_credit_purchases_tenantId_idx";

-- DropIndex
DROP INDEX "tenant_invitations_email_tenantId_status_key";

-- DropIndex
DROP INDEX "tenant_invitations_roleId_idx";

-- DropIndex
DROP INDEX "tenant_invitations_tenantId_idx";

-- DropIndex
DROP INDEX "tenant_lifecycle_events_createdAt_idx";

-- DropIndex
DROP INDEX "tenant_lifecycle_events_eventType_idx";

-- DropIndex
DROP INDEX "tenant_lifecycle_events_tenantId_idx";

-- DropIndex
DROP INDEX "tenant_users_tenantId_idx";

-- DropIndex
DROP INDEX "workspace_invitations_workspaceId_idx";

-- DropIndex
DROP INDEX "workspace_members_userId_idx";

-- DropIndex
DROP INDEX "workspace_members_workspaceId_idx";

-- DropIndex
DROP INDEX "workspace_members_workspaceId_userId_key";

-- DropIndex
DROP INDEX "workspace_templates_isSystem_idx";

-- DropIndex
DROP INDEX "workspace_templates_tenantId_idx";

-- DropIndex
DROP INDEX "workspaces_ownerId_idx";

-- DropIndex
DROP INDEX "workspaces_tenantId_idx";

-- AlterTable
ALTER TABLE "ai_workflows" DROP COLUMN "created_by";

-- AlterTable
ALTER TABLE "albums" DROP COLUMN "createdAt",
DROP COLUMN "photosCount",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "photos_count" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "comment_mentions" DROP COLUMN "commentId",
DROP COLUMN "createdAt",
DROP COLUMN "tenantId",
DROP COLUMN "userId",
DROP COLUMN "userType",
DROP COLUMN "workspaceId",
ADD COLUMN     "comment_id" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL,
ADD COLUMN     "user_type" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT;

-- AlterTable
ALTER TABLE "comment_reactions" DROP COLUMN "commentId",
DROP COLUMN "createdAt",
DROP COLUMN "tenantId",
DROP COLUMN "userId",
DROP COLUMN "userType",
DROP COLUMN "workspaceId",
ADD COLUMN     "comment_id" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL,
ADD COLUMN     "user_type" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT;

-- AlterTable
ALTER TABLE "comments" DROP COLUMN "authorId",
DROP COLUMN "authorType",
DROP COLUMN "contentType",
DROP COLUMN "createdAt",
DROP COLUMN "deletedAt",
DROP COLUMN "entityId",
DROP COLUMN "entityType",
DROP COLUMN "isDeleted",
DROP COLUMN "isEdited",
DROP COLUMN "parentId",
DROP COLUMN "tenantId",
DROP COLUMN "threadId",
DROP COLUMN "updatedAt",
DROP COLUMN "workspaceId",
ADD COLUMN     "author_id" TEXT NOT NULL,
ADD COLUMN     "author_type" TEXT NOT NULL,
ADD COLUMN     "content_type" "CommentContentType" NOT NULL DEFAULT 'TEXT',
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "entity_id" TEXT NOT NULL,
ADD COLUMN     "entity_type" "CommentEntityType" NOT NULL,
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_edited" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "parent_id" TEXT,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "thread_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "workspace_id" TEXT;

-- AlterTable
ALTER TABLE "conversation_participants" DROP COLUMN "allowNotifications",
DROP COLUMN "conversationId",
DROP COLUMN "isMuted",
DROP COLUMN "isPinned",
DROP COLUMN "joinedAt",
DROP COLUMN "lastReadAt",
DROP COLUMN "lastReadMessageId",
DROP COLUMN "lastSeenAt",
DROP COLUMN "leftAt",
DROP COLUMN "mutedUntil",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "allow_notifications" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "conversation_id" TEXT NOT NULL,
ADD COLUMN     "is_muted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_pinned" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "last_read_at" TIMESTAMP(3),
ADD COLUMN     "last_read_message_id" TEXT,
ADD COLUMN     "last_seen_at" TIMESTAMP(3),
ADD COLUMN     "left_at" TIMESTAMP(3),
ADD COLUMN     "muted_until" TIMESTAMP(3),
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "conversations" DROP COLUMN "allowAudioSharing",
DROP COLUMN "allowFileSharing",
DROP COLUMN "allowImageSharing",
DROP COLUMN "allowVideoSharing",
DROP COLUMN "createdAt",
DROP COLUMN "createdBy",
DROP COLUMN "isArchived",
DROP COLUMN "isEncrypted",
DROP COLUMN "isPrivate",
DROP COLUMN "lastActivityAt",
DROP COLUMN "lastMessageId",
DROP COLUMN "maxFileSize",
DROP COLUMN "requireApprovalForNewMembers",
DROP COLUMN "retentionDays",
DROP COLUMN "updatedAt",
DROP COLUMN "workspaceId",
ADD COLUMN     "allow_audio_sharing" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "allow_file_sharing" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "allow_image_sharing" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "allow_video_sharing" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by" TEXT NOT NULL,
ADD COLUMN     "is_archived" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_encrypted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_private" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "last_activity_at" TIMESTAMP(3),
ADD COLUMN     "last_message_id" TEXT,
ADD COLUMN     "max_file_size" INTEGER NOT NULL DEFAULT 10485760,
ADD COLUMN     "require_approval_for_new_members" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "retention_days" INTEGER,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "workspace_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "file_access_logs" DROP COLUMN "accessType",
DROP COLUMN "accessedAt",
DROP COLUMN "fileId",
DROP COLUMN "ipAddress",
DROP COLUMN "shareId",
DROP COLUMN "tenantId",
DROP COLUMN "userAgent",
DROP COLUMN "userId",
DROP COLUMN "userType",
ADD COLUMN     "access_type" "AccessType" NOT NULL,
ADD COLUMN     "accessed_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "file_id" TEXT NOT NULL,
ADD COLUMN     "ip_address" TEXT NOT NULL,
ADD COLUMN     "share_id" TEXT,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "user_agent" TEXT,
ADD COLUMN     "user_id" TEXT,
ADD COLUMN     "user_type" TEXT;

-- AlterTable
ALTER TABLE "file_permissions" DROP COLUMN "expiresAt",
DROP COLUMN "fileId",
DROP COLUMN "grantedAt",
DROP COLUMN "grantedBy",
DROP COLUMN "tenantId",
DROP COLUMN "userId",
DROP COLUMN "userType",
ADD COLUMN     "expires_at" TIMESTAMP(3),
ADD COLUMN     "file_id" TEXT NOT NULL,
ADD COLUMN     "granted_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "granted_by" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL,
ADD COLUMN     "user_type" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "file_shares" DROP COLUMN "allowComment",
DROP COLUMN "allowDownload",
DROP COLUMN "createdAt",
DROP COLUMN "currentDownloads",
DROP COLUMN "expiresAt",
DROP COLUMN "fileId",
DROP COLUMN "isActive",
DROP COLUMN "maxDownloads",
DROP COLUMN "requireAuth",
DROP COLUMN "shareToken",
DROP COLUMN "shareType",
DROP COLUMN "sharedBy",
DROP COLUMN "sharedByType",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "allow_comment" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "allow_download" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "current_downloads" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "expires_at" TIMESTAMP(3),
ADD COLUMN     "file_id" TEXT NOT NULL,
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "max_downloads" INTEGER,
ADD COLUMN     "require_auth" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "share_token" TEXT NOT NULL,
ADD COLUMN     "share_type" "ShareType" NOT NULL DEFAULT 'LINK',
ADD COLUMN     "shared_by" TEXT NOT NULL,
ADD COLUMN     "shared_by_type" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "line_auth_states" DROP COLUMN "createdAt",
DROP COLUMN "expiresAt",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "expires_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "login_logs" DROP COLUMN "failReason",
DROP COLUMN "ipAddress",
DROP COLUMN "loginAt",
DROP COLUMN "userAgent",
DROP COLUMN "userId",
ADD COLUMN     "fail_reason" TEXT,
ADD COLUMN     "ip_address" TEXT,
ADD COLUMN     "login_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "user_agent" TEXT,
ADD COLUMN     "user_id" TEXT;

-- AlterTable
ALTER TABLE "message_attachments" DROP COLUMN "createdAt",
DROP COLUMN "isProcessed",
DROP COLUMN "messageId",
DROP COLUMN "previewUrl",
DROP COLUMN "processingError",
DROP COLUMN "thumbnailUrl",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "is_processed" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "message_id" TEXT NOT NULL,
ADD COLUMN     "preview_url" TEXT,
ADD COLUMN     "processing_error" TEXT,
ADD COLUMN     "thumbnail_url" TEXT;

-- AlterTable
ALTER TABLE "message_center_messages" DROP COLUMN "content_type",
ADD COLUMN     "content_type" "MessageType" NOT NULL DEFAULT 'TEXT';

-- AlterTable
ALTER TABLE "message_reactions" DROP COLUMN "createdAt",
DROP COLUMN "messageId",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "message_id" TEXT NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "messages" DROP COLUMN "conversationId",
DROP COLUMN "createdAt",
DROP COLUMN "deletedAt",
DROP COLUMN "editedAt",
DROP COLUMN "isDeleted",
DROP COLUMN "isEdited",
DROP COLUMN "replyToId",
DROP COLUMN "senderId",
DROP COLUMN "updatedAt",
ADD COLUMN     "conversation_id" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "edited_at" TIMESTAMP(3),
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_edited" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "reply_to_id" TEXT,
ADD COLUMN     "sender_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "order_histories" DROP COLUMN "createdAt",
DROP COLUMN "orderId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "order_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "orders" DROP COLUMN "billingCycle",
DROP COLUMN "createdAt",
DROP COLUMN "endDate",
DROP COLUMN "numberOfSubscribers",
DROP COLUMN "planId",
DROP COLUMN "planName",
DROP COLUMN "startDate",
DROP COLUMN "tenantId",
DROP COLUMN "tenantName",
DROP COLUMN "updatedAt",
ADD COLUMN     "billing_cycle" TEXT NOT NULL DEFAULT 'monthly',
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "end_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "number_of_subscribers" INTEGER NOT NULL,
ADD COLUMN     "plan_id" TEXT NOT NULL,
ADD COLUMN     "plan_name" TEXT NOT NULL,
ADD COLUMN     "start_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "tenant_name" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "password_reset_tokens" DROP COLUMN "user_id",
ADD COLUMN     "system_user_id" TEXT,
ADD COLUMN     "tenant_user_id" TEXT;

-- AlterTable
ALTER TABLE "payments" DROP COLUMN "createdAt",
DROP COLUMN "orderId",
DROP COLUMN "transactionId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "order_id" TEXT NOT NULL,
ADD COLUMN     "transaction_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "permission_categories" DROP COLUMN "createdAt",
DROP COLUMN "isActive",
DROP COLUMN "sortOrder",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "is_active" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "sort_order" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "permissions" DROP COLUMN "categoryId",
DROP COLUMN "createdAt",
DROP COLUMN "isSystemDefined",
DROP COLUMN "updatedAt",
ADD COLUMN     "category_id" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "is_system_defined" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "photos" DROP COLUMN "albumId",
DROP COLUMN "createdAt",
DROP COLUMN "projectId",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "album_id" TEXT NOT NULL,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "project_id" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "plans" DROP COLUMN "billingCycle",
DROP COLUMN "createdAt",
DROP COLUMN "isPopular",
DROP COLUMN "maxProjects",
DROP COLUMN "maxStorage",
DROP COLUMN "maxUsers",
DROP COLUMN "monthlyAiCreditsLimit",
DROP COLUMN "updatedAt",
ADD COLUMN     "billing_cycle" TEXT NOT NULL DEFAULT 'monthly',
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "is_popular" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "max_projects" INTEGER NOT NULL,
ADD COLUMN     "max_storage" INTEGER NOT NULL,
ADD COLUMN     "max_users" INTEGER NOT NULL,
ADD COLUMN     "monthly_ai_credits_limit" DECIMAL(65,30) DEFAULT 0,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "progress_entries" DROP COLUMN "createdAt",
DROP COLUMN "photoUrls",
DROP COLUMN "progressType",
DROP COLUMN "progressValue",
DROP COLUMN "projectId",
DROP COLUMN "recordedAt",
DROP COLUMN "taskId",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "photo_urls" TEXT[],
ADD COLUMN     "progress_type" "ProgressType" NOT NULL DEFAULT 'TASK_UPDATE',
ADD COLUMN     "progress_value" DOUBLE PRECISION,
ADD COLUMN     "project_id" TEXT,
ADD COLUMN     "recorded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "task_id" TEXT,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "progress_reports" DROP COLUMN "completedTasks",
DROP COLUMN "completionRate",
DROP COLUMN "createdAt",
DROP COLUMN "generatedBy",
DROP COLUMN "inProgressTasks",
DROP COLUMN "overdueTasks",
DROP COLUMN "predictedCompletionDate",
DROP COLUMN "projectId",
DROP COLUMN "reportDate",
DROP COLUMN "reportType",
DROP COLUMN "riskLevel",
DROP COLUMN "tenantId",
DROP COLUMN "totalTasks",
DROP COLUMN "updatedAt",
ADD COLUMN     "completed_tasks" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "completion_rate" DOUBLE PRECISION NOT NULL DEFAULT 0,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "generated_by" TEXT NOT NULL,
ADD COLUMN     "in_progress_tasks" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "overdue_tasks" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "predicted_completion_date" TIMESTAMP(3),
ADD COLUMN     "project_id" TEXT,
ADD COLUMN     "report_date" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "report_type" "ReportType" NOT NULL DEFAULT 'WEEKLY',
ADD COLUMN     "risk_level" TEXT,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "total_tasks" INTEGER NOT NULL DEFAULT 0,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "project_milestones" DROP COLUMN "completedAt",
DROP COLUMN "createdAt",
DROP COLUMN "createdById",
DROP COLUMN "projectId",
DROP COLUMN "targetDate",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "completed_at" TIMESTAMP(3),
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by_id" TEXT NOT NULL,
ADD COLUMN     "project_id" TEXT NOT NULL,
ADD COLUMN     "target_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "projects" DROP COLUMN "createdAt",
DROP COLUMN "endDate",
DROP COLUMN "parentProjectId",
DROP COLUMN "startDate",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
DROP COLUMN "workspaceId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "end_date" TIMESTAMP(3),
ADD COLUMN     "parent_project_id" TEXT,
ADD COLUMN     "start_date" TIMESTAMP(3),
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "user_id" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT;

-- AlterTable
ALTER TABLE "refresh_tokens" DROP COLUMN "systemUserId",
DROP COLUMN "tenantUserId",
DROP COLUMN "userType",
ADD COLUMN     "system_user_id" TEXT,
ADD COLUMN     "tenant_user_id" TEXT,
ADD COLUMN     "user_type" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "role_permissions" DROP COLUMN "createdAt",
DROP COLUMN "permissionId",
DROP COLUMN "roleId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "permission_id" TEXT NOT NULL,
ADD COLUMN     "role_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "roles" DROP COLUMN "createdAt",
DROP COLUMN "displayName",
DROP COLUMN "isSystem",
DROP COLUMN "parentRoleId",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "display_name" TEXT NOT NULL,
ADD COLUMN     "is_system" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "parent_role_id" TEXT,
ADD COLUMN     "tenant_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "settings" DROP COLUMN "createdAt",
DROP COLUMN "createdBy",
DROP COLUMN "updatedAt",
DROP COLUMN "updatedBy",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "updated_by" TEXT;

-- AlterTable
ALTER TABLE "shared_files" DROP COLUMN "allowComment",
DROP COLUMN "allowDownload",
DROP COLUMN "createdAt",
DROP COLUMN "deletedAt",
DROP COLUMN "entityId",
DROP COLUMN "entityType",
DROP COLUMN "expiresAt",
DROP COLUMN "fileExtension",
DROP COLUMN "filePath",
DROP COLUMN "fileSize",
DROP COLUMN "fileType",
DROP COLUMN "fileUrl",
DROP COLUMN "isDeleted",
DROP COLUMN "isLatestVersion",
DROP COLUMN "originalName",
DROP COLUMN "parentFileId",
DROP COLUMN "previewPath",
DROP COLUMN "tenantId",
DROP COLUMN "thumbnailPath",
DROP COLUMN "updatedAt",
DROP COLUMN "uploaderId",
DROP COLUMN "uploaderType",
DROP COLUMN "workspaceId",
ADD COLUMN     "allow_comment" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "allow_download" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "deleted_at" TIMESTAMP(3),
ADD COLUMN     "entity_id" TEXT,
ADD COLUMN     "entity_type" "FileEntityType",
ADD COLUMN     "expires_at" TIMESTAMP(3),
ADD COLUMN     "file_extension" TEXT NOT NULL,
ADD COLUMN     "file_path" TEXT NOT NULL,
ADD COLUMN     "file_size" INTEGER NOT NULL,
ADD COLUMN     "file_type" TEXT NOT NULL,
ADD COLUMN     "file_url" TEXT,
ADD COLUMN     "is_deleted" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "is_latest_version" BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN     "original_name" TEXT NOT NULL,
ADD COLUMN     "parent_file_id" TEXT,
ADD COLUMN     "preview_path" TEXT,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "thumbnail_path" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "uploader_id" TEXT NOT NULL,
ADD COLUMN     "uploader_type" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT;

-- AlterTable
ALTER TABLE "subscriptions" DROP COLUMN "createdAt",
DROP COLUMN "endDate",
DROP COLUMN "numberOfSubscribers",
DROP COLUMN "paymentMethod",
DROP COLUMN "planId",
DROP COLUMN "planName",
DROP COLUMN "startDate",
DROP COLUMN "tenantId",
DROP COLUMN "tenantName",
DROP COLUMN "transactionId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "end_date" TIMESTAMP(3),
ADD COLUMN     "number_of_subscribers" INTEGER NOT NULL,
ADD COLUMN     "payment_method" TEXT,
ADD COLUMN     "plan_id" TEXT,
ADD COLUMN     "plan_name" TEXT NOT NULL,
ADD COLUMN     "start_date" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "tenant_id" TEXT,
ADD COLUMN     "tenant_name" TEXT NOT NULL,
ADD COLUMN     "transaction_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "system_logs" DROP COLUMN "createdAt",
DROP COLUMN "errorMessage",
DROP COLUMN "targetResource",
DROP COLUMN "targetResourceId",
DROP COLUMN "tenantId",
DROP COLUMN "userId",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "error_message" TEXT,
ADD COLUMN     "target_resource" TEXT,
ADD COLUMN     "target_resource_id" TEXT,
ADD COLUMN     "tenant_id" TEXT,
ADD COLUMN     "user_id" TEXT;

-- AlterTable
ALTER TABLE "system_users" DROP COLUMN "createdAt",
DROP COLUMN "lastLoginAt",
DROP COLUMN "lastLoginIp",
DROP COLUMN "lastLogoutAt",
DROP COLUMN "mfaEnabled",
DROP COLUMN "mfaSecret",
DROP COLUMN "passwordLastChangedAt",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "last_login_at" TIMESTAMP(3),
ADD COLUMN     "last_login_ip" TEXT,
ADD COLUMN     "last_logout_at" TIMESTAMP(3),
ADD COLUMN     "mfa_enabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "mfa_secret" TEXT,
ADD COLUMN     "password_last_changed_at" TIMESTAMP(3),
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "tasks" DROP COLUMN "actualHours",
DROP COLUMN "assigneeId",
DROP COLUMN "createdAt",
DROP COLUMN "createdById",
DROP COLUMN "dueDate",
DROP COLUMN "estimatedHours",
DROP COLUMN "projectId",
DROP COLUMN "startDate",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "actual_hours" DOUBLE PRECISION,
ADD COLUMN     "assignee_id" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by_id" TEXT NOT NULL,
ADD COLUMN     "due_date" TIMESTAMP(3),
ADD COLUMN     "estimated_hours" DOUBLE PRECISION,
ADD COLUMN     "project_id" TEXT NOT NULL,
ADD COLUMN     "start_date" TIMESTAMP(3),
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "tenant_credit_purchases" DROP COLUMN "paymentId",
DROP COLUMN "pricePaid",
DROP COLUMN "purchasedAt",
DROP COLUMN "tenantId",
ADD COLUMN     "payment_id" TEXT,
ADD COLUMN     "price_paid" DECIMAL(65,30) NOT NULL,
ADD COLUMN     "purchased_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "tenant_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "tenant_invitations" DROP COLUMN "acceptedAt",
DROP COLUMN "acceptedById",
DROP COLUMN "createdAt",
DROP COLUMN "createdById",
DROP COLUMN "expiresAt",
DROP COLUMN "roleId",
DROP COLUMN "tenantId",
ADD COLUMN     "accepted_at" TIMESTAMP(3),
ADD COLUMN     "accepted_by_id" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by_id" TEXT,
ADD COLUMN     "expires_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "role_id" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "tenant_lifecycle_events" DROP COLUMN "createdAt",
DROP COLUMN "eventType",
DROP COLUMN "tenantId",
DROP COLUMN "triggeredBy",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "event_type" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "triggered_by" TEXT;

-- AlterTable
ALTER TABLE "tenant_users" DROP COLUMN "createdAt",
DROP COLUMN "dataTransferNote",
DROP COLUMN "dataTransferStatus",
DROP COLUMN "invitedBy",
DROP COLUMN "lastLoginAt",
DROP COLUMN "lastLoginIp",
DROP COLUMN "lastLogoutAt",
DROP COLUMN "leftCompanyAt",
DROP COLUMN "leftCompanyReason",
DROP COLUMN "mfaEnabled",
DROP COLUMN "mfaSecret",
DROP COLUMN "passwordLastChangedAt",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "data_transfer_note" TEXT,
ADD COLUMN     "data_transfer_status" TEXT DEFAULT 'pending',
ADD COLUMN     "invited_by" TEXT,
ADD COLUMN     "last_login_at" TIMESTAMP(3),
ADD COLUMN     "last_login_ip" TEXT,
ADD COLUMN     "last_logout_at" TIMESTAMP(3),
ADD COLUMN     "left_company_at" TIMESTAMP(3),
ADD COLUMN     "left_company_reason" TEXT,
ADD COLUMN     "mfa_enabled" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "mfa_secret" TEXT,
ADD COLUMN     "password_last_changed_at" TIMESTAMP(3),
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "tenants" DROP COLUMN "adminEmail",
DROP COLUMN "adminName",
DROP COLUMN "billingCycle",
DROP COLUMN "companySize",
DROP COLUMN "contactEmail",
DROP COLUMN "contactName",
DROP COLUMN "createdAt",
DROP COLUMN "currentAiCredits",
DROP COLUMN "maxProjects",
DROP COLUMN "maxStorage",
DROP COLUMN "maxUsers",
DROP COLUMN "nextBillingDate",
DROP COLUMN "paymentStatus",
DROP COLUMN "planId",
DROP COLUMN "updatedAt",
ADD COLUMN     "admin_email" TEXT,
ADD COLUMN     "admin_name" TEXT,
ADD COLUMN     "billing_cycle" TEXT DEFAULT 'monthly',
ADD COLUMN     "company_size" TEXT DEFAULT '1-10',
ADD COLUMN     "contact_email" TEXT,
ADD COLUMN     "contact_name" TEXT,
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "current_ai_credits" DECIMAL(65,30) DEFAULT 0,
ADD COLUMN     "display_name" TEXT,
ADD COLUMN     "is_default" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "max_projects" INTEGER DEFAULT 10,
ADD COLUMN     "max_storage" INTEGER DEFAULT 10,
ADD COLUMN     "max_users" INTEGER DEFAULT 5,
ADD COLUMN     "next_billing_date" TIMESTAMP(3),
ADD COLUMN     "payment_status" TEXT DEFAULT 'unpaid',
ADD COLUMN     "plan_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "workspace_invitations" DROP COLUMN "acceptedAt",
DROP COLUMN "createdAt",
DROP COLUMN "expiresAt",
DROP COLUMN "invitedBy",
DROP COLUMN "updatedAt",
DROP COLUMN "workspaceId",
ADD COLUMN     "accepted_at" TIMESTAMP(3),
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "expires_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "invited_by" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL,
ADD COLUMN     "workspace_id" TEXT NOT NULL;

-- AlterTable
ALTER TABLE "workspace_members" DROP COLUMN "createdAt",
DROP COLUMN "updatedAt",
DROP COLUMN "userId",
DROP COLUMN "workspaceId",
ADD COLUMN     "joined_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "left_at" TIMESTAMP(3),
ADD COLUMN     "tenant_user_id" TEXT NOT NULL,
ADD COLUMN     "workspace_id" TEXT NOT NULL,
DROP COLUMN "role",
ADD COLUMN     "role" "ParticipantRole" NOT NULL DEFAULT 'MEMBER';

-- AlterTable
ALTER TABLE "workspace_templates" DROP COLUMN "createdAt",
DROP COLUMN "createdBy",
DROP COLUMN "defaultMemberRole",
DROP COLUMN "defaultSettings",
DROP COLUMN "isSystem",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "created_by" TEXT NOT NULL,
ADD COLUMN     "default_member_role" TEXT NOT NULL DEFAULT 'member',
ADD COLUMN     "default_settings" JSONB,
ADD COLUMN     "is_system" BOOLEAN NOT NULL DEFAULT false,
ADD COLUMN     "tenant_id" TEXT,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- AlterTable
ALTER TABLE "workspaces" DROP COLUMN "createdAt",
DROP COLUMN "ownerId",
DROP COLUMN "tenantId",
DROP COLUMN "updatedAt",
ADD COLUMN     "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
ADD COLUMN     "owner_id" TEXT NOT NULL,
ADD COLUMN     "tenant_id" TEXT NOT NULL,
ADD COLUMN     "updated_at" TIMESTAMP(3) NOT NULL;

-- DropEnum
DROP TYPE "MessageContentType";

-- CreateTable
CREATE TABLE "oauth_accounts" (
    "id" TEXT NOT NULL,
    "provider" TEXT NOT NULL,
    "provider_id" TEXT NOT NULL,
    "system_user_id" TEXT,
    "tenant_user_id" TEXT,
    "user_type" TEXT NOT NULL,
    "email" TEXT,
    "profile" JSONB,
    "access_token" TEXT,
    "refresh_token" TEXT,
    "token_expires" TIMESTAMP(3),
    "last_login" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "oauth_accounts_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "line_users" (
    "id" TEXT NOT NULL,
    "line_user_id" TEXT NOT NULL,
    "display_name" TEXT NOT NULL,
    "picture_url" TEXT,
    "status_message" TEXT,
    "last_interaction_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "is_following" BOOLEAN NOT NULL DEFAULT true,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updated_at" TIMESTAMP(3) NOT NULL,
    "tenant_id" TEXT,
    "workspace_id" TEXT,
    "tenant_user_id" TEXT,
    "system_user_id" TEXT,

    CONSTRAINT "line_users_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "oauth_accounts_system_user_id_idx" ON "oauth_accounts"("system_user_id");

-- CreateIndex
CREATE INDEX "oauth_accounts_tenant_user_id_idx" ON "oauth_accounts"("tenant_user_id");

-- CreateIndex
CREATE INDEX "oauth_accounts_email_idx" ON "oauth_accounts"("email");

-- CreateIndex
CREATE UNIQUE INDEX "oauth_accounts_provider_provider_id_key" ON "oauth_accounts"("provider", "provider_id");

-- CreateIndex
CREATE UNIQUE INDEX "line_users_line_user_id_key" ON "line_users"("line_user_id");

-- CreateIndex
CREATE INDEX "line_users_tenant_id_idx" ON "line_users"("tenant_id");

-- CreateIndex
CREATE INDEX "line_users_workspace_id_idx" ON "line_users"("workspace_id");

-- CreateIndex
CREATE INDEX "line_users_tenant_user_id_idx" ON "line_users"("tenant_user_id");

-- CreateIndex
CREATE INDEX "line_users_system_user_id_idx" ON "line_users"("system_user_id");

-- CreateIndex
CREATE INDEX "albums_tenant_id_idx" ON "albums"("tenant_id");

-- CreateIndex
CREATE INDEX "albums_user_id_idx" ON "albums"("user_id");

-- CreateIndex
CREATE INDEX "comment_mentions_comment_id_idx" ON "comment_mentions"("comment_id");

-- CreateIndex
CREATE INDEX "comment_mentions_user_id_idx" ON "comment_mentions"("user_id");

-- CreateIndex
CREATE INDEX "comment_mentions_tenant_id_idx" ON "comment_mentions"("tenant_id");

-- CreateIndex
CREATE INDEX "comment_mentions_workspace_id_idx" ON "comment_mentions"("workspace_id");

-- CreateIndex
CREATE UNIQUE INDEX "comment_mentions_comment_id_user_id_user_type_key" ON "comment_mentions"("comment_id", "user_id", "user_type");

-- CreateIndex
CREATE INDEX "comment_reactions_comment_id_idx" ON "comment_reactions"("comment_id");

-- CreateIndex
CREATE INDEX "comment_reactions_user_id_idx" ON "comment_reactions"("user_id");

-- CreateIndex
CREATE INDEX "comment_reactions_tenant_id_idx" ON "comment_reactions"("tenant_id");

-- CreateIndex
CREATE INDEX "comment_reactions_workspace_id_idx" ON "comment_reactions"("workspace_id");

-- CreateIndex
CREATE UNIQUE INDEX "comment_reactions_comment_id_user_id_user_type_key" ON "comment_reactions"("comment_id", "user_id", "user_type");

-- CreateIndex
CREATE INDEX "comments_entity_type_entity_id_idx" ON "comments"("entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "comments_tenant_id_idx" ON "comments"("tenant_id");

-- CreateIndex
CREATE INDEX "comments_workspace_id_idx" ON "comments"("workspace_id");

-- CreateIndex
CREATE INDEX "comments_author_id_idx" ON "comments"("author_id");

-- CreateIndex
CREATE INDEX "comments_parent_id_idx" ON "comments"("parent_id");

-- CreateIndex
CREATE INDEX "comments_thread_id_idx" ON "comments"("thread_id");

-- CreateIndex
CREATE INDEX "comments_created_at_idx" ON "comments"("created_at");

-- CreateIndex
CREATE INDEX "conversation_participants_conversation_id_idx" ON "conversation_participants"("conversation_id");

-- CreateIndex
CREATE INDEX "conversation_participants_user_id_idx" ON "conversation_participants"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "conversation_participants_conversation_id_user_id_key" ON "conversation_participants"("conversation_id", "user_id");

-- CreateIndex
CREATE INDEX "conversations_workspace_id_idx" ON "conversations"("workspace_id");

-- CreateIndex
CREATE INDEX "conversations_created_by_idx" ON "conversations"("created_by");

-- CreateIndex
CREATE INDEX "conversations_last_activity_at_idx" ON "conversations"("last_activity_at");

-- CreateIndex
CREATE INDEX "file_access_logs_file_id_idx" ON "file_access_logs"("file_id");

-- CreateIndex
CREATE INDEX "file_access_logs_share_id_idx" ON "file_access_logs"("share_id");

-- CreateIndex
CREATE INDEX "file_access_logs_user_id_user_type_idx" ON "file_access_logs"("user_id", "user_type");

-- CreateIndex
CREATE INDEX "file_access_logs_tenant_id_idx" ON "file_access_logs"("tenant_id");

-- CreateIndex
CREATE INDEX "file_access_logs_accessed_at_idx" ON "file_access_logs"("accessed_at");

-- CreateIndex
CREATE INDEX "file_access_logs_access_type_idx" ON "file_access_logs"("access_type");

-- CreateIndex
CREATE INDEX "file_permissions_file_id_idx" ON "file_permissions"("file_id");

-- CreateIndex
CREATE INDEX "file_permissions_user_id_user_type_idx" ON "file_permissions"("user_id", "user_type");

-- CreateIndex
CREATE INDEX "file_permissions_tenant_id_idx" ON "file_permissions"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "file_permissions_file_id_user_id_user_type_key" ON "file_permissions"("file_id", "user_id", "user_type");

-- CreateIndex
CREATE UNIQUE INDEX "file_shares_share_token_key" ON "file_shares"("share_token");

-- CreateIndex
CREATE INDEX "file_shares_file_id_idx" ON "file_shares"("file_id");

-- CreateIndex
CREATE INDEX "file_shares_share_token_idx" ON "file_shares"("share_token");

-- CreateIndex
CREATE INDEX "file_shares_tenant_id_idx" ON "file_shares"("tenant_id");

-- CreateIndex
CREATE INDEX "file_shares_expires_at_idx" ON "file_shares"("expires_at");

-- CreateIndex
CREATE INDEX "line_auth_states_user_id_idx" ON "line_auth_states"("user_id");

-- CreateIndex
CREATE INDEX "line_message_logs_bot_id_idx" ON "line_message_logs"("bot_id");

-- CreateIndex
CREATE INDEX "message_attachments_message_id_idx" ON "message_attachments"("message_id");

-- CreateIndex
CREATE INDEX "message_reactions_message_id_idx" ON "message_reactions"("message_id");

-- CreateIndex
CREATE INDEX "message_reactions_user_id_idx" ON "message_reactions"("user_id");

-- CreateIndex
CREATE UNIQUE INDEX "message_reactions_message_id_user_id_emoji_key" ON "message_reactions"("message_id", "user_id", "emoji");

-- CreateIndex
CREATE INDEX "messages_conversation_id_idx" ON "messages"("conversation_id");

-- CreateIndex
CREATE INDEX "messages_sender_id_idx" ON "messages"("sender_id");

-- CreateIndex
CREATE INDEX "messages_created_at_idx" ON "messages"("created_at");

-- CreateIndex
CREATE INDEX "password_reset_tokens_system_user_id_idx" ON "password_reset_tokens"("system_user_id");

-- CreateIndex
CREATE INDEX "password_reset_tokens_tenant_user_id_idx" ON "password_reset_tokens"("tenant_user_id");

-- CreateIndex
CREATE UNIQUE INDEX "payments_order_id_key" ON "payments"("order_id");

-- CreateIndex
CREATE INDEX "photos_album_id_idx" ON "photos"("album_id");

-- CreateIndex
CREATE INDEX "photos_project_id_idx" ON "photos"("project_id");

-- CreateIndex
CREATE INDEX "photos_tenant_id_idx" ON "photos"("tenant_id");

-- CreateIndex
CREATE INDEX "photos_user_id_idx" ON "photos"("user_id");

-- CreateIndex
CREATE INDEX "progress_entries_project_id_idx" ON "progress_entries"("project_id");

-- CreateIndex
CREATE INDEX "progress_entries_task_id_idx" ON "progress_entries"("task_id");

-- CreateIndex
CREATE INDEX "progress_entries_tenant_id_idx" ON "progress_entries"("tenant_id");

-- CreateIndex
CREATE INDEX "progress_entries_user_id_idx" ON "progress_entries"("user_id");

-- CreateIndex
CREATE INDEX "progress_entries_recorded_at_idx" ON "progress_entries"("recorded_at");

-- CreateIndex
CREATE INDEX "progress_reports_project_id_idx" ON "progress_reports"("project_id");

-- CreateIndex
CREATE INDEX "progress_reports_tenant_id_idx" ON "progress_reports"("tenant_id");

-- CreateIndex
CREATE INDEX "progress_reports_report_date_idx" ON "progress_reports"("report_date");

-- CreateIndex
CREATE UNIQUE INDEX "progress_reports_project_id_report_type_period_key" ON "progress_reports"("project_id", "report_type", "period");

-- CreateIndex
CREATE INDEX "project_milestones_project_id_idx" ON "project_milestones"("project_id");

-- CreateIndex
CREATE INDEX "project_milestones_tenant_id_idx" ON "project_milestones"("tenant_id");

-- CreateIndex
CREATE INDEX "project_milestones_target_date_idx" ON "project_milestones"("target_date");

-- CreateIndex
CREATE INDEX "projects_tenant_id_idx" ON "projects"("tenant_id");

-- CreateIndex
CREATE INDEX "projects_user_id_idx" ON "projects"("user_id");

-- CreateIndex
CREATE INDEX "projects_workspace_id_idx" ON "projects"("workspace_id");

-- CreateIndex
CREATE INDEX "projects_parent_project_id_idx" ON "projects"("parent_project_id");

-- CreateIndex
CREATE UNIQUE INDEX "role_permissions_role_id_permission_id_key" ON "role_permissions"("role_id", "permission_id");

-- CreateIndex
CREATE INDEX "settings_created_by_idx" ON "settings"("created_by");

-- CreateIndex
CREATE INDEX "settings_updated_by_idx" ON "settings"("updated_by");

-- CreateIndex
CREATE INDEX "shared_files_tenant_id_idx" ON "shared_files"("tenant_id");

-- CreateIndex
CREATE INDEX "shared_files_workspace_id_idx" ON "shared_files"("workspace_id");

-- CreateIndex
CREATE INDEX "shared_files_uploader_id_uploader_type_idx" ON "shared_files"("uploader_id", "uploader_type");

-- CreateIndex
CREATE INDEX "shared_files_entity_type_entity_id_idx" ON "shared_files"("entity_type", "entity_id");

-- CreateIndex
CREATE INDEX "shared_files_parent_file_id_idx" ON "shared_files"("parent_file_id");

-- CreateIndex
CREATE INDEX "shared_files_created_at_idx" ON "shared_files"("created_at");

-- CreateIndex
CREATE INDEX "subscriptions_plan_id_idx" ON "subscriptions"("plan_id");

-- CreateIndex
CREATE INDEX "subscriptions_tenant_id_idx" ON "subscriptions"("tenant_id");

-- CreateIndex
CREATE INDEX "tasks_project_id_idx" ON "tasks"("project_id");

-- CreateIndex
CREATE INDEX "tasks_assignee_id_idx" ON "tasks"("assignee_id");

-- CreateIndex
CREATE INDEX "tasks_tenant_id_idx" ON "tasks"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_credit_purchases_tenant_id_idx" ON "tenant_credit_purchases"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_invitations_role_id_idx" ON "tenant_invitations"("role_id");

-- CreateIndex
CREATE INDEX "tenant_invitations_tenant_id_idx" ON "tenant_invitations"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenant_invitations_email_tenant_id_status_key" ON "tenant_invitations"("email", "tenant_id", "status");

-- CreateIndex
CREATE INDEX "tenant_lifecycle_events_tenant_id_idx" ON "tenant_lifecycle_events"("tenant_id");

-- CreateIndex
CREATE INDEX "tenant_lifecycle_events_event_type_idx" ON "tenant_lifecycle_events"("event_type");

-- CreateIndex
CREATE INDEX "tenant_lifecycle_events_created_at_idx" ON "tenant_lifecycle_events"("created_at");

-- CreateIndex
CREATE INDEX "tenant_users_tenant_id_idx" ON "tenant_users"("tenant_id");

-- CreateIndex
CREATE UNIQUE INDEX "tenants_name_key" ON "tenants"("name");

-- CreateIndex
CREATE INDEX "workspace_invitations_workspace_id_idx" ON "workspace_invitations"("workspace_id");

-- CreateIndex
CREATE UNIQUE INDEX "workspace_members_workspace_id_tenant_user_id_key" ON "workspace_members"("workspace_id", "tenant_user_id");

-- CreateIndex
CREATE INDEX "workspace_templates_tenant_id_idx" ON "workspace_templates"("tenant_id");

-- CreateIndex
CREATE INDEX "workspace_templates_is_system_idx" ON "workspace_templates"("is_system");

-- CreateIndex
CREATE INDEX "workspaces_owner_id_idx" ON "workspaces"("owner_id");

-- CreateIndex
CREATE INDEX "workspaces_tenant_id_idx" ON "workspaces"("tenant_id");

-- RenameForeignKey
ALTER TABLE "message_center_messages" RENAME CONSTRAINT "message_center_messages_conversationId_fkey" TO "message_center_messages_conversation_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_center_messages" RENAME CONSTRAINT "message_center_messages_replyToMessageId_fkey" TO "message_center_messages_reply_to_message_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_center_messages" RENAME CONSTRAINT "message_center_messages_tenantId_fkey" TO "message_center_messages_tenant_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_center_notifications" RENAME CONSTRAINT "message_center_notifications_tenantId_fkey" TO "message_center_notifications_tenant_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_center_notifications" RENAME CONSTRAINT "message_center_notifications_workspaceId_fkey" TO "message_center_notifications_workspace_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_conversations" RENAME CONSTRAINT "message_conversations_tenantId_fkey" TO "message_conversations_tenant_id_fkey";

-- RenameForeignKey
ALTER TABLE "message_conversations" RENAME CONSTRAINT "message_conversations_workspaceId_fkey" TO "message_conversations_workspace_id_fkey";

-- AddForeignKey
ALTER TABLE "albums" ADD CONSTRAINT "albums_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_message_logs" ADD CONSTRAINT "line_message_logs_line_user_id_fkey" FOREIGN KEY ("line_user_id") REFERENCES "line_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "order_histories" ADD CONSTRAINT "order_histories_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "orders" ADD CONSTRAINT "orders_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "payments" ADD CONSTRAINT "payments_order_id_fkey" FOREIGN KEY ("order_id") REFERENCES "orders"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "permissions" ADD CONSTRAINT "permissions_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "permission_categories"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "photos" ADD CONSTRAINT "photos_album_id_fkey" FOREIGN KEY ("album_id") REFERENCES "albums"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "photos" ADD CONSTRAINT "photos_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "photos" ADD CONSTRAINT "photos_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "projects" ADD CONSTRAINT "projects_parent_project_id_fkey" FOREIGN KEY ("parent_project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tasks" ADD CONSTRAINT "tasks_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_task_id_fkey" FOREIGN KEY ("task_id") REFERENCES "tasks"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_entries" ADD CONSTRAINT "progress_entries_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "project_milestones" ADD CONSTRAINT "project_milestones_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_reports" ADD CONSTRAINT "progress_reports_project_id_fkey" FOREIGN KEY ("project_id") REFERENCES "projects"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "progress_reports" ADD CONSTRAINT "progress_reports_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_system_user_id_fkey" FOREIGN KEY ("system_user_id") REFERENCES "system_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "refresh_tokens" ADD CONSTRAINT "refresh_tokens_tenant_user_id_fkey" FOREIGN KEY ("tenant_user_id") REFERENCES "tenant_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "password_reset_tokens" ADD CONSTRAINT "password_reset_tokens_system_user_id_fkey" FOREIGN KEY ("system_user_id") REFERENCES "system_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "password_reset_tokens" ADD CONSTRAINT "password_reset_tokens_tenant_user_id_fkey" FOREIGN KEY ("tenant_user_id") REFERENCES "tenant_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_permission_id_fkey" FOREIGN KEY ("permission_id") REFERENCES "permissions"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "role_permissions" ADD CONSTRAINT "role_permissions_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "roles" ADD CONSTRAINT "roles_parent_role_id_fkey" FOREIGN KEY ("parent_role_id") REFERENCES "roles"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "roles" ADD CONSTRAINT "roles_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "subscriptions" ADD CONSTRAINT "subscriptions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_credit_purchases" ADD CONSTRAINT "tenant_credit_purchases_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_invitations" ADD CONSTRAINT "tenant_invitations_role_id_fkey" FOREIGN KEY ("role_id") REFERENCES "roles"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_invitations" ADD CONSTRAINT "tenant_invitations_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenants" ADD CONSTRAINT "tenants_plan_id_fkey" FOREIGN KEY ("plan_id") REFERENCES "plans"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workspace_members" ADD CONSTRAINT "workspace_members_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workspace_members" ADD CONSTRAINT "workspace_members_tenant_user_id_fkey" FOREIGN KEY ("tenant_user_id") REFERENCES "tenant_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workspaces" ADD CONSTRAINT "workspaces_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workspace_templates" ADD CONSTRAINT "workspace_templates_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "workspace_invitations" ADD CONSTRAINT "workspace_invitations_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_users" ADD CONSTRAINT "tenant_users_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "tenant_lifecycle_events" ADD CONSTRAINT "tenant_lifecycle_events_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comments" ADD CONSTRAINT "comments_parent_id_fkey" FOREIGN KEY ("parent_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comments" ADD CONSTRAINT "comments_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comments" ADD CONSTRAINT "comments_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_reactions" ADD CONSTRAINT "comment_reactions_comment_id_fkey" FOREIGN KEY ("comment_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_reactions" ADD CONSTRAINT "comment_reactions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_reactions" ADD CONSTRAINT "comment_reactions_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_mentions" ADD CONSTRAINT "comment_mentions_comment_id_fkey" FOREIGN KEY ("comment_id") REFERENCES "comments"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_mentions" ADD CONSTRAINT "comment_mentions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "comment_mentions" ADD CONSTRAINT "comment_mentions_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_parent_file_id_fkey" FOREIGN KEY ("parent_file_id") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "shared_files" ADD CONSTRAINT "shared_files_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_permissions" ADD CONSTRAINT "file_permissions_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_permissions" ADD CONSTRAINT "file_permissions_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_shares" ADD CONSTRAINT "file_shares_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_shares" ADD CONSTRAINT "file_shares_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "shared_files"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_share_id_fkey" FOREIGN KEY ("share_id") REFERENCES "file_shares"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "file_access_logs" ADD CONSTRAINT "file_access_logs_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversations" ADD CONSTRAINT "conversations_last_message_id_fkey" FOREIGN KEY ("last_message_id") REFERENCES "messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "conversation_participants" ADD CONSTRAINT "conversation_participants_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_reply_to_id_fkey" FOREIGN KEY ("reply_to_id") REFERENCES "messages"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "messages" ADD CONSTRAINT "messages_conversation_id_fkey" FOREIGN KEY ("conversation_id") REFERENCES "conversations"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message_reactions" ADD CONSTRAINT "message_reactions_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "message_attachments" ADD CONSTRAINT "message_attachments_message_id_fkey" FOREIGN KEY ("message_id") REFERENCES "messages"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "oauth_accounts" ADD CONSTRAINT "oauth_accounts_system_user_id_fkey" FOREIGN KEY ("system_user_id") REFERENCES "system_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "oauth_accounts" ADD CONSTRAINT "oauth_accounts_tenant_user_id_fkey" FOREIGN KEY ("tenant_user_id") REFERENCES "tenant_users"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_users" ADD CONSTRAINT "line_users_tenant_user_id_fkey" FOREIGN KEY ("tenant_user_id") REFERENCES "tenant_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_users" ADD CONSTRAINT "line_users_system_user_id_fkey" FOREIGN KEY ("system_user_id") REFERENCES "system_users"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_users" ADD CONSTRAINT "line_users_tenant_id_fkey" FOREIGN KEY ("tenant_id") REFERENCES "tenants"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "line_users" ADD CONSTRAINT "line_users_workspace_id_fkey" FOREIGN KEY ("workspace_id") REFERENCES "workspaces"("id") ON DELETE CASCADE ON UPDATE CASCADE;

-- RenameIndex
ALTER INDEX "message_center_messages_conversationId_idx" RENAME TO "message_center_messages_conversation_id_idx";

-- RenameIndex
ALTER INDEX "message_center_messages_replyToMessageId_idx" RENAME TO "message_center_messages_reply_to_message_id_idx";

-- RenameIndex
ALTER INDEX "message_center_messages_senderId_senderType_idx" RENAME TO "message_center_messages_sender_id_sender_type_idx";

-- RenameIndex
ALTER INDEX "message_center_messages_sentAt_idx" RENAME TO "message_center_messages_sent_at_idx";

-- RenameIndex
ALTER INDEX "message_center_messages_tenantId_idx" RENAME TO "message_center_messages_tenant_id_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_createdAt_idx" RENAME TO "message_center_notifications_created_at_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_entityType_entityId_idx" RENAME TO "message_center_notifications_entity_type_entity_id_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_isRead_idx" RENAME TO "message_center_notifications_is_read_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_recipientId_recipientType_idx" RENAME TO "message_center_notifications_recipient_id_recipient_type_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_tenantId_idx" RENAME TO "message_center_notifications_tenant_id_idx";

-- RenameIndex
ALTER INDEX "message_center_notifications_workspaceId_idx" RENAME TO "message_center_notifications_workspace_id_idx";

-- RenameIndex
ALTER INDEX "message_conversations_lastMessageAt_idx" RENAME TO "message_conversations_last_message_at_idx";

-- RenameIndex
ALTER INDEX "message_conversations_tenantId_idx" RENAME TO "message_conversations_tenant_id_idx";

-- RenameIndex
ALTER INDEX "message_conversations_workspaceId_idx" RENAME TO "message_conversations_workspace_id_idx";
