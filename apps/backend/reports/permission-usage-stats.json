{"total": 51, "byScope": {"SYSTEM": 24, "TENANT": 8, "WORKSPACE": 19}, "byCategory": {"system_management": 5, "user_management": 11, "log_management": 2, "tenant_management": 5, "collaboration": 14, "member_management": 4, "ai_management": 9, "workspace_settings": 1}, "bySubject": {"Dashboard": 1, "Permission": 5, "Role": 4, "SystemLog": 1, "SystemUser": 5, "Workspace": 5, "Comment": 4, "CommentReaction": 2, "SharedFile": 4, "FilePermission": 1, "FileShare": 3, "User": 4, "LineBot": 4, "LineMessageLog": 1, "AiModel": 5, "AdminPanel": 1, "Tenant": 1}, "byAction": {"read": 13, "create": 12, "update": 9, "delete": 11, "manage": 4, "access": 2}, "byZone": {"admin": 32, "workspace": 19}, "fileDistribution": [["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/files/files.controller.ts", 8], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-users/system-users.controller.ts", 6], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/comments/comments.controller.ts", 6], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/ai/configuration/models/ai-models.controller.ts", 5], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/roles/roles.controller.ts", 4], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/permissions/permissions.controller.ts", 4], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/workspaces/workspaces.controller.ts", 4], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/workspace/users/users.controller.ts", 4], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/line-bot.controller.ts", 4], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/frontend/src/utils/role-check.ts", 3], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/dashboard/dashboard.controller.ts", 1], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/system-logs/system-logs.controller.ts", 1], ["/Users/<USER>/Documents/Github/HorizAI_SaaS_NextJS/apps/backend/src/modules/admin/line/controllers/message-log.controller.ts", 1]]}