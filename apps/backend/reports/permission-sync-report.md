# 權限同步報告

- **產生時間**: 2025-06-11T15:32:19.298Z
- **版本**: unknown
- **模式**: sync

## 統計摘要
- 總權限數: 51
- 按範圍:
  - SYSTEM: 24
  - TENANT: 8
  - WORKSPACE: 19
- 按分類:
  - system_management: 5
  - user_management: 11
  - log_management: 2
  - tenant_management: 5
  - collaboration: 14
  - member_management: 4
  - ai_management: 9
  - workspace_settings: 1

## 權限列表
- `Workspace` `access` [WORKSPACE/workspace_settings]
- `Permission` `create` [SYSTEM/user_management]
- `Permission` `delete` [SYSTEM/user_management]
- `Permission` `manage` [SYSTEM/user_management]
- `Permission` `read` [SYSTEM/user_management]
- `Permission` `update` [SYSTEM/user_management]
- `Role` `create` [SYSTEM/user_management]
- `Role` `delete` [SYSTEM/user_management]
- `Role` `read` [SYSTEM/user_management]
- `Role` `update` [SYSTEM/user_management]
- `SystemUser` `manage` [SYSTEM/user_management]
- `SystemUser` `read` [SYSTEM/user_management]
- `Tenant` `manage` [SYSTEM/tenant_management]
- `Workspace` `create` [TENANT/tenant_management]
- `Workspace` `delete` [TENANT/tenant_management]
- `Workspace` `read` [TENANT/tenant_management]
- `Workspace` `update` [TENANT/tenant_management]
- `AdminPanel` `access` [SYSTEM/system_management]
- `Dashboard` `read` [SYSTEM/system_management]
- `SystemUser` `create` [SYSTEM/system_management]
- `SystemUser` `delete` [SYSTEM/system_management]
- `SystemUser` `update` [SYSTEM/system_management]
- `User` `create` [WORKSPACE/member_management]
- `User` `delete` [WORKSPACE/member_management]
- `User` `read` [WORKSPACE/member_management]
- `User` `update` [WORKSPACE/member_management]
- `LineMessageLog` `read` [SYSTEM/log_management]
- `SystemLog` `read` [SYSTEM/log_management]
- `Comment` `create` [WORKSPACE/collaboration]
- `Comment` `delete` [WORKSPACE/collaboration]
- `Comment` `read` [WORKSPACE/collaboration]
- `Comment` `update` [WORKSPACE/collaboration]
- `CommentReaction` `create` [WORKSPACE/collaboration]
- `CommentReaction` `delete` [WORKSPACE/collaboration]
- `FilePermission` `create` [WORKSPACE/collaboration]
- `FileShare` `create` [WORKSPACE/collaboration]
- `FileShare` `delete` [WORKSPACE/collaboration]
- `FileShare` `read` [WORKSPACE/collaboration]
- `SharedFile` `create` [WORKSPACE/collaboration]
- `SharedFile` `delete` [WORKSPACE/collaboration]
- `SharedFile` `read` [WORKSPACE/collaboration]
- `SharedFile` `update` [WORKSPACE/collaboration]
- `AiModel` `create` [SYSTEM/ai_management]
- `AiModel` `delete` [SYSTEM/ai_management]
- `AiModel` `manage` [SYSTEM/ai_management]
- `AiModel` `read` [SYSTEM/ai_management]
- `AiModel` `update` [SYSTEM/ai_management]
- `LineBot` `create` [TENANT/ai_management]
- `LineBot` `delete` [TENANT/ai_management]
- `LineBot` `read` [TENANT/ai_management]
- `LineBot` `update` [TENANT/ai_management]