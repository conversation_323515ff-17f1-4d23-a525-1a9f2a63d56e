#!/bin/bash

# Git Pre-commit Hook for Permission Sync Check
# 在提交前檢查權限是否需要同步

set -e

echo "🔍 檢查權限同步狀態..."

# 切換到後端目錄
cd "$(dirname "$0")/../../"

# 檢查是否有權限相關的檔案變更
CHANGED_FILES=$(git diff --cached --name-only | grep -E '\.(ts|js)$' | grep -v -E '\.(spec|test)\.(ts|js)$' || true)

if [ -z "$CHANGED_FILES" ]; then
    echo "✅ 沒有相關檔案變更，跳過權限檢查"
    exit 0
fi

echo "📝 檢測到以下檔案變更:"
echo "$CHANGED_FILES" | sed 's/^/  /'

# 執行權限快速檢查
echo ""
echo "🚀 執行權限快速檢查..."

if npm run db:perms-check > /dev/null 2>&1; then
    echo "✅ 權限檢查通過"
    exit 0
else
    echo "❌ 權限檢查失敗"
    echo ""
    echo "💡 可能的解決方案:"
    echo "  1. 執行 'npm run db:sync-perms' 同步權限"
    echo "  2. 檢查是否有新的 @CheckPolicies 裝飾器需要同步"
    echo "  3. 確認權限定義是否正確"
    echo ""
    echo "🔧 如果確定要跳過檢查，請使用: git commit --no-verify"
    exit 1
fi
