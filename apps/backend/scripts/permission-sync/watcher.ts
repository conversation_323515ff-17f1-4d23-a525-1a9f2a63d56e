import { watch, FSWatcher } from 'chokidar';
import { PermissionScanner } from './scanner';
import { PermissionSyncer } from './syncer';
import { PermissionReporter } from './reporter';
import { PrismaClient } from '@prisma/client';
import * as path from 'path';
import { debounce } from 'lodash';

/**
 * 權限變更監控器
 * 監控檔案變更並自動觸發權限同步
 */
export class PermissionWatcher {
  private watcher: FSWatcher | null = null;
  private scanner: PermissionScanner;
  private syncer: PermissionSyncer;
  private reporter: PermissionReporter;
  private isRunning = false;

  // 防抖處理，避免頻繁觸發
  private debouncedSync = debounce(this.performSync.bind(this), 2000);

  constructor(
    private options: {
      watchPaths?: string[];
      autoSync?: boolean;
      verbose?: boolean;
    } = {}
  ) {
    this.scanner = new PermissionScanner();
    this.syncer = new PermissionSyncer();
    this.reporter = new PermissionReporter();
  }

  /**
   * 開始監控
   */
  async start(): Promise<void> {
    if (this.isRunning) {
      console.log('⚠️  權限監控器已在運行中');
      return;
    }

    const watchPaths = this.options.watchPaths || [
      'src/**/*.ts',
      'src/**/*.js',
      '!src/**/*.spec.ts',
      '!src/**/*.test.ts',
      '!node_modules/**',
      '!dist/**'
    ];

    console.log('🔍 啟動權限變更監控器...');
    console.log(`📁 監控路徑: ${watchPaths.join(', ')}`);

    this.watcher = watch(watchPaths, {
      ignored: /(^|[\/\\])\../, // 忽略隱藏檔案
      persistent: true,
      ignoreInitial: true
    });

    this.watcher
      .on('change', (filePath) => this.handleFileChange(filePath, 'change'))
      .on('add', (filePath) => this.handleFileChange(filePath, 'add'))
      .on('unlink', (filePath) => this.handleFileChange(filePath, 'delete'))
      .on('error', (error) => console.error('❌ 檔案監控錯誤:', error));

    this.isRunning = true;
    console.log('✅ 權限監控器已啟動');
  }

  /**
   * 停止監控
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    if (this.watcher) {
      await this.watcher.close();
      this.watcher = null;
    }

    this.isRunning = false;
    console.log('🛑 權限監控器已停止');
  }

  /**
   * 處理檔案變更
   */
  private handleFileChange(filePath: string, changeType: 'change' | 'add' | 'delete'): void {
    // 只處理可能包含權限定義的檔案
    if (!this.isPermissionRelevantFile(filePath)) {
      return;
    }

    if (this.options.verbose) {
      console.log(`📝 檔案${changeType === 'change' ? '變更' : changeType === 'add' ? '新增' : '刪除'}: ${filePath}`);
    }

    // 觸發防抖同步
    this.debouncedSync();
  }

  /**
   * 檢查檔案是否可能包含權限定義
   */
  private isPermissionRelevantFile(filePath: string): boolean {
    // 檢查檔案副檔名
    if (!/\.(ts|js)$/.test(filePath)) {
      return false;
    }

    // 排除測試檔案
    if (/\.(spec|test)\.(ts|js)$/.test(filePath)) {
      return false;
    }

    // 排除特定目錄
    if (/node_modules|dist|build/.test(filePath)) {
      return false;
    }

    return true;
  }

  /**
   * 執行權限同步
   */
  private async performSync(): Promise<void> {
    if (!this.options.autoSync) {
      console.log('💡 檢測到權限相關檔案變更，建議執行: pnpm db:sync-perms');
      return;
    }

    try {
      console.log('🔄 自動執行權限同步...');

      // 1. 掃描權限
      const scanResult = await this.scanner.scan(false); // 不使用快取

      // 2. 檢查是否有變更
      const syncResult = await this.syncer.sync(scanResult.permissions, {
        dryRun: true // 先預覽
      });

      if (syncResult.created === 0 && syncResult.updated === 0 && syncResult.deprecated === 0) {
        if (this.options.verbose) {
          console.log('✅ 權限無變更');
        }
        return;
      }

      // 3. 執行實際同步
      console.log(`🔄 檢測到權限變更 (新增: ${syncResult.created}, 更新: ${syncResult.updated}, 廢棄: ${syncResult.deprecated})`);
      
      const actualSyncResult = await this.syncer.sync(scanResult.permissions, {
        dryRun: false
      });

      // 4. 生成報告
      const report = await this.reporter.generateReport(
        scanResult,
        actualSyncResult,
        'sync'
      );

      console.log('✅ 自動權限同步完成');

    } catch (error) {
      console.error('❌ 自動權限同步失敗:', error.message);
    }
  }
}
