# 權限收集配套系統

HorizAI SaaS 平台的完整權限管理和自動化同步系統。

## 🎯 系統概述

本系統提供完整的權限收集、驗證、同步和管理配套機制，確保程式碼中的權限使用與資料庫定義保持一致性。

### 核心功能

- **自動權限掃描** - 掃描程式碼中的 `@CheckPolicies` 裝飾器
- **權限同步** - 將掃描結果同步到資料庫
- **一致性驗證** - 檢查權限定義與使用的一致性
- **開發模式監控** - 即時監控檔案變更並提示同步
- **統計分析** - 生成權限使用統計報告

## 🚀 快速開始

### 基本命令

```bash
# 掃描權限定義
npm run db:scan-perms

# 同步權限到資料庫
npm run db:sync-perms

# 預覽同步（不實際修改資料庫）
npm run db:sync-perms -- --dry-run

# 強制同步（覆蓋現有定義）
npm run db:sync-perms -- --force

# 驗證權限一致性
npm run db:validate-perms

# 啟動開發模式（檔案監控）
npm run db:perms-dev

# 快速檢查（適用於 Git hooks）
npm run db:perms-check

# 生成權限統計
npm run db:perms-stats
```

### 開發工作流程

1. **開發階段** - 使用 `npm run db:perms-dev` 啟動監控
2. **提交前** - 自動執行 `npm run db:perms-check`
3. **部署前** - 執行 `npm run db:sync-perms` 確保同步

## 📋 詳細功能

### 1. 權限掃描 (scan)

掃描程式碼中的權限定義，支援：

- `@CheckPolicies` 裝飾器
- `ability.can()` 方法呼叫
- 權限常數定義

```bash
npm run db:scan-perms [選項]

選項:
  --no-cache     不使用快取
  -v, --verbose  詳細輸出
  -q, --quiet    靜默模式
```

### 2. 權限同步 (sync)

將掃描結果同步到資料庫：

```bash
npm run db:sync-perms [選項]

選項:
  --dry-run      預覽模式，不實際修改資料庫
  --force        強制覆蓋現有權限定義
  --no-cache     不使用快取
  -v, --verbose  詳細輸出
```

### 3. 權限驗證 (validate)

檢查權限一致性：

```bash
npm run db:validate-perms [選項]

檢查項目:
  ✓ 遺失的權限（程式碼中使用但資料庫中不存在）
  ✓ 孤立的權限（資料庫中存在但程式碼中未使用）
  ✓ 重複的權限定義
  ✓ 硬編碼權限使用
  ✓ 廢棄權限的使用
```

### 4. 開發模式 (dev)

啟動檔案監控，自動檢測權限變更：

```bash
npm run db:perms-dev [選項]

功能:
  🔍 監控檔案變更
  ⚡ 自動檢測權限變更
  📊 生成開發報告
  💡 提供同步建議

選項:
  --auto-sync    自動同步權限變更
  -v, --verbose  詳細輸出
```

### 5. 快速檢查 (check)

適用於 Git hooks 的快速檢查：

```bash
npm run db:perms-check

返回值:
  0 - 檢查通過
  1 - 檢查失敗（需要同步）
```

### 6. 權限統計 (stats)

生成權限使用統計：

```bash
npm run db:perms-stats

統計內容:
  📊 總權限數
  🏷️  按範圍分布 (SYSTEM/TENANT/WORKSPACE)
  📂 按類別分布
  📄 檔案分布
```

## 🔧 配置

### 掃描配置

在 `scanner.ts` 中配置掃描參數：

```typescript
const config: ScanConfig = {
  backendPaths: ['src/**/*.ts'],
  frontendPaths: ['../frontend/src/**/*.vue'],
  excludePatterns: ['**/*.spec.ts', '**/*.test.ts'],
  cacheEnabled: true,
  suppressWarnings: false,
};
```

### 監控配置

在 `watcher.ts` 中配置檔案監控：

```typescript
const watchPaths = ['src/**/*.ts', 'src/**/*.js', '!src/**/*.spec.ts', '!node_modules/**'];
```

## 📊 報告系統

系統會自動生成多種報告：

### 掃描報告

- `reports/permission-scan-report.json` - 掃描結果
- 包含權限列表、統計資訊、錯誤警告

### 同步報告

- `reports/permission-sync-report.json` - 同步結果
- `reports/permission-sync-report.md` - Markdown 格式報告

### 開發報告

- `reports/permission-dev-report.json` - 開發模式報告
- 包含驗證結果、同步預覽、建議

### 統計報告

- `reports/permission-usage-stats.json` - 使用統計

## 🔗 Git 整合

### Pre-commit Hook

自動設置 Git pre-commit hook：

```bash
# 複製 hook 腳本
cp scripts/git-hooks/pre-commit-permissions.sh .git/hooks/pre-commit
chmod +x .git/hooks/pre-commit
```

### CI/CD 整合

在 CI/CD 管道中添加權限檢查：

```yaml
- name: Check Permission Sync
  run: npm run db:perms-check
```

## 🛠️ 開發指南

### 添加新權限

1. 在控制器中使用 `@CheckPolicies` 裝飾器
2. 執行 `npm run db:scan-perms` 掃描
3. 執行 `npm run db:sync-perms` 同步

### 權限命名規範

```typescript
@CheckPolicies((ability) => ability.can('create', 'User'))
```

- **Action**: create, read, update, delete, manage
- **Subject**: User, Role, Permission, Workspace 等

### 最佳實踐

1. **使用常數** - 避免硬編碼權限字串
2. **定期同步** - 開發過程中定期執行同步
3. **驗證一致性** - 提交前執行驗證
4. **監控變更** - 使用開發模式監控檔案變更

## 🚨 故障排除

### 常見問題

**Q: 權限掃描失敗**
A: 檢查檔案路徑配置，確認 TypeScript 編譯正常

**Q: 同步失敗**
A: 檢查資料庫連接，確認權限定義格式正確

**Q: 驗證失敗**
A: 查看詳細錯誤訊息，修復權限一致性問題

### 日誌查看

```bash
# 查看詳細日誌
npm run db:sync-perms -- --verbose

# 查看報告
cat reports/permission-sync-report.json
```

## 📚 API 參考

詳細的 API 文檔請參考各個模組的 TypeScript 介面定義。

## 🤝 貢獻

歡迎提交 Issue 和 Pull Request 來改進權限管理系統。
