import { Command } from "commander";
import { PrismaClient } from "@prisma/client";
import { PermissionScanner } from "./scanner";
import { PermissionSyncer } from "./syncer";
import { PermissionReporter } from "./reporter";
import { PermissionValidator } from "./validator";
import { PermissionDevTools } from "./dev-tools";
import { CLIOptions } from "./types";

/**
 * 權限同步 CLI 工具
 */
class PermissionSyncCLI {
  private prisma: PrismaClient;
  private scanner: PermissionScanner;
  private syncer: PermissionSyncer;
  private reporter: PermissionReporter;
  private validator: PermissionValidator;
  private devTools: PermissionDevTools;

  constructor() {
    this.prisma = new PrismaClient();
    this.scanner = new PermissionScanner();
    this.syncer = new PermissionSyncer(this.prisma);
    this.reporter = new PermissionReporter();
    this.validator = new PermissionValidator(this.prisma);
    this.devTools = new PermissionDevTools();
  }

  /**
   * 初始化 CLI 命令
   */
  initializeCommands(): Command {
    const program = new Command();

    program
      .name("permission-sync")
      .description("HorizAI SaaS 權限自動化同步工具")
      .version("1.0.0");

    // scan 命令 - 只掃描不同步
    program
      .command("scan")
      .description("掃描程式碼中的權限定義")
      .option("--no-cache", "不使用快取")
      .option("-v, --verbose", "詳細輸出")
      .option("-q, --quiet", "靜默模式，不顯示警告")
      .option("-o, --output <path>", "指定報告輸出路徑")
      .action(async (options) => {
        await this.handleScanCommand({
          command: "scan",
          cache: options.cache,
          verbose: options.verbose,
          suppressWarnings: options.quiet,
          output: options.output,
        });
      });

    // sync 命令 - 掃描並同步
    program
      .command("sync")
      .description("掃描並同步權限到資料庫")
      .option("--dry-run", "預覽模式，不實際修改資料庫")
      .option("--force", "強制覆蓋現有權限定義")
      .option("--no-cache", "不使用快取")
      .option("-v, --verbose", "詳細輸出")
      .option("-q, --quiet", "靜默模式，不顯示警告")
      .option("-o, --output <path>", "指定報告輸出路徑")
      .action(async (options) => {
        await this.handleSyncCommand({
          command: "sync",
          dryRun: options.dryRun,
          force: options.force,
          cache: options.cache,
          verbose: options.verbose,
          suppressWarnings: options.quiet,
          output: options.output,
        });
      });

    // report 命令 - 產生報告
    program
      .command("report")
      .description("產生權限同步報告")
      .option("--format <format>", "報告格式 (json|markdown)", "json")
      .option("-o, --output <path>", "指定報告輸出路徑")
      .action(async (options) => {
        await this.handleReportCommand(
          {
            command: "report",
            output: options.output,
          },
          options.format
        );
      });

    // validate 命令 - 權限一致性驗證
    program
      .command("validate")
      .description("驗證權限一致性")
      .option("--no-cache", "不使用快取")
      .option("-v, --verbose", "詳細輸出")
      .action(async (options) => {
        await this.handleValidateCommand({
          command: "validate",
          cache: options.cache,
          verbose: options.verbose,
        });
      });

    // dev 命令 - 開發模式
    program
      .command("dev")
      .description("啟動權限開發模式（檔案監控）")
      .option("--auto-sync", "自動同步權限變更")
      .option("-v, --verbose", "詳細輸出")
      .action(async (options) => {
        await this.handleDevCommand({
          command: "dev",
          autoSync: options.autoSync,
          verbose: options.verbose,
        });
      });

    // check 命令 - 快速檢查
    program
      .command("check")
      .description("快速檢查權限同步狀態（適用於 Git hooks）")
      .action(async () => {
        await this.handleCheckCommand({
          command: "check",
        });
      });

    // stats 命令 - 權限統計
    program
      .command("stats")
      .description("生成權限使用統計")
      .option("-v, --verbose", "詳細輸出")
      .action(async (options) => {
        await this.handleStatsCommand({
          command: "stats",
          verbose: options.verbose,
        });
      });

    return program;
  }

  /**
   * 處理 scan 命令
   */
  async handleScanCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      console.log("🔍 開始掃描權限定義...\n");
      // 根據 suppressWarnings 參數重建 scanner
      this.scanner = new PermissionScanner({
        suppressWarnings: options.suppressWarnings,
      });

      const scanResult = await this.scanner.scan(options.cache !== false);

      if (options.verbose) {
        console.log("\n📋 詳細掃描結果:");
        console.log(`  發現權限: ${scanResult.permissions.length} 個`);
        console.log(`  掃描檔案: ${scanResult.stats.scannedFiles} 個`);
        console.log(
          `  含權限檔案: ${scanResult.stats.filesWithPermissions} 個`
        );

        if (scanResult.errors.length > 0) {
          console.log(`  錯誤: ${scanResult.errors.length} 個`);
        }
      }

      await this.reporter.generateScanReport(scanResult);

      console.log("\n✅ 掃描完成！");
    } catch (error) {
      console.error("❌ 掃描失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 處理 sync 命令
   */
  async handleSyncCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      console.log(`🚀 開始${options.dryRun ? "預覽" : "執行"}權限同步...\n`);
      // 根據 suppressWarnings 參數重建 scanner
      this.scanner = new PermissionScanner({
        suppressWarnings: options.suppressWarnings,
      });

      // 1. 掃描權限
      console.log("🔍 第一步: 掃描權限定義");
      const scanResult = await this.scanner.scan(options.cache !== false);

      if (scanResult.errors.length > 0) {
        console.warn("⚠️  掃描過程中發現錯誤，但將繼續執行同步");
        if (options.verbose) {
          scanResult.errors.forEach((error) => console.warn(`  • ${error}`));
        }
      }

      // 2. 同步權限
      console.log("\n🔄 第二步: 同步權限到資料庫");
      const syncResult = await this.syncer.sync(scanResult.permissions, {
        dryRun: options.dryRun,
        force: options.force,
      });

      // 3. 產生報告
      console.log("\n📊 第三步: 產生同步報告");
      const report = await this.reporter.generateReport(
        scanResult,
        syncResult,
        options.dryRun ? "dry-run" : "sync"
      );

      // 4. 產生 Markdown 報告
      if (!options.dryRun) {
        await this.reporter.generateMarkdownReport(report);
      }

      // 5. 檢查同步結果
      if (syncResult.errors > 0) {
        console.warn(`\n⚠️  同步過程中發生 ${syncResult.errors} 個錯誤`);
        if (options.verbose) {
          syncResult.details.errorMessages.forEach((error) => {
            console.warn(`  • ${error}`);
          });
        }
      }

      console.log(`\n✅ ${options.dryRun ? "預覽" : "同步"}完成！`);

      // 在 dry-run 模式下，如果有變更，提示執行實際同步但不強制退出
      if (
        options.dryRun &&
        (syncResult.created > 0 ||
          syncResult.updated > 0 ||
          syncResult.deprecated > 0)
      ) {
        console.log("\n💡 提示: 發現權限變更，執行以下命令進行實際同步:");
        console.log("   pnpm db:sync-perms");
        if (options.force) {
          console.log("   或使用強制模式: pnpm db:sync-perms --force");
        }
        
        // 只在 CI 環境下才退出（通過環境變數判斷）
        if (process.env.CI || process.env.GITHUB_ACTIONS) {
          console.error('❌ 權限同步檢查失敗: 檢測到未同步的權限變更');
          process.exit(1);
        } else {
          console.log('ℹ️  在非 CI 環境下，預覽模式不會因檢測到變更而失敗');
        }
      }
    } catch (error) {
      console.error("❌ 同步失敗:", error.message);
      if (options.verbose) {
        console.error("詳細錯誤資訊:", error.stack);
      }
      process.exit(1);
    }
  }

  /**
   * 處理 report 命令
   */
  async handleReportCommand(
    options: Partial<CLIOptions>,
    format = "json"
  ): Promise<void> {
    try {
      console.log("📊 產生權限報告...\n"); // 讀取最新的同步報告
      const reportDir = this.reporter["reportDir"];
      const reportPath = require("path").join(
        reportDir,
        "permission-sync-report.json"
      );

      if (!require("fs").existsSync(reportPath)) {
        console.warn("⚠️  找不到同步報告，請先執行 scan 或 sync 命令");
        return;
      }

      const reportData = JSON.parse(
        require("fs").readFileSync(reportPath, "utf-8")
      );

      if (format === "markdown") {
        await this.reporter.generateMarkdownReport(reportData);
        console.log("✅ Markdown 報告已產生");
      } else {
        console.log("📄 JSON 報告位置:", reportPath);

        // 輸出簡要統計
        console.log("\n📈 報告摘要:");
        console.log(
          `  時間戳: ${new Date(reportData.timestamp).toLocaleString("zh-TW")}`
        );
        console.log(`  模式: ${reportData.mode}`);
        console.log(`  權限總數: ${reportData.summary.total}`);

        if (reportData.syncResult) {
          console.log(`  新增: ${reportData.syncResult.created}`);
          console.log(`  更新: ${reportData.syncResult.updated}`);
          console.log(`  廢棄: ${reportData.syncResult.deprecated}`);
        }
      }
    } catch (error) {
      console.error("❌ 產生報告失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 處理 validate 命令
   */
  async handleValidateCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      console.log("🔍 開始權限一致性驗證...\n");

      // 掃描權限
      const scanResult = await this.scanner.scan(options.cache !== false);

      // 執行驗證
      const validationResult = await this.validator.validate(scanResult.permissions);

      if (validationResult.isValid) {
        console.log("✅ 權限一致性驗證通過！");
        process.exit(0);
      } else {
        console.log("❌ 權限一致性驗證失敗");
        process.exit(1);
      }

    } catch (error) {
      console.error("❌ 驗證失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 處理 dev 命令
   */
  async handleDevCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      await this.devTools.startDevMode();
    } catch (error) {
      console.error("❌ 開發模式啟動失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 處理 check 命令
   */
  async handleCheckCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      const isValid = await this.devTools.quickCheck();
      process.exit(isValid ? 0 : 1);
    } catch (error) {
      console.error("❌ 快速檢查失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 處理 stats 命令
   */
  async handleStatsCommand(options: Partial<CLIOptions>): Promise<void> {
    try {
      await this.devTools.generateUsageStats();
    } catch (error) {
      console.error("❌ 統計生成失敗:", error.message);
      process.exit(1);
    }
  }

  /**
   * 清理資源
   */
  async cleanup(): Promise<void> {
    await this.syncer.disconnect();
  }
}

/**
 * 主程式入口
 */
async function main() {
  const cli = new PermissionSyncCLI();
  const program = cli.initializeCommands();

  // 處理未捕獲的錯誤
  process.on("uncaughtException", async (error) => {
    console.error("未捕獲的例外:", error);
    await cli.cleanup();
    process.exit(1);
  });

  process.on("unhandledRejection", async (reason, promise) => {
    console.error("未處理的 Promise 拒絕:", reason);
    await cli.cleanup();
    process.exit(1);
  });

  // 處理程式結束
  process.on("SIGINT", async () => {
    console.log("\n收到中斷信號，正在清理...");
    await cli.cleanup();
    process.exit(0);
  });

  process.on("SIGTERM", async () => {
    console.log("\n收到終止信號，正在清理...");
    await cli.cleanup();
    process.exit(0);
  });

  try {
    await program.parseAsync(process.argv);
    await cli.cleanup();
  } catch (error) {
    console.error("執行命令時發生錯誤:", error.message);
    await cli.cleanup();
    process.exit(1);
  }
}

// 如果直接執行此檔案
if (require.main === module) {
  main();
}

export { PermissionSyncCLI, main };
