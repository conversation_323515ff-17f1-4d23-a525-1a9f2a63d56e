import { PrismaClient } from '@prisma/client';
import { PermissionDefinition } from './types';
import * as fs from 'fs';
import * as path from 'path';

/**
 * 權限一致性驗證結果
 */
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
  summary: {
    totalChecks: number;
    passedChecks: number;
    failedChecks: number;
    warningCount: number;
  };
}

export interface ValidationError {
  type: 'MISSING_PERMISSION' | 'ORPHANED_PERMISSION' | 'INVALID_REFERENCE' | 'DUPLICATE_PERMISSION';
  message: string;
  details: any;
  severity: 'high' | 'medium' | 'low';
}

export interface ValidationWarning {
  type: 'UNUSED_PERMISSION' | 'HARDCODED_PERMISSION' | 'DEPRECATED_USAGE';
  message: string;
  details: any;
}

/**
 * 權限一致性驗證器
 * 確保程式碼中的權限使用與資料庫定義保持一致
 */
export class PermissionValidator {
  private prisma: PrismaClient;

  constructor(prisma?: PrismaClient) {
    this.prisma = prisma || new PrismaClient();
  }

  /**
   * 執行完整的權限一致性驗證
   */
  async validate(scannedPermissions: PermissionDefinition[]): Promise<ValidationResult> {
    const errors: ValidationError[] = [];
    const warnings: ValidationWarning[] = [];
    let totalChecks = 0;

    console.log('🔍 開始權限一致性驗證...');

    // 1. 檢查資料庫中的權限
    const dbPermissions = await this.getDbPermissions();
    
    // 2. 檢查遺失的權限
    totalChecks++;
    const missingPermissions = await this.checkMissingPermissions(scannedPermissions, dbPermissions);
    errors.push(...missingPermissions);

    // 3. 檢查孤立的權限
    totalChecks++;
    const orphanedPermissions = await this.checkOrphanedPermissions(scannedPermissions, dbPermissions);
    warnings.push(...orphanedPermissions);

    // 4. 檢查重複的權限定義
    totalChecks++;
    const duplicatePermissions = await this.checkDuplicatePermissions(scannedPermissions);
    errors.push(...duplicatePermissions);

    // 5. 檢查硬編碼權限使用
    totalChecks++;
    const hardcodedUsages = await this.checkHardcodedPermissions(scannedPermissions);
    warnings.push(...hardcodedUsages);

    // 6. 檢查廢棄權限的使用
    totalChecks++;
    const deprecatedUsages = await this.checkDeprecatedPermissions(scannedPermissions, dbPermissions);
    warnings.push(...deprecatedUsages);

    const passedChecks = totalChecks - errors.length;
    const isValid = errors.length === 0;

    const result: ValidationResult = {
      isValid,
      errors,
      warnings,
      summary: {
        totalChecks,
        passedChecks,
        failedChecks: errors.length,
        warningCount: warnings.length
      }
    };

    this.logValidationResult(result);
    return result;
  }

  /**
   * 獲取資料庫中的權限
   */
  private async getDbPermissions(): Promise<any[]> {
    return await this.prisma.permissions.findMany({
      select: {
        id: true,
        action: true,
        subject: true,
        description: true,
        deprecated: true,
        createdAt: true,
        updatedAt: true
      }
    });
  }

  /**
   * 檢查遺失的權限（程式碼中使用但資料庫中不存在）
   */
  private async checkMissingPermissions(
    scannedPermissions: PermissionDefinition[],
    dbPermissions: any[]
  ): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];
    const dbPermissionKeys = new Set(
      dbPermissions.map(p => `${p.action}:${p.subject}`)
    );

    for (const permission of scannedPermissions) {
      const key = `${permission.action}:${permission.subject}`;
      if (!dbPermissionKeys.has(key)) {
        errors.push({
          type: 'MISSING_PERMISSION',
          message: `權限 ${key} 在程式碼中使用但資料庫中不存在`,
          details: {
            permission,
            location: `${permission.filePath}:${permission.lineNumber}`
          },
          severity: 'high'
        });
      }
    }

    return errors;
  }

  /**
   * 檢查孤立的權限（資料庫中存在但程式碼中未使用）
   */
  private async checkOrphanedPermissions(
    scannedPermissions: PermissionDefinition[],
    dbPermissions: any[]
  ): Promise<ValidationWarning[]> {
    const warnings: ValidationWarning[] = [];
    const scannedPermissionKeys = new Set(
      scannedPermissions.map(p => `${p.action}:${p.subject}`)
    );

    for (const dbPermission of dbPermissions) {
      const key = `${dbPermission.action}:${dbPermission.subject}`;
      if (!scannedPermissionKeys.has(key) && !dbPermission.deprecated) {
        warnings.push({
          type: 'UNUSED_PERMISSION',
          message: `權限 ${key} 在資料庫中存在但程式碼中未使用`,
          details: {
            permission: dbPermission
          }
        });
      }
    }

    return warnings;
  }

  /**
   * 檢查重複的權限定義
   */
  private async checkDuplicatePermissions(
    scannedPermissions: PermissionDefinition[]
  ): Promise<ValidationError[]> {
    const errors: ValidationError[] = [];
    const permissionMap = new Map<string, PermissionDefinition[]>();

    // 按 action:subject 分組
    for (const permission of scannedPermissions) {
      const key = `${permission.action}:${permission.subject}`;
      if (!permissionMap.has(key)) {
        permissionMap.set(key, []);
      }
      permissionMap.get(key)!.push(permission);
    }

    // 檢查重複
    for (const [key, permissions] of permissionMap) {
      if (permissions.length > 1) {
        errors.push({
          type: 'DUPLICATE_PERMISSION',
          message: `權限 ${key} 有重複定義`,
          details: {
            duplicates: permissions.map(p => ({
              location: `${p.filePath}:${p.lineNumber}`,
              description: p.description
            }))
          },
          severity: 'medium'
        });
      }
    }

    return errors;
  }

  /**
   * 檢查硬編碼權限使用
   */
  private async checkHardcodedPermissions(
    scannedPermissions: PermissionDefinition[]
  ): Promise<ValidationWarning[]> {
    const warnings: ValidationWarning[] = [];

    for (const permission of scannedPermissions) {
      // 檢查是否為硬編碼（沒有從常數引用）
      if (!permission.description?.includes('常數引用') && 
          !permission.description?.includes('權限常數')) {
        warnings.push({
          type: 'HARDCODED_PERMISSION',
          message: `權限 ${permission.action}:${permission.subject} 使用硬編碼字串`,
          details: {
            permission,
            location: `${permission.filePath}:${permission.lineNumber}`,
            suggestion: '建議使用 @horizai/permissions 常數'
          }
        });
      }
    }

    return warnings;
  }

  /**
   * 檢查廢棄權限的使用
   */
  private async checkDeprecatedPermissions(
    scannedPermissions: PermissionDefinition[],
    dbPermissions: any[]
  ): Promise<ValidationWarning[]> {
    const warnings: ValidationWarning[] = [];
    const deprecatedPermissions = new Set(
      dbPermissions
        .filter(p => p.deprecated)
        .map(p => `${p.action}:${p.subject}`)
    );

    for (const permission of scannedPermissions) {
      const key = `${permission.action}:${permission.subject}`;
      if (deprecatedPermissions.has(key)) {
        warnings.push({
          type: 'DEPRECATED_USAGE',
          message: `使用了已廢棄的權限 ${key}`,
          details: {
            permission,
            location: `${permission.filePath}:${permission.lineNumber}`
          }
        });
      }
    }

    return warnings;
  }

  /**
   * 記錄驗證結果
   */
  private logValidationResult(result: ValidationResult): void {
    console.log('\n📊 權限一致性驗證結果:');
    console.log(`  ✅ 通過檢查: ${result.summary.passedChecks}/${result.summary.totalChecks}`);
    console.log(`  ❌ 失敗檢查: ${result.summary.failedChecks}`);
    console.log(`  ⚠️  警告數量: ${result.summary.warningCount}`);

    if (result.errors.length > 0) {
      console.log('\n❌ 發現的錯誤:');
      result.errors.forEach((error, index) => {
        console.log(`  ${index + 1}. [${error.severity.toUpperCase()}] ${error.message}`);
        if (error.details.location) {
          console.log(`     位置: ${error.details.location}`);
        }
      });
    }

    if (result.warnings.length > 0) {
      console.log('\n⚠️  發現的警告:');
      result.warnings.forEach((warning, index) => {
        console.log(`  ${index + 1}. ${warning.message}`);
        if (warning.details.location) {
          console.log(`     位置: ${warning.details.location}`);
        }
      });
    }

    if (result.isValid) {
      console.log('\n✅ 權限一致性驗證通過！');
    } else {
      console.log('\n❌ 權限一致性驗證失敗，請修復上述錯誤');
    }
  }
}
