# Task ID: 17
# Title: Create LLM Provider Abstraction Layer
# Status: pending
# Dependencies: 6
# Priority: medium
# Description: Design a service or interface that abstracts interactions with different LLM providers (OpenAI, Anthropic, Gemini, local models), allowing seamless switching.
# Details:
Define common interface `ILlmService { generate(prompt: string, modelConfig: AiModel, apiKey: string): Promise<string>; }`. Implement concrete classes (`OpenAiLlmService`, `AnthropicLlmService`). `AgentRunnerService` uses this abstraction based on `AiModel` config. This may involve wrapping LangChain's provider classes for central key/config management.

# Test Strategy:
Unit tests for each provider implementation (mock API calls). Test switching providers via `AiModel` settings.
