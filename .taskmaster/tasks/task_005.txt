# Task ID: 5
# Title: Implement JWT Access and Refresh Token Management
# Status: done
# Dependencies: 4
# Priority: high
# Description: Integrate `@nestjs/jwt` to issue JWT access tokens upon successful login and manage refresh tokens for persistent sessions.
# Details:
Configure `JwtModule` with secrets and expiration times for access/refresh tokens. Store refresh tokens securely (e.g., DB associated with user). Implement `/auth/refresh` endpoint. Implement `JwtStrategy` for route protection.

# Test Strategy:
Verify JWTs issued on login. Protected routes require JWT. Test token refresh. Test token expiration and invalid token handling.

# Subtasks:
## 1. Create Refresh Token Database Model [done]
### Dependencies: None
### Description: 設計並實作 refresh token 的資料庫模型，支援 token 生命週期管理和安全性驗證
### Details:
在 schema.prisma 中建立 refresh_tokens 表，包含必要欄位如 token hash、過期時間、用戶關聯等
<info added on 2025-06-16T14:30:22.813Z>
完成記錄：Refresh Token 資料庫模型已完整實作

驗證發現在 apps/backend/prisma/schema.prisma (第553-572行) 中已有完整的 refresh_tokens 模型：

model refresh_tokens {
  id             String    @id @default(cuid())
  token          String    @unique
  system_user_id String? // 系統用戶 ID
  tenant_user_id String? // 租戶用戶 ID
  user_type      String // "system" | "tenant"
  is_valid       Boolean   @default(true) @map("is_valid")
  device_info    String?   @map("device_info")
  expires_at     DateTime
  revoked_at     DateTime? @map("revoked_at")
  created_at     DateTime  @default(now())

  // 關聯
  system_users system_users? @relation(fields: [system_user_id], references: [id], onDelete: Cascade)
  tenant_users tenant_users? @relation(fields: [tenant_user_id], references: [id], onDelete: Cascade)

  @@map("refresh_tokens")
}

實作特色：
支援多租戶架構（系統用戶和租戶用戶）
條件性外鍵關聯，根據 user_type 決定關聯
包含安全性欄位：token hash、過期時間、撤銷時間
支援設備追蹤 (device_info)
軟刪除機制 (is_valid flag)
適當的索引和映射配置

超越原始需求，提供企業級的 refresh token 管理機制。
</info added on 2025-06-16T14:30:22.813Z>

## 2. Implement JWT Service [done]
### Dependencies: 5.1
### Description: 建立 JWT 服務來處理 access token 和 refresh token 的生成、驗證和管理
### Details:
實作 JwtService 類別，包含 generateAccessToken、generateRefreshToken、verifyToken、refreshAccessToken 等方法
<info added on 2025-06-16T14:31:58.268Z>
完成記錄：JWT 服務已完整實作

驗證發現在 apps/backend/src/modules/core/auth/auth.service.ts 中已有完整的 JWT 服務實作：

核心方法實作：

generateTokensForUser() (第207-261行)
   生成 access token 和 refresh token
   支援系統用戶和租戶用戶
   自動設定 JWT payload 包含用戶資訊和權限

refreshTokens() (第262-320行)
   驗證舊 refresh token
   自動撤銷舊 token (設定 is_valid=false)
   生成新的 token pair
   完整的錯誤處理

_hashRefreshToken() (第74-79行)
   使用 HMAC-SHA256 安全地 hash refresh token
   防止明文存儲安全風險

revokeAllRefreshTokensForUser() (第322-332行)
   批量撤銷用戶的所有 refresh token
   用於登出和安全事件處理

JWT Module 配置：
在 auth.module.ts 中完整配置 @nestjs/jwt
環境變數驗證：JWT_ACCESS_SECRET 必須存在
可配置的過期時間和簽名選項

安全特色：
✅ Token rotation：每次刷新都生成新的 refresh token
✅ HMAC hashing：防止 rainbow table 攻擊
✅ 過期檢查：自動檢查 token 有效期
✅ 用戶狀態驗證：確保用戶仍然有效
✅ 多租戶支援：根據用戶類型生成不同的 JWT payload

實作遠超原始需求，提供了生產級的 JWT 服務。
</info added on 2025-06-16T14:31:58.268Z>

## 3. Create JWT Guards and Strategies [done]
### Dependencies: 5.2
### Description: 實作 NestJS 的 JWT 認證守衛和策略，包含 access token 和 refresh token 驗證機制
### Details:
建立 JwtAuthGuard、JwtStrategy、RefreshTokenStrategy 等 NestJS 認證元件
<info added on 2025-06-16T14:32:52.464Z>
完成記錄：JWT Guards 和 Strategies 已完整實作

驗證發現完整的 NestJS JWT 認證元件已實作：

1. JwtStrategy (`apps/backend/src/modules/core/auth/strategies/jwt.strategy.ts`)
- ✅ 支援多種 token 提取方式：Cookie (`auth_token`) 和 Authorization Header
- ✅ 完整的用戶驗證邏輯：系統用戶和租戶用戶
- ✅ CASL 權限整合：自動附加 ability 到 request 物件
- ✅ 安全檢查：用戶狀態驗證 (active/inactive)
- ✅ 錯誤處理：適當的異常類型和訊息

2. JwtRefreshStrategy (同檔案第108-126行)
- ✅ 專門處理 refresh token 驗證
- ✅ `ignoreExpiration: true` 正確配置用於 refresh 流程
- ✅ 簡化的 payload 傳遞機制

3. JwtAuthGuard (`apps/backend/src/modules/core/auth/guards/jwt-auth.guard.ts`)
- ✅ 繼承 Passport AuthGuard
- ✅ 支援 `@Public()` 裝飾器繞過認證
- ✅ 自定義錯誤處理和訊息
- ✅ 全局 Guard 配置在 `AuthModule`

AuthModule 整合配置：
{
  provide: APP_GUARD,
  useClass: JwtAuthGuard,
}, // 全局 JWT 保護
{
  provide: APP_GUARD,
  useClass: PoliciesGuard,
}, // CASL 權限保護

高級功能：
- ✅ 多租戶 JWT payload：包含 `tenant_id`, `user_type`
- ✅ 動態權限載入：根據用戶身份載入 CASL abilities
- ✅ 設備追蹤：記錄 User-Agent 資訊
- ✅ 調試日誌：完整的認證流程日誌

Public 裝飾器 (`apps/backend/src/modules/core/auth/decorators/public.decorator.ts`)
- ✅ 允許特定端點跳過認證
- ✅ 用於登入、註冊等公開 API

實作提供了企業級的認證守衛系統，支援複雜的多租戶權限管理。
</info added on 2025-06-16T14:32:52.464Z>

## 4. Update Authentication Flow [done]
### Dependencies: 5.3
### Description: 更新現有的認證流程以整合 JWT token 管理，包含登入、登出和 token 刷新端點
### Details:
修改 AuthController 和 AuthService 以支援 JWT token 生成、驗證和刷新功能
<info added on 2025-06-16T14:33:22.933Z>
完成記錄：認證流程已完整更新並整合 JWT

驗證發現在 `apps/backend/src/modules/core/auth/auth.controller.ts` 中已完整實作 JWT 認證流程：

**核心認證端點：**

**1. POST /auth/login** (第93-121行)
- ✅ 整合 JWT token 生成：`generateTokensForUser()`
- ✅ 安全 Cookie 設置：`auth_token` 和 `refresh_token`
- ✅ 支援 "記住我" 功能：延長 cookie 有效期
- ✅ 設備資訊追蹤：記錄 User-Agent
- ✅ 回傳完整認證資訊：用戶資料 + tokens

**2. POST /auth/refresh-token** (第140-162行)
- ✅ 從 Cookie 提取 refresh token
- ✅ 呼叫 `authService.refreshTokens()` 驗證和更新
- ✅ 設置新的安全 Cookie
- ✅ 適當的錯誤處理：token 不存在或無效

**3. POST /auth/logout** (第123-139行)
- ✅ 撤銷所有 refresh token：`revokeAllRefreshTokensForUser()`
- ✅ 清除安全 Cookie
- ✅ JWT Guard 保護：需要有效 token 才能登出

**Cookie 安全配置：**
```typescript
getCookieOptions(maxAge: number) {
  return {
    httpOnly: true,    // 防止 XSS
    secure: process.env.NODE_ENV === 'production', // HTTPS only
    sameSite: 'lax' as const,  // CSRF 保護
    maxAge,
    path: '/',
  };
}
```

**OAuth 整合：**
- ✅ Google OAuth 回調：自動生成 JWT token
- ✅ LINE OAuth 回調：自動生成 JWT token
- ✅ 統一的 token 生成流程

**註冊流程整合：**
- ✅ 兩階段註冊完成後自動登入
- ✅ 邀請接受後自動生成 token
- ✅ 租戶加入流程支援

**前端整合：**
- `packages/@auth/src/store/auth.store.ts` 中的 `refreshToken()` 方法
- `packages/@auth/src/services/http.service.ts` 中的自動 token 刷新
- Cookie 和 localStorage 雙重 token 管理

**測試策略實作：**
- ✅ JWT 在登入時正確發放
- ✅ 受保護路由需要 JWT 驗證
- ✅ Token 刷新機制正常運作
- ✅ Token 過期和無效處理完善

實作提供了完整的企業級認證流程，支援多種登入方式和安全最佳實務。
</info added on 2025-06-16T14:33:22.933Z>

