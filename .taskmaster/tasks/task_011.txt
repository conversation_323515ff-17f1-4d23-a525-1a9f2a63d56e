# Task ID: 11
# Title: Develop `KnowledgeBaseTool` for Agent RAG Queries
# Status: pending
# Dependencies: 7, 9
# Priority: high
# Description: Create a LangChain `Tool` that allows the Agent to query the RAG-indexed knowledge base (files, etc.) using LlamaIndex, ensuring tenant isolation.
# Details:
Create `KnowledgeBaseTool extends Tool`. `name = "KnowledgeBaseTool"`, `description = "Queries knowledge base..."`. `_call(input: string): Promise<string>` uses a RAG query service (encapsulating LlamaIndex querying with `tenant_id` filter from Task 9). Return query results as string.

# Test Strategy:
Unit test `KnowledgeBaseTool._call` with mock RAG query service. Integration test: Agent uses tool to retrieve info from indexed docs, respecting tenant boundaries.
