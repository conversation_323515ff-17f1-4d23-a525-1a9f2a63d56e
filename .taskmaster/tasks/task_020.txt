# Task ID: 20
# Title: Develop System-Wide Audit Logging
# Status: pending
# Dependencies: 4
# Priority: medium
# Description: Create a comprehensive audit trail for significant user actions (logins, resource creation/modification/deletion, settings changes).
# Details:
Prisma schema for `AuditLog`: `id`, `userId`, `action String`, `details Json?`, `ipAddress String?`, `timestamp DateTime`, `tenantId String?`. Create `AuditLogService`. Use NestJS interceptors/decorators for automatic logging.

# Test Strategy:
Perform user actions, verify audit logs created with correct details. Ensure no sensitive data logged.
