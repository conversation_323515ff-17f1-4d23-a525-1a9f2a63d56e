# Task ID: 10
# Title: Develop `ProjectInfoTool` for Agent
# Status: pending
# Dependencies: 7
# Priority: high
# Description: Create a LangChain `Tool` that allows the Agent to query project status and details by wrapping functionalities of the existing `projects.service`.
# Details:
Create `ProjectInfoTool extends Tool` in `apps/backend/src/modules/agent/tools/`. `name = "ProjectInfoTool"`, `description = "Queries project info..."`. `_call(input: string): Promise<string>` should use `projects.service` (to be created/injected) to find project by name/ID, scoped by `tenantId` (passed contextually). Return project data as string.

# Test Strategy:
Unit test `ProjectInfoTool._call` with mock `projects.service`. Integration test: Agent uses tool to retrieve (mocked) project data.
