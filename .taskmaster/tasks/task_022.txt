# Task ID: 22
# Title: Develop `UpdateProgressTool` / `CreateProgressEntryTool` for Agent
# Status: pending
# Dependencies: 12
# Priority: medium
# Description: Create a <PERSON><PERSON><PERSON>n `Tool` for the Agent to update project progress or create progress entries, e.g., after analyzing工地照片.
# Details:
Create `UpdateProgressTool extends Tool`. `name = "UpdateProgressTool"`, `description = "Updates project/task progress..."`. `_call(input: string): Promise<string>`: Parse input for project/task ID, progress details. Call `progress.service.updateProgress({ ..., tenantId })`. Return success message.

# Test Strategy:
Unit test `UpdateProgressTool._call`. Integration test: Agent uses tool to update (mocked) progress.
