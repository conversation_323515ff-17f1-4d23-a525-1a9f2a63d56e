# Task ID: 16
# Title: Develop Tenant-Specific Settings Management
# Status: pending
# Dependencies: 2, 13
# Priority: medium
# Description: Allow Tenant Admins to configure tenant-level settings and preferences (e.g., default AI model, notification settings).
# Details:
Extend `Tenant` model with `settingsJson Json?` or create `TenantSetting` model (`tenantId String @unique`, `settings Json`). Create API endpoints for Tenant Admins (protected by CASL) to update these settings.

# Test Strategy:
CRUD tests for tenant settings. Verify settings are correctly applied (e.g., default AI model used by agents in that tenant).
