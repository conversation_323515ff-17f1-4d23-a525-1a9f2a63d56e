# Task ID: 12
# Title: Implement Basic Agent Execution Flow in `AgentRunnerService`
# Status: pending
# Dependencies: 7, 10, 11
# Priority: high
# Description: Enhance `AgentRunnerService` to initialize a LangChain agent (e.g., OpenAI Functions Agent) with an LLM (from `AiModel` config) and the implemented tools (`ProjectInfoTool`, `KnowledgeBaseTool`). Execute the agent with user input.
# Details:
`AgentRunnerService.runAgent`: Fetch `AiModel` config. Initialize LLM (e.g., `ChatOpenAI`) with API key. Instantiate tools, passing `tenantId` context. Define prompt. Create LangChain agent (e.g., `createOpenAIFunctionsAgent`). Create `AgentExecutor`. Invoke agent with `userInput`. Return `result.output`.

# Test Strategy:
Unit test `AgentRunnerService.runAgent` mocking LLM/tool calls. Integration test: query triggers a tool, response generated. Log intermediate steps.
