# Task ID: 13
# Title: Implement Role-Based Access Control (RBAC) with CASL
# Status: pending
# Dependencies: 4
# Priority: medium
# Description: Integrate CASL (`@casl/ability`) for fine-grained permission control based on user roles (System Admin, Tenant Admin, Tenant User). Define abilities for various resources.
# Details:
Install `@casl/ability`, `@casl/prisma`. Define `CaslAbilityFactory` in `apps/backend/src/auth/casl/`. Example: `defineAbilityFor(user: User) { const { can, build } = new AbilityBuilder(Ability); if (user.role === UserRole.SYSTEM_ADMIN) can('manage', 'all'); else if (user.role === UserRole.TENANT_ADMIN) can('manage', 'Workspace', { tenantId: user.tenantId }); ... return build(); }`. Create CASL guards and apply to controllers.

# Test Strategy:
Unit tests for `CaslAbilityFactory` per role. Integration tests: attempt resource access with different roles, verify permissions.
