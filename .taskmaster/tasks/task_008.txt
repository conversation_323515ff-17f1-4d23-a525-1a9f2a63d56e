# Task ID: 8
# Title: Implement `RAGIngestionService` for File Indexing
# Status: pending
# Dependencies: 1, 2
# Priority: high
# Description: Create `RAGIngestionService` to listen for file upload events (e.g., from `files.service`). Use LlamaIndex.js to process and index content of uploaded files (initially PDFs, images) into a vector store.
# Details:
Choose and setup vector DB (e.g., PGVector with `prisma-extension-pgvector`). Create `rag-ingestion.service.ts`. `@OnEvent('file.uploaded') async handleFileUpload(event: { filePath: string, tenantId: string, fileId: string })`. Logic: load file content, create LlamaIndex `Document` with `metadata: { tenant_id: event.tenantId, file_id: event.fileId }`, index into vector store. Requires `EventEmitterModule` and `files.service` to emit event.

# Test Strategy:
Unit test `handleFileUpload` with mock file/vector store. Verify event listener triggers. Check vector DB for indexed documents with correct metadata.
