# Task ID: 9
# Title: Enforce `tenant_id` Isolation in RAG Pipeline
# Status: pending
# Dependencies: 8
# Priority: high
# Description: Ensure that all data indexed by `RAGIngestionService` includes a `tenant_id` in its metadata. Modify RAG query mechanisms (e.g., in `KnowledgeBaseTool`) to *always* filter by the current user's `tenant_id`.
# Details:
Indexing: Confirm `metadata: { tenant_id: event.tenantId, ... }` in LlamaIndex `Document` (Task 8). Querying: Implement metadata filtering in LlamaIndex retrieval (e.g., `retriever.retrieve({ query: queryText, filters: { tenant_id: tenantId } })`). Research exact mechanism for chosen vector store/LlamaIndex version (e.g., for PGVector, `WHERE metadata_->>'tenant_id' = $1`).

# Test Strategy:
Index data for Tenant A & B. Query as Tenant A, verify only A's data. Query as Tenant B, verify only B's data. Test for no data leakage with incorrect/missing `tenant_id`.
