# Task ID: 14
# Title: Add Google OAuth and LINE Login Options
# Status: pending
# Dependencies: 4
# Priority: medium
# Description: Integrate Passport.js strategies for Google OAuth 2.0 and LINE Login to provide alternative sign-in methods.
# Details:
Install `passport-google-oauth20` and `passport-line`. Configure strategies with client IDs/secrets. Implement callback routes for auth success/failure. Logic to find/create user accounts from OAuth profile.

# Test Strategy:
Configure test OAuth apps. Manually test login flows. Verify user accounts correctly created/linked.
