# Task ID: 7
# Title: Setup `agent` <PERSON><PERSON><PERSON> & `AgentRunnerService` Shell
# Status: done
# Dependencies: 1, 6
# Priority: high
# Description: Establish the core `agent` module in NestJS. Create the `AgentRunnerService` class responsible for initializing and running LangChain agents.
# Details:
Create `apps/backend/src/modules/agent/agent.module.ts` and `agent.service.ts` (`AgentRunnerService`). `AgentRunnerService` shell: `async runAgent(userInput: string, tenantId: string, agentConfigId?: string): Promise<string> { /* Placeholder logic */ }`. Inject dependencies like `AiModelService` later.

# Test Strategy:
Verify `agent` module loads. `AgentRunnerService` instantiates. `runAgent` method callable and returns placeholder.
