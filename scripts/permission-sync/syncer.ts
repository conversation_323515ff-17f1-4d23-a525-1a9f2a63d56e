  const category = await this.prisma.permission_categories.upsert({
    where: { name: categoryName },
    update: {},
    create: {
      name: categoryName,
      description: `Category for ${categoryName}`,
      icon: 'default_icon',
      sort_order: 0,
      is_active: true,
    },
  });

  if (category.created_at.getTime() === category.updated_at.getTime()) {
    this.log(`Created new permission category: ${categoryName}`);
  }

  // 獲取所有現存的 permission_categories
  const existingCategories = await this.prisma.permission_categories.findMany({
    where: {
      is_active: true,
    },
  });

  // 禁用數據庫中存在但程式碼中不存在的類別
  if (categoriesToDisable.length > 0) {
    await this.prisma.permission_categories.updateMany({
      where: {
        name: { in: categoriesToDisable },
      },
      data: {
        is_active: false,
      },
    });
    this.log(`Disabled ${categoriesToDisable.length} categories.`);
  }

  if (category && existingPermission.category_id !== category.id) {
    const dataToUpdate: Prisma.permissionsUpdateInput = {};
    if (needsUpdate) {
      if (descriptionChanged) dataToUpdate.description = description;
      if (fieldsChanged) dataToUpdate.fields = fields;
      if (scopeChanged) dataToUpdate.scope = scope;
      if (nameChanged) dataToUpdate.name = name;
      if (zoneChanged) dataToUpdate.zone = zone;
      if (categoryChanged)
        dataToUpdate.category_id = category ? category.id : null;

      await this.prisma.permissions.update({
        where: { id: existingPermission.id },
        data: dataToUpdate,
      });
    }

    const dataToCreate: Prisma.permissionsCreateInput = {
      action: permission.action,
      subject: permission.subject,
      description: description,
      fields: fields,
      scope: scope,
      name: name,
      zone: zone,
      category_id: category.id,
      is_system_defined: true,
    };

    await this.prisma.permissions.create({ data: dataToCreate });
  }

  // Step 4: 將程式碼中不再存在的系統定義權限標記為 deprecated
  const dbSystemPermissions = await this.prisma.permissions.findMany({
    where: {
      is_system_defined: true,
      deprecated: false,
    },
  });

  if (!isStillDefined) {
    permissionsToDeprecate.push(
      this.prisma.permissions.update({
        where: { id: dbPerm.id },
        data: { deprecated: true, updated_at: new Date() },
      }),
    );
  } 