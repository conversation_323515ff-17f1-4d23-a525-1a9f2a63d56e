# Operating System / IDE specific files
.DS_Store
Thumbs.db
ehthumbs.db
Desktop.ini
$RECYCLE.BIN/
.idea/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.cache/

# Dependency Management
node_modules/
.pnp/
.pnp.js
.yarn/install-state.gz
pnpm-lock.yaml
package-lock.json
yarn.lock

# Build / Distribution Outputs
dist/
build/
out/
*.tsbuildinfo
.next/ # Next.js build output (keep if potentially relevant)
.vercel/ # Vercel deployment output

# Environment Variables
.env
.env*.local
.env.development
.env.test
.env.production

# Logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# Testing & Coverage Reports
coverage/
*.lcov
.nyc_output/

# Temporary Files
*.tmp
*.temp

# Uploads (Adjust path if necessary)
apps/backend/uploads/

# Security Related
*.pem

# Framework Specific / PWA
next-env.d.ts # Next.js specific (keep if potentially relevant)
public/sw.js
public/workbox-*.js
public/worker-*.js
public/sw.js.map
public/workbox-*.js.map
public/worker-*.js.map

# Virtual Environments (Python - if used)
venv/

# Prisma
.prisma/
prisma/node_modules/
# 保留 prisma/migrations/ 和 prisma/schema.prisma

.turbo/*

# Added by Task Master AI
logs
dev-debug.log
# Dependency directories
# Environment variables
# Editor directories and files
.idea
.vscode

# OS specific
# Task files
# tasks.json
# tasks/

# Added by Task Master AI
node_modules/
dist/
build/
out/
.env
.env.local
*.tsbuildinfo
.turbo
.DS_Store
*.log

# Added by Task Master AI
tasks.json
tasks/ 