# @horizai/auth

HorizAI 的認證套件，提供安全的 Cookie-based 認證機制。

## 特點

- 🔒 使用 HTTP-only Cookie 進行安全認證
- 🌐 完整的 CORS 支援
- 🔄 自動的認證狀態管理
- 📝 TypeScript 支援
- 🎯 Vue 3 整合

## 安裝

```bash
pnpm add @horizai/auth
```

## 快速開始

### 1. 初始化

```typescript
import { createApp } from "vue";
import { initAuth } from "@horizai/auth";
import App from "./App.vue";

const app = createApp(App);

const authConfig = {
  baseURL: import.meta.env.VITE_API_URL,
  loginEndpoint: "/api/auth/login",
  logoutEndpoint: "/api/auth/logout",
  refreshTokenEndpoint: "/api/auth/refresh",
  meEndpoint: "/api/auth/me",

  // 可選的回調函式
  onLoginSuccess: (response) => {
    console.log("登入成功", response);
  },
  onLogoutSuccess: () => {
    console.log("登出成功");
  },
};

// 初始化認證系統
await initAuth(app, authConfig);

app.mount("#app");
```

### 2. 在元件中使用

```vue
<script setup lang="ts">
import { useAuth } from "@horizai/auth";

const auth = useAuth();

const handleLogin = async () => {
  try {
    await auth.login({
      email: "<EMAIL>",
      password: "password",
      remember_me: true,
    });
    // 登入成功
  } catch (error) {
    // 處理錯誤
  }
};

const handleLogout = async () => {
  await auth.logout();
  // 登出成功
};

const isAuthenticated = auth.isAuthenticated();
</script>
```

## API 參考

### AuthConfig

```typescript
interface AuthConfig {
  baseURL: string;
  loginEndpoint: string;
  logoutEndpoint: string;
  refreshTokenEndpoint: string;
  meEndpoint: string;

  // 選填，在 Cookie 認證模式下不使用
  accessTokenKey?: string;
  refreshTokenKey?: string;

  skipPiniaInstallation?: boolean;
  rememberMeKey?: string;
  rememberMeExpiryDays?: number;

  // 回調函式
  onLoginSuccess?: (response: AuthResponse) => void;
  onLoginError?: (error: AuthError) => void;
  onLogoutSuccess?: () => void;
  onAuthStateChange?: (isAuthenticated: boolean) => void;
  tokenExpiryHandler?: () => void;
}
```

### AuthResponse

```typescript
interface AuthResponse {
  // 在 Cookie 認證模式下通常為空或由後端忽略
  accessToken?: string;
  refreshToken?: string;

  // 使用者資訊
  user: User;
}
```

### AuthService

主要的認證服務，提供以下方法：

- `login(credentials: LoginDto): Promise<AuthResponse>`
- `logout(): Promise<void>`
- `isAuthenticated(): boolean`
- `getUser(): Promise<User>`
- `refreshToken(): Promise<AuthResponse>`

### TokenService

處理 Cookie-based token 的服務：

- `isAuthenticated(): boolean` - 檢查是否已認證（基於 Cookie 存在與否）
- `getAuthToken(): string | null` - 嘗試從 Cookie 中讀取 Token（可能無法讀取 HTTP-only Cookie）
- `isTokenExpired(): boolean` - 檢查 Token 是否過期（基於 Cookie 存在與否）
- `clearAuth(): void` - 清除前端的認證相關資訊

### HttpService

處理 HTTP 請求的服務，自動支援 Cookie 認證、Token 刷新和錯誤處理：

#### 使用方式

1. **使用工廠函數建立實例**

```typescript
import { createHttpService } from '@horizai/auth';

// 建立 HTTP 服務實例，傳入基礎 URL
const httpService = createHttpService(window.location.origin);

// 使用實例發送請求
await httpService.get('/api/users');
```

2. **繼承 `DefaultHttpService` 建立服務類別**

```typescript
import { DefaultHttpService } from '@horizai/auth';

class UserService extends DefaultHttpService {
  constructor() {
    // 必須傳入基礎 URL
    super(window.location.origin);
  }
  
  async getUsers() {
    return this.get('/api/users');
  }
}
```

#### 可用方法

- `get<T>(url: string, config?): Promise<T>`
- `post<T>(url: string, data?, config?): Promise<T>`
- `put<T>(url: string, data?, config?): Promise<T>`
- `delete<T>(url: string, config?): Promise<T>`
- `patch<T>(url: string, data?, config?): Promise<T>`

#### 特性

- 自動處理 Token 刷新：當收到 401 錯誤時自動刷新 Token
- 統一錯誤處理：提供一致的錯誤處理機制
- 支援重試機制：對特定錯誤進行自動重試
- 請求攝底：自動設置 `withCredentials: true`

## 安全性考慮

1. **Cookie 安全**

   - 使用 HTTP-only Cookie 防止 JavaScript 存取
   - 在生產環境啟用 secure 標誌確保僅通過 HTTPS 傳送
   - 使用 SameSite 屬性防止 CSRF 攻擊

2. **CORS 設置**

   - 前端 HTTP 服務自動啟用 `withCredentials: true`
   - 後端需正確配置 CORS 策略，啟用 `credentials: true`

3. **錯誤處理**
   - 統一的錯誤處理機制
   - 詳細的錯誤日誌
   - 使用者友好的錯誤訊息

## 開發說明

### 本地開發

```bash
# 安裝相依套件
pnpm install

# 建構套件
pnpm build

# 執行測試
pnpm test
```

### 調試

1. 使用瀏覽器開發工具檢查 Cookie
2. 檢查網路請求中的 Cookie 標頭是否正確傳送
3. 啟用詳細日誌輸出
4. 確保後端 CORS 設定允許憑證（credentials）

## 貢獻指南

1. Fork 專案
2. 建立功能分支
3. 提交變更
4. 發起 Pull Request

## 授權

MIT
