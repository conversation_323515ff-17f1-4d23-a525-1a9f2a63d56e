// 共用 OAuth2 抽象層
import type { OAuthToken, OAuthUser } from '../../src/types/external-login.types'
import { getAuthConfig, getHttpServiceInstance } from '../../src/config'
import { createHttpService } from '../../src/services/http.service'

export interface OAuth2ProviderConfig {
  clientId: string
  clientSecret: string
  authorizeHost: string
  authorizePath: string
  tokenHost: string
  tokenPath: string
  userInfoEndpoint: string
  defaultScope: string
}

export abstract class OAuth2Provider {
  protected config: OAuth2ProviderConfig

  constructor(config: OAuth2ProviderConfig) {
    this.config = config
  }

  abstract getAuthorizeUrl(params: {
    redirectUri: string
    state: string
    scope?: string
  }): string

  abstract getToken(params: {
    code: string
    redirectUri: string
  }): Promise<OAuthToken>

  abstract getUserInfo(token: OAuthToken): Promise<OAuthUser>
}

// Google 實作骨架
export class GoogleOAuth2<PERSON>rovider extends OAuth2<PERSON>rovider {
  getAuthorizeUrl(params: { redirectUri: string; state: string; scope?: string }): string {
    const { redirectUri, state, scope } = params;
    const qs = new URLSearchParams({
      client_id: this.config.clientId,
      redirect_uri: redirectUri,
      response_type: 'code',
      scope: scope || this.config.defaultScope,
      state,
      access_type: 'offline',
      include_granted_scopes: 'true',
    });
    return `${this.config.authorizeHost}${this.config.authorizePath}?${qs.toString()}`;
  }
  async getToken(params: { code: string; redirectUri: string }): Promise<OAuthToken> {
    const config = getAuthConfig()
    const http = getHttpServiceInstance() ?? createHttpService(config.baseURL)
    const url = `${this.config.tokenHost}${this.config.tokenPath}`
    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      code: params.code,
      redirect_uri: params.redirectUri,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
    });
    return http.post<OAuthToken>(url, body.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
  }
  async getUserInfo(token: OAuthToken): Promise<OAuthUser> {
    const config = getAuthConfig()
    const http = getHttpServiceInstance() ?? createHttpService(config.baseURL)
    const data = await http.get<Record<string, unknown>>(this.config.userInfoEndpoint, {
      headers: { Authorization: `Bearer ${token.access_token}` },
    })
    return {
      id: data.sub || data.id,
      email: data.email,
      name: data.name,
      picture: data.picture,
      provider: 'google',
      ...data,
    } as OAuthUser
  }
}

// Line 實作骨架
export class LineOAuth2Provider extends OAuth2Provider {
  getAuthorizeUrl(params: { redirectUri: string; state: string; scope?: string }): string {
    const { redirectUri, state, scope } = params;
    const qs = new URLSearchParams({
      response_type: 'code',
      client_id: this.config.clientId,
      redirect_uri: redirectUri,
      state,
      scope: scope || this.config.defaultScope,
    });
    return `${this.config.authorizeHost}${this.config.authorizePath}?${qs.toString()}`;
  }
  async getToken(params: { code: string; redirectUri: string }): Promise<OAuthToken> {
    const config = getAuthConfig()
    const http = getHttpServiceInstance() ?? createHttpService(config.baseURL)
    const url = `${this.config.tokenHost}${this.config.tokenPath}`
    const body = new URLSearchParams({
      grant_type: 'authorization_code',
      code: params.code,
      redirect_uri: params.redirectUri,
      client_id: this.config.clientId,
      client_secret: this.config.clientSecret,
    })
    return http.post<OAuthToken>(url, body.toString(), {
      headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
    })
  }
  async getUserInfo(token: OAuthToken): Promise<OAuthUser> {
    const config = getAuthConfig()
    const http = getHttpServiceInstance() ?? createHttpService(config.baseURL)
    const data = await http.get<Record<string, unknown>>(this.config.userInfoEndpoint, {
      headers: { Authorization: `Bearer ${token.access_token}` },
    })
    return {
      id: data.sub || data.userId,
      name: data.name || data.displayName,
      picture: data.pictureUrl,
      provider: 'line',
      ...data,
    } as OAuthUser
  }
} 