import type { AuthConfig } from '../types/auth.types'

export interface TokenServiceInterface {
  getAuthToken(): string | null
  setAuthToken(token: string): void
  getRefreshToken(): string | null
  setRefreshToken(token: string): void
  clearTokens(): void
  isTokenExpired(token?: string): boolean
}

export class DefaultTokenService implements TokenServiceInterface {
  private config: AuthConfig

  constructor(config: AuthConfig) {
    this.config = config
  }

  getAuthToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.config.tokenStorageKey || 'auth_token')
  }

  setAuthToken(token: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.config.tokenStorageKey || 'auth_token', token)
  }

  getRefreshToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem(this.config.refreshTokenStorageKey || 'refresh_token')
  }

  setRefreshToken(token: string): void {
    if (typeof window === 'undefined') return
    localStorage.setItem(this.config.refreshTokenStorageKey || 'refresh_token', token)
  }

  clearTokens(): void {
    if (typeof window === 'undefined') return
    localStorage.removeItem(this.config.tokenStorageKey || 'auth_token')
    localStorage.removeItem(this.config.refreshTokenStorageKey || 'refresh_token')
  }

  isTokenExpired(token?: string): boolean {
    const tokenToCheck = token || this.getAuthToken()
    if (!tokenToCheck) return true

    try {
      const payload = JSON.parse(atob(tokenToCheck.split('.')[1]))
      const currentTime = Math.floor(Date.now() / 1000)
      return payload.exp < currentTime
    } catch {
      return true
    }
  }
}

export function createTokenService(config: AuthConfig): TokenServiceInterface {
  return new DefaultTokenService(config)
}