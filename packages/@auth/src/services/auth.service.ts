import type { LoginCredentials, User, AuthConfig } from '../types/auth.types'
import type { HttpService } from '../types/http.types'

export interface AuthService {
  login(credentials: LoginCredentials): Promise<{ user: User; ability_rules?: unknown[] }>
  logout(): Promise<void>
  getUser(): Promise<{ user: User; ability_rules?: unknown[] }>
  refreshToken(): Promise<{ accessToken: string }>
}

export function createAuthService(
  config: AuthConfig,
  httpService: HttpService
): AuthService {
  return {
    async login(credentials: LoginCredentials) {
      const response = await httpService.post<{ user: User; ability_rules?: unknown[] }>(
        config.loginEndpoint || '/api/auth/login',
        credentials
      )
      return response
    },

    async logout() {
      await httpService.post(config.logoutEndpoint || '/api/auth/logout')
    },

    async getUser() {
      const response = await httpService.get<{ user: User; ability_rules?: unknown[] }>(
        config.meEndpoint || '/api/auth/me'
      )
      return response
    },

    async refreshToken() {
      const response = await httpService.post<{ accessToken: string }>(
        config.refreshEndpoint || '/api/auth/refresh-token'
      )
      return response
    }
  }
}
