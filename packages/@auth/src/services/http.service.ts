import type { AuthConfig } from '../types/auth.types'
import { useAuthStore } from '../store/auth.store'

export interface HttpResponse<T = unknown> {
  data: T
  status: number
  statusText: string
  headers: Record<string, string>
}

export interface HttpRequestConfig {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  headers?: Record<string, string>
  body?: unknown
  timeout?: number
}

export interface HttpServiceInterface {
  get<T>(url: string, options?: RequestInit): Promise<T>
  post<T>(url: string, data?: unknown, options?: RequestInit): Promise<T>
  put<T>(url: string, data?: unknown, options?: RequestInit): Promise<T>
  delete<T>(url: string, options?: RequestInit): Promise<T>
  patch<T>(url: string, data?: unknown, options?: RequestInit): Promise<T>
}

export class DefaultHttpService implements HttpServiceInterface {
  private baseURL: string
  private authConfig: AuthConfig
  private defaultHeaders: Record<string, string>
  private isRefreshing = false
  private hasTriedRefresh = false // 防止無限刷新

  constructor(baseURL: string, authConfig: AuthConfig, defaultHeaders: Record<string, string> = {}) {
    this.baseURL = baseURL
    this.authConfig = authConfig
    this.defaultHeaders = defaultHeaders
  }

  private async request<T>(url: string, options: RequestInit = {}): Promise<T> {
    const authStore = useAuthStore()
    const token = authStore.token

    const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`
    
    const defaultHeaders: Record<string, string> = {
      'Content-Type': 'application/json',
    }

    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`
    }

    const config: RequestInit = {
      ...options,
      credentials: 'include', // 重要：包含 HttpOnly Cookie
      headers: {
        ...defaultHeaders,
        ...this.defaultHeaders,
        ...options.headers,
      },
    }

    try {
      const response = await fetch(fullUrl, config)
      
      if (!response.ok) {
        if (response.status === 401) {
          // 檢查是否是認證相關的端點，避免無限循環
          const isAuthEndpoint = url.includes('/auth/') || url.includes('auth/')
          const isRefreshRequest = url.includes('refresh-token')
          const isLogoutRequest = url.includes('logout')
          const isMeRequest = url.includes('/me') || url.includes('auth/me')
          
          // 如果是認證端點或正在刷新中，直接拋出錯誤
          if (isAuthEndpoint || this.isRefreshing) {
            throw new Error(`HTTP error! status: ${response.status}`)
          }

          // 只有在非認證端點且未嘗試過刷新時才嘗試刷新 token
          if (!this.hasTriedRefresh && !isRefreshRequest && !isLogoutRequest && !isMeRequest) {
            const refreshSuccess = await this.handleTokenRefresh()
            if (refreshSuccess) {
              // 重試原請求
              const retryResponse = await fetch(fullUrl, config)
              if (!retryResponse.ok) {
                throw new Error(`HTTP error! status: ${retryResponse.status}`)
              }
              return retryResponse.json()
            }
          }
          
          // 刷新失敗或不需要刷新，直接拋出錯誤
          throw new Error(`HTTP error! status: ${response.status}`)
        }
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 請求成功，重置刷新標記
      this.hasTriedRefresh = false
      return response.json()
    } catch (error) {
      throw new Error(`HTTP request failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }

  private async handleTokenRefresh(): Promise<boolean> {
    // 防止並發刷新
    if (this.isRefreshing || this.hasTriedRefresh) {
      return false
    }

    this.isRefreshing = true
    this.hasTriedRefresh = true

    try {
      const refreshUrl = `${this.baseURL}/${this.authConfig.refreshTokenEndpoint || 'auth/refresh-token'}`
      const response = await fetch(refreshUrl, {
        method: 'POST',
        credentials: 'include', // 包含 refresh_token Cookie
        headers: { 'Content-Type': 'application/json' },
      })

      if (response.ok) {
        // 刷新成功，新的 token 已經設定在 Cookie 中
        this.hasTriedRefresh = false // 重置標記，允許下次刷新
        return true
      } else {
        // 刷新失敗，觸發登出（但不會無限循環，因為 logout 是認證端點）
        if (this.authConfig.tokenExpiryHandler) {
          // 使用 setTimeout 避免阻塞當前請求
          setTimeout(() => {
            this.authConfig.tokenExpiryHandler?.()
          }, 0)
        }
        return false
      }
    } catch (_error) {
      // 刷新請求失敗
      if (this.authConfig.tokenExpiryHandler) {
        setTimeout(() => {
          this.authConfig.tokenExpiryHandler?.()
        }, 0)
      }
      return false
    } finally {
      this.isRefreshing = false
    }
  }

  async get<T>(url: string, options?: RequestInit): Promise<T> {
    return this.request<T>(url, { ...options, method: 'GET' })
  }

  async post<T>(url: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(url, {
      ...options,
      method: 'POST',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async put<T>(url: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(url, {
      ...options,
      method: 'PUT',
      body: data ? JSON.stringify(data) : undefined,
    })
  }

  async delete<T>(url: string, options?: RequestInit): Promise<T> {
    return this.request<T>(url, { ...options, method: 'DELETE' })
  }

  async patch<T>(url: string, data?: unknown, options?: RequestInit): Promise<T> {
    return this.request<T>(url, {
      ...options,
      method: 'PATCH',
      body: data ? JSON.stringify(data) : undefined,
    })
  }
}

export function createHttpService(baseURL: string, authConfig?: AuthConfig): HttpServiceInterface {
  const defaultAuthConfig: AuthConfig = {
    baseURL,
    ...authConfig,
  }
  
  return new DefaultHttpService(baseURL, defaultAuthConfig, {})
}
