import type { AbilityRule } from "../types/ability.types";

/**
 * 權限規則介面，對應後端回傳的權限規則格式
 */
export interface PermissionRule {
  action: string;
  subject: string;
  conditions?: Record<string, unknown>;
  fields?: string[];
  inverted?: boolean;
}

/**
 * Ability 工廠服務
 * 負責根據權限規則建立權限檢查實例
 */
export class AbilityService {
  /**
   * 根據權限規則建立權限檢查實例
   */
  static createAbility(rules: AbilityRule[]): { can: (action: string, subject: string) => boolean } {
    return {
      can: (action: string, subject: string) => {
        return rules.some(rule => 
          rule.action === action && rule.subject === subject
        )
      }
    }
  }

  /**
   * 序列化權限規則
   */
  static serializeRules(rules: AbilityRule[]): string {
    return JSON.stringify(rules)
  }

  /**
   * 反序列化權限規則
   */
  static deserializeRules(serializedRules: string): AbilityRule[] {
    try {
      const parsed = JSON.parse(serializedRules)
      if (Array.isArray(parsed)) {
        return parsed.filter(this.isValidRule)
      }
      return []
    } catch {
      return []
    }
  }

  /**
   * 檢查是否為有效的權限規則
   */
  private static isValidRule(rule: unknown): rule is AbilityRule {
    return (
      typeof rule === 'object' &&
      rule !== null &&
      'action' in rule &&
      'subject' in rule &&
      typeof (rule as Record<string, unknown>).action === 'string' &&
      typeof (rule as Record<string, unknown>).subject === 'string'
    )
  }

  /**
   * 檢查權限
   */
  static can(
    ability: { can: (action: string, subject: string) => boolean } | null,
    action: string,
    subject: string,
    _field?: string
  ): boolean {
    if (!ability) return false
    return ability.can(action, subject)
  }

  /**
   * 檢查是否沒有權限
   */
  static cannot(
    ability: { can: (action: string, subject: string) => boolean } | null,
    action: string,
    subject: string,
    _field?: string
  ): boolean {
    return !this.can(ability, action, subject, _field)
  }
}

/**
 * 建立權限工廠
 */
export function createAbilityFactory() {
  return AbilityService
}

/**
 * 獲取使用者權限範圍
 */
export function getUserPermissionScope(): "system" | "tenant" | "guest" {
  return "guest"
}

/**
 * 檢查是否為管理員
 */
export function isAdmin(): boolean {
  return false
}

/**
 * 檢查是否為系統管理員
 */
export function isSystemAdmin(): boolean {
  return false
}

/**
 * 檢查是否為租戶管理員
 */
export function isTenantAdmin(): boolean {
  return false
}
