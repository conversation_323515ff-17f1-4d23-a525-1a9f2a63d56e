/**
 * HTTP 服務的介面定義
 */

export interface HttpRequestConfig {
  headers?: Record<string, string>
  timeout?: number
  baseURL?: string
}

export interface HttpResponse<T = unknown> {
  data: T
  status: number
  statusText: string
  headers?: Record<string, string>
}

export interface HttpError {
  message: string
  status?: number
  statusText?: string
  data?: unknown
}

export interface HttpService {
  /**
   * 發送 GET 請求
   * @param url 請求 URL
   * @param config 請求配置
   * @returns 請求的結果
   */
  get<T = unknown>(url: string, config?: HttpRequestConfig): Promise<HttpResponse<T>>;

  /**
   * 發送 POST 請求
   * @param url 請求 URL
   * @param data 請求數據
   * @param config 請求配置
   * @returns 請求的結果
   */
  post<T = unknown>(url: string, data?: unknown, config?: HttpRequestConfig): Promise<HttpResponse<T>>;

  /**
   * 發送 PUT 請求
   * @param url 請求 URL
   * @param data 請求數據
   * @param config 請求配置
   * @returns 請求的結果
   */
  put<T = unknown>(url: string, data?: unknown, config?: HttpRequestConfig): Promise<HttpResponse<T>>;

  /**
   * 發送 DELETE 請求
   * @param url 請求 URL
   * @param config 請求配置
   * @returns 請求的結果
   */
  delete<T = unknown>(url: string, config?: HttpRequestConfig): Promise<HttpResponse<T>>;

  /**
   * 發送 PATCH 請求
   * @param url 請求 URL
   * @param data 請求數據
   * @param config 請求配置
   * @returns 請求的結果
   */
  patch<T = unknown>(url: string, data?: unknown, config?: HttpRequestConfig): Promise<HttpResponse<T>>;

  /**
   * 設置基礎 URL
   * @param base_url 基礎 URL
   */
  setBaseURL(baseURL: string): void;
  
  /**
   * 設置授權標頭
   * @param token 授權 token
   */
  setAuthToken(token: string | null): void;

  /**
   * 重置令牌刷新相關狀態
   * 用於登入成功或登出時，重置刷新計數器和狀態
   */
  reset_refresh_state(): void;

  /**
   * 設置登出狀態
   * @param isLoggingOut 是否正在登出
   */
  setLoggingOutState(isLoggingOut: boolean): void;
}
