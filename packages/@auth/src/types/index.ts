import { PureAbility, AbilityBuilder, AbilityClass, MongoQuery } from '@casl/ability'
import { Ref, ComputedRef } from 'vue'

export type Action = 'create' | 'read' | 'update' | 'delete' | 'manage'
export type Subject = 'all' | string

export type AppAbility = PureAbility<[Action, Subject], MongoQuery>
export type AppAbilityClass = AbilityClass<AppAbility>
export type AppAbilityBuilder = AbilityBuilder<AppAbility>

export interface AuthResponse {
  accessToken: string
  refreshToken: string
  user: User
}

export interface LoginDto {
  email: string
  password: string
}

export interface User {
  id: string
  email: string
  name: string
  role: string
  isTenantAdmin: boolean
  permissions: string[]
}

export interface TokenService {
  getAccessToken(): string | null
  getRefreshToken(): string | null
  setTokens(accessToken: string, refreshToken: string): void
  clearTokens(): void
}

export interface AuthStoreState {
  user: User | null
  isAuthenticated: boolean
  isLoading: boolean
  error: Error | null
  ability: AppAbility | null
}

export interface AuthStoreGetters {
  currentUser: User | null
  isLoggedIn: boolean
  hasError: boolean
  currentAbility: AppAbility | null
}

export interface AuthStoreActions {
  setUser(user: User | null): void
  setLoading(loading: boolean): void
  setError(error: Error | null): void
  resetState(): void
  updateAbility(user: User): void
  clearAbility(): void
}

export type AuthStore = AuthStoreState & {
  getters: AuthStoreGetters
  actions: AuthStoreActions
}

export interface UseAuthReturn {
  // State (readonly refs and computed)
  user: Readonly<Ref<User | null>>
  isAuthenticated: Readonly<Ref<boolean>>
  loading: Readonly<Ref<boolean>>
  error: Readonly<Ref<string | null>>
  ability: Readonly<Ref<unknown>>
  isLoggedIn: ComputedRef<boolean>
  currentUser: ComputedRef<User | null>
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<any>
  logout: () => Promise<void>
  fetchCurrentUser: () => Promise<any>
  refreshToken: () => Promise<boolean>
  initialize: () => Promise<void>
  clearError: () => void
}

export interface AuthState {
  user: User | null
  token: string | null
  isAuthenticated: boolean
  ability: AppAbility | null
}

export interface LoginCredentials {
  email: string
  password: string
  tenantId?: string
  /** 對應後端的 remember_me 欄位 */
  remember_me?: boolean
}

export interface LoginResponse {
  user: User
  token: string
  refreshToken?: string
}

export interface RefreshTokenResponse {
  token: string
  refreshToken?: string
}

export interface Permission {
  action: Action
  subject: Subject
}

export * from './auth.types'
export * from './ability.types'
export * from './token.types'
export * from './http.types' 