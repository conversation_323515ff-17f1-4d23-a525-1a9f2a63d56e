/**
 * 身份驗證相關的類型定義
 */

export interface User {
  id: string
  email: string
  name?: string
  [key: string]: unknown
}

export interface LoginCredentials {
  email: string
  password: string
  /** 可選的記住我參數，對應後端的 remember_me */
  remember_me?: boolean
}

export interface RegisterData {
  email: string
  password: string
  name?: string
  confirm_password?: string
}

export interface AuthResponse {
  user: User
  access_token?: string
  refresh_token?: string
  expires_in?: number
}

export interface AuthError {
  message: string
  code?: string
  field?: string
}

export interface AuthState {
  user: User | null
  isAuthenticated: boolean
  loading: boolean
  error: string | null
  ability: unknown
}

export interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<AuthResponse>
  logout: () => Promise<void>
  register: (data: RegisterData) => Promise<AuthResponse>
  fetchCurrentUser: () => Promise<User>
  refreshToken: () => Promise<{ access_token: string }>
  resetPassword: (email: string) => Promise<void>
  updatePassword: (oldPassword: string, newPassword: string) => Promise<void>
}

export interface AuthStore extends AuthState, AuthActions {
  // 組合 state 和 actions
}

export interface AuthConfig {
  baseURL: string
  loginEndpoint?: string
  logoutEndpoint?: string
  refreshTokenEndpoint?: string
  userEndpoint?: string
  tokenExpiryHandler?: () => Promise<void>
}
