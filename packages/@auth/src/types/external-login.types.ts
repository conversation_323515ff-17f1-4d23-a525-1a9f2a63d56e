// External login (OAuth2) 型別定義

export type ExternalLoginProvider = 'google' | 'line';

export interface OAuthToken {
  access_token: string;
  refresh_token?: string;
  id_token?: string;
  expires_in: number;
  [key: string]: unknown;
}

export interface OAuthUser {
  id: string;
  email?: string;
  name?: string;
  picture?: string;
  provider: ExternalLoginProvider;
  [key: string]: unknown;
}

export interface LineStatus {
  isConnected: boolean
  lineUserId?: string
  displayName?: string
  pictureUrl?: string
}

/** Google 帳號綁定狀態 */
export interface GoogleStatus {
  isConnected: boolean
  googleUserId?: string
  email?: string
  name?: string
  picture?: string
}

export interface ExternalLoginConfig {
  lineClientId?: string
  lineChannelSecret?: string
  googleClientId?: string
  googleClientSecret?: string
  redirectUri: string
}

export interface ExternalLoginCallbackData {
  code: string
  state: string
  error?: string
  errorDescription?: string
}
