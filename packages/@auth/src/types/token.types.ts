export interface TokenService {
  /**
   * 檢查是否已認證（基於 Cookie 存在與否）
   */
  isAuthenticated(): boolean

  /**
   * 從 Cookie 中讀取認證 Token（僅供前端使用，HTTP-only Cookie 無法直接讀取）
   */
  getAuthToken(): string | null

  /**
   * 解析 JWT Token 的 payload（僅用於可見的 Token，不適用於 HTTP-only Cookie）
   */
  getTokenPayload<T = TokenPayload>(token: string): T | null

  /**
   * 檢查 Token 是否過期（基於 Cookie 存在與否）
   */
  isTokenExpired(token?: string): boolean

  /**
   * 清除前端的認證相關資訊
   * 注意：HTTP-only Cookie 需要由後端清除
   */
  clearAuth(): void

  /**
   * 設置認證 Token
   * 注意：在 Cookie 認證模式下，此方法實際上不需要做任何事情
   * Token 的設置由後端通過 Set-Cookie 標頭處理
   */
  setTokens(accessToken: string, refreshToken: string): void

  /**
   * 清除所有認證相關的資訊
   * 注意：在 Cookie 認證模式下，僅清除前端可存取的資料
   */
  clearTokens(): void

  getRefreshToken(): string | null
  setRefreshToken(token: string): void
}

// 以下介面保留用於兼容性，但在 Cookie 認證模式下不會真正使用
export interface TokenState {
  accessToken: string | null
  refreshToken: string | null
  isRefreshing: boolean
}

export interface TokenResponse {
  accessToken: string
  refreshToken: string
  expiresIn?: number
}

export interface TokenPayload {
  sub: string
  email: string
  name?: string
  role?: string
  iat: number
  exp: number
  [key: string]: unknown
}