import { defineStore } from 'pinia'
import { ref, computed, readonly } from 'vue'
import type { User } from '../types/user.model'
import type { HttpServiceInterface } from '../interfaces/http-service.interface'
import type { AbilityRule } from '../types/ability.types'

/**
 * 認證狀態管理 Store
 */
export const useAuthStore = defineStore('auth', () => {
  // 狀態
  const user = ref<User | null>(null)
  const isAuthenticated = ref(false)
  const loading = ref(false)
  const error = ref<string | null>(null)
  
  // 修改為 AbilityRule[] 類型
  const ability = ref<AbilityRule[]>([])
  
  // HTTP 服務實例（透過依賴注入）
  let httpService: HttpServiceInterface | null = null

  // Getters
  const isLoggedIn = computed(() => isAuthenticated.value)
  const currentUser = computed(() => user.value)
  
  // 權限檢查函數
  const canPerformAction = (action: string, subject: string): boolean => {
    if (!ability.value || ability.value.length === 0) {
      return false
    }
    
    // 簡化的權限檢查邏輯
    // 在實際應用中，這裡應該實現更複雜的 CASL 權限檢查
    return ability.value.some(rule => {
      // 基本的 action 和 subject 匹配
      if (rule.action === action && rule.subject === subject) {
        return true
      }
      // 支援萬用字元
      if (rule.action === 'manage' && rule.subject === subject) {
        return true
      }
      if (rule.action === action && rule.subject === 'all') {
        return true
      }
      if (rule.action === 'manage' && rule.subject === 'all') {
        return true
      }
      return false
    })
  }

  // 返回 ability 實例的函數
  const getAbility = () => {
    return {
      can: (action: string, subject: string) => canPerformAction(action, subject),
      cannot: (action: string, subject: string) => !canPerformAction(action, subject),
      rules: ability.value
    }
  }

  const getUser = () => {
    return user.value
  }

  // Actions
  function setHttpService(service: HttpServiceInterface) {
    httpService = service
  }

  async function login(credentials: { email: string; password: string; remember_me?: boolean }) {
    if (!httpService) {
      throw new Error('HTTP service not initialized')
    }

    loading.value = true
    error.value = null

    try {
      // 呼叫後端登入 API
      const response = await httpService.post<{ user: User; user_type: string; ability_rules?: unknown[] }>('/auth/login', credentials)
      
      // 檢查回應格式
      if (!response.user) {
        throw new Error('登入回應中缺少使用者資訊')
      }

      // 更新狀態
      user.value = response.user
      isAuthenticated.value = true
      
      // 設定權限規則（如果有的話）
      if (response.ability_rules && Array.isArray(response.ability_rules)) {
        ability.value = response.ability_rules as AbilityRule[]
        console.log('🔐 登入成功，設置權限規則:', response.ability_rules.length, '條規則')
        // 調試用：將規則保存到全域變數
        ;(window as any)._debug_ability_rules = response.ability_rules
      } else {
        ability.value = []
        console.warn('⚠️ 登入響應中沒有 ability_rules')
      }

      return response
    } catch (err) {
      error.value = err instanceof Error ? err.message : '登入失敗'
      throw err
    } finally {
      loading.value = false
    }
  }

  async function logout() {
    if (!httpService) {
      throw new Error('HTTP service not initialized')
    }

    loading.value = true
    error.value = null

    try {
      // 呼叫後端登出 API
      await httpService.post('/auth/logout')
    } catch (err) {
      console.warn('登出 API 呼叫失敗:', err)
      // 即使 API 失敗也要清除本地狀態
    } finally {
      // 清除本地狀態
      user.value = null
      isAuthenticated.value = false
      ability.value = []
      loading.value = false
    }
  }

  async function fetchCurrentUser() {
    if (!httpService) {
      throw new Error('HTTP service not initialized')
    }

    loading.value = true
    error.value = null

    try {
      // 嘗試獲取當前用戶資訊
      const response = await httpService.get<{ user: User; ability_rules?: unknown[] }>('/auth/me')
      
      if (response.user) {
        user.value = response.user
        isAuthenticated.value = true
        
        if (response.ability_rules && Array.isArray(response.ability_rules)) {
          ability.value = response.ability_rules as AbilityRule[]
          console.log('🔐 獲取用戶資訊成功，設置權限規則:', response.ability_rules.length, '條規則')
          // 調試用：將規則保存到全域變數
          ;(window as any)._debug_ability_rules = response.ability_rules
        } else {
          ability.value = []
          console.warn('⚠️ 用戶資訊響應中沒有 ability_rules')
        }
        
        return response
      } else {
        // 沒有用戶資訊，表示未登入
        user.value = null
        isAuthenticated.value = false
        ability.value = []
      }
    } catch (err) {
      // 獲取用戶資訊失敗，可能是未登入或 token 過期
      user.value = null
      isAuthenticated.value = false
      ability.value = []
      
      // 只有在非 401 錯誤時才設定 error
      if (err instanceof Error && !err.message.includes('401')) {
        error.value = err.message
      }
    } finally {
      loading.value = false
    }
  }

  async function refreshToken() {
    if (!httpService) {
      throw new Error('HTTP service not initialized')
    }

    try {
      // 嘗試刷新 token
      const response = await httpService.post<{ user?: User; ability_rules?: unknown[] }>('/auth/refresh-token')
      
      // 如果刷新響應包含用戶資訊，直接使用
      if (response && response.user) {
        user.value = response.user
        isAuthenticated.value = true
        
        if (response.ability_rules && Array.isArray(response.ability_rules)) {
          ability.value = response.ability_rules as AbilityRule[]
        } else {
          ability.value = []
        }
      }
      
      return true
    } catch (err) {
      // 刷新失敗，清除狀態
      user.value = null
      isAuthenticated.value = false
      ability.value = []
      
      return false
    }
  }

  function clearError() {
    error.value = null
  }

  // 清除認證狀態（用於 token 過期等情況）
  function clearAuthState() {
    user.value = null
    isAuthenticated.value = false
    ability.value = []
    loading.value = false
    error.value = null
  }

  // 初始化函數
  async function initialize() {
    // 如果已經有用戶資訊並且已認證，跳過初始化
    if (isAuthenticated.value && user.value) {
      console.debug('認證初始化：用戶已登入，跳過重複初始化')
      return
    }

    // 安全地嘗試獲取當前用戶（如果已登入）
    // 不拋出錯誤，只是靜默地設定狀態
    try {
      await fetchCurrentUser()
    } catch (err) {
      // 初始化時的錯誤不應該阻止應用啟動
      console.debug('認證初始化：無法獲取當前用戶，可能未登入')
      // 確保狀態被正確設定為未認證
      user.value = null
      isAuthenticated.value = false
      ability.value = []
      error.value = null
    }
  }

  return {
    // State
    user: readonly(user),
    isAuthenticated: readonly(isAuthenticated),
    loading: readonly(loading),
    error: readonly(error),
    ability: readonly(ability),
    
    // Getters
    isLoggedIn,
    currentUser,
    
    // Actions
    setHttpService,
    login,
    logout,
    fetchCurrentUser,
    refreshToken,
    clearError,
    clearAuthState,
    initialize,
    getAbility,
    getUser
  }
})
