import type { App } from 'vue'
import { useAuthStore } from './store/auth.store'
import { createHttpService } from './services/http.service'
import type { AuthConfig } from './types/auth.types'

export async function initAuth(app: App, config: AuthConfig): Promise<void> {
  // 創建 HTTP 服務實例，配置為使用 Cookie-based 認證
  const httpService = createHttpService(config.baseURL, {
    // 設定 token 過期處理器 - 只清除本地狀態，不調用後端 API
    tokenExpiryHandler: async () => {
      const authStore = useAuthStore()
      // 直接清除本地狀態，不調用後端 logout API 避免無限循環
      authStore.clearAuthState()
      console.debug('Token 過期，已清除本地認證狀態')
    },
  })

  // 獲取 auth store 並設定 HTTP 服務
  const authStore = useAuthStore()
  authStore.setHttpService(httpService)

  // 初始化認證狀態（嘗試獲取當前用戶）
  await authStore.initialize()
} 