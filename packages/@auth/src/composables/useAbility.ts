import { ref, computed } from 'vue'

// 基本的權限規則介面
export interface AbilityRule {
  action: string
  subject: string
  conditions?: Record<string, unknown>
  fields?: string[]
}

// 權限檢查結果
export interface AbilityCheck {
  can: (action: string, subject: string, field?: string) => boolean
  cannot: (action: string, subject: string, field?: string) => boolean
  rules: AbilityRule[]
}

// 權限管理 composable
export function useAbility() {
  const rules = ref<AbilityRule[]>([])

  // 檢查是否有權限
  const can = (
    _action: string,
    _subject: string,
    _conditions?: Record<string, unknown>,
    _fields?: string[]
  ): boolean => {
    return rules.value.some(rule => {
      // 基本的 action 和 subject 匹配
      if (rule.action !== _action && rule.action !== 'manage') {
        return false
      }
      
      if (rule.subject !== _subject && rule.subject !== 'all') {
        return false
      }

      // 如果指定了 field，檢查是否在允許的 fields 中
      if (_fields && rule.fields && rule.fields.length > 0) {
        return rule.fields.includes(_fields[0])
      }

      return true
    })
  }

  // 檢查是否沒有權限
  const cannot = (action: string, subject: string, field?: string): boolean => {
    return !can(action, subject, field)
  }

  // 更新權限規則
  const updateRules = (newRules: AbilityRule[]): void => {
    rules.value = newRules
  }

  // 添加權限規則
  const addRule = (rule: AbilityRule): void => {
    rules.value.push(rule)
  }

  // 移除權限規則
  const removeRule = (action: string, subject: string): void => {
    rules.value = rules.value.filter(rule => 
      !(rule.action === action && rule.subject === subject)
    )
  }

  // 清空所有規則
  const clearRules = (): void => {
    rules.value = []
  }

  // 計算屬性：當前規則數量
  const rulesCount = computed(() => rules.value.length)

  // 計算屬性：是否有任何規則
  const hasRules = computed(() => rules.value.length > 0)

  return {
    rules: computed(() => rules.value),
    rulesCount,
    hasRules,
    can,
    cannot,
    updateRules,
    addRule,
    removeRule,
    clearRules
  }
}

/**
 * 針對特定操作的權限檢查 hook
 */
export function usePermission(
  action: string,
  subject: string,
  field?: string
) {
  const { can, cannot } = useAbility()

  const allowed = computed(() => can(action, subject, field))
  const denied = computed(() => cannot(action, subject, field))

  return {
    allowed,
    denied,
    can: () => can(action, subject, field),
    cannot: () => cannot(action, subject, field)
  }
}

/**
 * 權限檢查工廠函數
 */
export function createAbility(initialRules: AbilityRule[] = []): AbilityRule[] {
  return initialRules
}

/**
 * 權限規則建構器
 */
export class AbilityBuilder {
  private rules: AbilityRule[] = []

  can(action: string, subject: string, conditions?: Record<string, unknown>, fields?: string[]): this {
    this.rules.push({ action, subject, conditions, fields })
    return this
  }

  cannot(_action: string, _subject: string, _conditions?: Record<string, unknown>, _fields?: string[]): this {
    // 在簡化版本中，我們不實現 cannot 規則
    // 只是為了保持 API 一致性
    return this
  }

  build(): AbilityRule[] {
    return [...this.rules]
  }
}

/**
 * 權限檢查輔助函數
 */
export function checkPermission(
  rules: AbilityRule[],
  action: string,
  subject: string,
  resource?: Record<string, unknown>
): boolean {
  return rules.some(rule => {
    if (rule.action !== action && rule.action !== 'manage') {
      return false
    }
    
    if (rule.subject !== subject && rule.subject !== 'all') {
      return false
    }
    
    // 檢查條件
    if (rule.conditions && resource) {
      for (const [key, value] of Object.entries(rule.conditions)) {
        if (resource[key] !== value) {
          return false
        }
      }
    }
    
    return true
  })
}

/**
 * 權限規則序列化
 */
export function serializeRules(rules: AbilityRule[]): string {
  return JSON.stringify(rules)
}

/**
 * 權限規則反序列化
 */
export function deserializeRules(serializedRules: string): AbilityRule[] {
  try {
    const parsed = JSON.parse(serializedRules)
    return Array.isArray(parsed) ? parsed : []
  } catch {
    return []
  }
}

/**
 * 權限規則合併
 */
export function mergeRules(...ruleSets: AbilityRule[][]): AbilityRule[] {
  return ruleSets.flat()
}

/**
 * 權限規則過濾
 */
export function filterRules(
  rules: AbilityRule[],
  predicate: (rule: AbilityRule) => boolean
): AbilityRule[] {
  return rules.filter(predicate)
}

/**
 * 權限規則查找
 */
export function findRule(
  rules: AbilityRule[],
  action: string,
  subject: string
): AbilityRule | undefined {
  return rules.find(rule => 
    (rule.action === action || rule.action === 'manage') &&
    (rule.subject === subject || rule.subject === 'all')
  )
}

/**
 * 權限規則驗證
 */
export function validateRule(rule: Record<string, unknown>): rule is AbilityRule {
  return (
    typeof rule === 'object' &&
    rule !== null &&
    typeof rule.action === 'string' &&
    typeof rule.subject === 'string'
  )
}

/**
 * 權限規則清理
 */
export function cleanRules(rules: unknown[]): AbilityRule[] {
  return rules.filter(validateRule)
}

/**
 * 權限規則統計
 */
export function getRuleStats(rules: AbilityRule[]): {
  total: number
  byAction: Record<string, number>
  bySubject: Record<string, number>
} {
  const byAction: Record<string, number> = {}
  const bySubject: Record<string, number> = {}

  rules.forEach(rule => {
    byAction[rule.action] = (byAction[rule.action] || 0) + 1
    bySubject[rule.subject] = (bySubject[rule.subject] || 0) + 1
  })

  return {
    total: rules.length,
    byAction,
    bySubject
  }
}
