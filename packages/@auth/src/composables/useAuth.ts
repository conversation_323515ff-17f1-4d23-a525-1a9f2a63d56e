import { useAuthStore } from '../store/auth.store'
import type { LoginCredentials } from '../types/auth.types'

export const useAuth = () => {
  const authStore = useAuthStore()

  const login = async (credentials: LoginCredentials) => {
    return authStore.login(credentials)
  }

  const logout = async () => {
    return authStore.logout()
  }

  const fetchCurrentUser = async () => {
    return authStore.fetchCurrentUser()
  }

  const refreshToken = async () => {
    return authStore.refreshToken()
  }

  const initialize = async () => {
    return authStore.initialize()
  }

  const clearError = () => {
    return authStore.clearError()
  }

  const getAbility = () => {
    return authStore.getAbility()
  }

  const getUser = () => {
    return authStore.getUser()
  }

  return {
    // State
    user: authStore.user,
    isAuthenticated: authStore.isAuthenticated,
    loading: authStore.loading,
    error: authStore.error,
    ability: authStore.ability,
    isLoggedIn: authStore.isLoggedIn,
    currentUser: authStore.currentUser,
    
    // Actions
    login,
    logout,
    fetchCurrentUser,
    refreshToken,
    initialize,
    clearError,
    getAbility,
    getUser
  }
}
