import { ref } from 'vue'
import { ExternalLoginService } from '../services/external-login.service'
import type { LineStatus, GoogleStatus } from '../types/external-login.types'

export function useExternalLogin() {
  // LINE 解除綁定狀態
  const isLineDisconnecting = ref(false)
  const lineDisconnectError = ref<string | null>(null)
  // 檢查 LINE 綁定狀態
  const isCheckingLineStatus = ref(false)
  const checkLineStatusError = ref<string | null>(null)
  const lineStatus = ref<LineStatus | null>(null)
  // Google 解除綁定狀態
  const isGoogleDisconnecting = ref(false)
  const googleDisconnectError = ref<string | null>(null)
  // 檢查 Google 綁定狀態
  const isCheckingGoogleStatus = ref(false)
  const checkGoogleStatusError = ref<string | null>(null)
  const googleStatus = ref<GoogleStatus | null>(null)

  function loginWithGoogle(redirectUri: string, state: string, scope?: string) {
    const url = ExternalLoginService.getGoogleAuthUrl(redirectUri, state, scope)
    window.location.href = url
  }

  function loginWithLine(redirectUri: string, state: string, scope?: string) {
    const url = ExternalLoginService.getLineAuthUrl(redirectUri, state, scope)
    window.location.href = url
  }

  async function disconnectLine() {
    isLineDisconnecting.value = true
    lineDisconnectError.value = null
    try {
      await ExternalLoginService.disconnectLine()
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : '解除綁定失敗'
      lineDisconnectError.value = errorMessage
    } finally {
      isLineDisconnecting.value = false
    }
  }

  async function checkLineStatus() {
    isCheckingLineStatus.value = true
    checkLineStatusError.value = null
    try {
      lineStatus.value = await ExternalLoginService.checkLineStatus()
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : '查詢狀態失敗'
      checkLineStatusError.value = errorMessage
    } finally {
      isCheckingLineStatus.value = false
    }
  }

  // Google 解除綁定
  async function disconnectGoogle() {
    isGoogleDisconnecting.value = true
    googleDisconnectError.value = null
    try {
      await ExternalLoginService.disconnectGoogle()
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : 'Google 解除綁定失敗'
      googleDisconnectError.value = errorMessage
    } finally {
      isGoogleDisconnecting.value = false
    }
  }

  // 檢查 Google 綁定狀態
  async function checkGoogleStatus() {
    isCheckingGoogleStatus.value = true
    checkGoogleStatusError.value = null
    try {
      googleStatus.value = await ExternalLoginService.checkGoogleStatus()
    } catch (e: unknown) {
      const errorMessage = e instanceof Error ? e.message : '查詢 Google 綁定狀態失敗'
      checkGoogleStatusError.value = errorMessage
    } finally {
      isCheckingGoogleStatus.value = false
    }
  }

  return {
    // 操作函式
    loginWithGoogle,
    loginWithLine,
    disconnectLine,
    checkLineStatus,
    disconnectGoogle,
    checkGoogleStatus,
    // LINE 狀態
    isLineDisconnecting,
    lineDisconnectError,
    isCheckingLineStatus,
    checkLineStatusError,
    lineStatus,
    // Google 狀態
    isGoogleDisconnecting,
    googleDisconnectError,
    isCheckingGoogleStatus,
    checkGoogleStatusError,
    googleStatus,
  }
} 