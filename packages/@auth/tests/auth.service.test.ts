import { describe, it, expect, vi, beforeEach } from "vitest";
import { describe, it, expect, beforeEach, vi } from "vitest";
import { AuthService } from "../src/services/auth.service";
import type { HttpService } from "../src/types/http.types";
import type { TokenService } from "../src/types/token.types";
import type {
  AuthConfig,
  AuthResponse,
  User,
  LoginDto,
} from "../src/types/auth.types";

// 建立模擬服務
const createMockServices = () => {
  // 模擬 HTTP 服務
  const mockHttpService: HttpService = {
    get: vi.fn(),
    post: vi.fn(),
    put: vi.fn(),
    delete: vi.fn(),
    patch: vi.fn(),
    setBaseURL: vi.fn(),
    setAuthorizationHeader: vi.fn(),
    resetRefreshState: vi.fn(),
  };

  // 模擬 Token 服務
  const mockTokenService: TokenService = {
    isAuthenticated: vi.fn().mockReturnValue(true),
    getAuthToken: vi.fn().mockReturnValue("mock-token"),
    getTokenPayload: vi.fn(),
    isTokenExpired: vi.fn().mockReturnValue(false),
    clearAuth: vi.fn(),
    setTokens: vi.fn(),
    clearTokens: vi.fn(),
  };

  // 模擬配置
  const mockConfig: AuthConfig = {
    baseURL: "http://localhost:4000",
    loginEndpoint: "/api/auth/login",
    logoutEndpoint: "/api/auth/logout",
    refreshTokenEndpoint: "/api/auth/refresh",
    meEndpoint: "/api/auth/me",
    accessTokenKey: "mock-access-token-key",
    refreshTokenKey: "mock-refresh-token-key",
    onLoginSuccess: vi.fn(),
    onLoginError: vi.fn(),
    onLogoutSuccess: vi.fn(),
    onAuthStateChange: vi.fn(),
  };

  return {
    mockHttpService,
    mockTokenService,
    mockConfig,
  };
};

describe("AuthService", () => {
  let authService: AuthService;
  let mockHttpService: HttpService;
  let mockTokenService: TokenService;
  let mockConfig: AuthConfig;

  beforeEach(() => {
    const mocks = createMockServices();
    mockHttpService = mocks.mockHttpService;
    mockTokenService = mocks.mockTokenService;
    mockConfig = mocks.mockConfig;

    authService = new AuthService(
      mockConfig,
      mockHttpService,
      mockTokenService
    );
  });

  describe("login", () => {
    it("應該成功處理登入請求", async () => {
      // 準備測試資料
      const loginDto: LoginDto = {
        email: "<EMAIL>",
        password: "password123",
        remember_me: true,
      };

      const mockUser: User = {
        id: "user-123",
        email: "<EMAIL>",
        name: "Test User",
        role: "TENANT_USER",
        permissions: [],
      };

      const mockResponse: AuthResponse = {
        user: mockUser,
        accessToken: "mock-access-token",
        ability_rules: [],
      };

      // 設定模擬的回應
      vi.mocked(mockHttpService.post).mockResolvedValue(mockResponse);

      // 執行測試
      const result = await authService.login(loginDto);

      // 驗證結果
      expect(mockHttpService.post).toHaveBeenCalledWith(
        mockConfig.loginEndpoint,
        loginDto
      );
      expect(result).toEqual(mockResponse);
      expect(mockConfig.onLoginSuccess).toHaveBeenCalledWith(mockResponse);
      expect(mockConfig.onAuthStateChange).toHaveBeenCalledWith(true);
    });

    it("應該處理登入錯誤", async () => {
      // 準備測試資料
      const loginDto: LoginDto = {
        email: "<EMAIL>",
        password: "wrong-password",
        remember_me: false,
      };

      const mockError = new Error("Invalid credentials");
      vi.mocked(mockHttpService.post).mockRejectedValue(mockError);

      // 執行測試並驗證錯誤處理
      await expect(authService.login(loginDto)).rejects.toThrow();
      expect(mockConfig.onLoginError).toHaveBeenCalled();
    });
  });

  describe("logout", () => {
    it("應該成功處理登出請求", async () => {
      // 設定模擬的回應
      vi.mocked(mockHttpService.post).mockResolvedValue(undefined);

      // 執行測試
      await authService.logout();

      // 驗證結果
      expect(mockHttpService.post).toHaveBeenCalledWith(
        mockConfig.logoutEndpoint
      );
      expect(mockTokenService.clearAuth).toHaveBeenCalled();
      expect(mockConfig.onLogoutSuccess).toHaveBeenCalled();
      expect(mockConfig.onAuthStateChange).toHaveBeenCalledWith(false);
    });
  });

  describe("isAuthenticated", () => {
    it("應該檢查使用者是否已認證", () => {
      // 設定模擬回傳值
      vi.mocked(mockTokenService.isAuthenticated).mockReturnValue(true);
      vi.mocked(mockTokenService.isTokenExpired).mockReturnValue(false);

      // 執行測試
      const result = authService.isAuthenticated();

      // 驗證結果
      expect(result).toBe(true);
      expect(mockTokenService.isAuthenticated).toHaveBeenCalled();
      expect(mockTokenService.isTokenExpired).toHaveBeenCalled();
    });
  });

  describe("getUser", () => {
    it("應該取得使用者資訊", async () => {
      // 準備測試資料
      const mockUser: User = {
        id: "user-123",
        email: "<EMAIL>",
        name: "Test User",
        role: "TENANT_USER",
        permissions: [],
      };

      // 設定模擬的回應
      vi.mocked(mockHttpService.get).mockResolvedValue(mockUser);

      // 執行測試
      const result = await authService.getUser();

      // 驗證結果
      expect(mockHttpService.get).toHaveBeenCalledWith(mockConfig.meEndpoint);
      expect(result).toEqual(mockUser);
    });
  });

  describe("refreshToken", () => {
    it("應該成功刷新令牌", async () => {
      // 準備測試資料
      const mockResponse = {
        accessToken: "new-access-token",
      };

      // 設定模擬的回應
      vi.mocked(mockHttpService.post).mockResolvedValue(mockResponse);

      // 執行測試
      const result = await authService.refreshToken();

      // 驗證結果
      expect(mockHttpService.post).toHaveBeenCalledWith(
        mockConfig.refreshTokenEndpoint,
        {}
      );
      expect(result).toEqual(mockResponse);
    });
  });
});
