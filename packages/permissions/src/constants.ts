/**
 * HorizAI SaaS 平台 標準權限動作 (Actions) 和 主體 (Subjects) 定義
 *
 * 這是所有權限動作和主體字串的唯一真實來源 (Single Source of Truth)。
 * 所有後端 `@CheckPolicies` 或 `ability.can()` 的使用，以及前端權限檢查，
 * 都必須引用這裡定義的常數，而非硬編碼字串或使用其他地方的枚舉/常數。
 */

/**
 * 標準權限動作 (Actions)
 */
export const Actions = {
  MANAGE: "manage", // 管理所有
  CREATE: "create", // 建立資源
  READ: "read", // 讀取資源 (列表或單個)
  UPDATE: "update", // 更新資源
  DELETE: "delete", // 刪除資源
  // 其他特定動作，例如：
  INVITE: "invite", // 邀請
  REMOVE: "remove", // 移除
  EXECUTE: "execute", // 執行 (例如 AI Bot)
  SHARE: "share", // 分享
  ACCESS: "access", // 存取 (例如 管理後台)
} as const;

/**
 * 標準權限主體 (Subjects)
 * 通常對應到後端 Prisma 模型或核心業務概念
 */
export const Subjects = {
  ALL: "all", // 所有主體
  SYSTEM: "System", // 系統本身
  USER: "User", // 使用者
  TENANT: "Tenant", // 租戶
  TENANT_USER: "TenantUser", // 租戶成員 (針對租戶內的用戶管理)
  TENANT_INVITATION: "TenantInvitation", // 租戶邀請
  WORKSPACE: "Workspace", // 工作區
  WORKSPACE_MEMBER: "workspace-member", // 工作區成員 (針對工作區內的用戶管理)
  PROJECT: "Project", // 專案
  CLIENT: "Client", // 客戶
  FORM: "Form", // 表單 (例如 專案中的表單)
  PERMISSION: "Permission", // 權限定義本身
  ROLE: "Role", // 角色
  DASHBOARD: "Dashboard", // 儀表板
  DASHBOARD_STATS: "DashboardStats", // 儀表板統計
  DASHBOARD_RECENT_TENANTS: "DashboardRecentTenants", // 儀表板最近租戶
  DASHBOARD_RECENT_ORDERS: "DashboardRecentOrders", // 儀表板最近訂單
  DASHBOARD_ACTIVE_USERS: "DashboardActiveUsers", // 儀表板活躍使用者
  DASHBOARD_REVENUE: "DashboardRevenue", // 儀表板收入
  DASHBOARD_ACTIVITY: "DashboardActivity", // 儀表板活動
  ADMIN_PANEL: "AdminPanel", // 管理後台
  SYSTEM_USER: "SystemUser", // 系統用戶
  AI_MODEL: "ai_models", // AI 模型
  AI_BOT: "ai_bots", // AI Bot
  ORDER: "Order", // 訂閱訂單
  PLAN: "Plan", // 訂閱方案
  SYSTEM_SETTINGS: "SystemSettings", // 系統全域設定
  AI_KEY: "ai_keys", // AI 金鑰
  AI_FEATURE_CONFIG: "AiFeatureConfig", // AI 功能組態
  AI_GLOBAL_SETTING: "AiGlobalSetting", // AI 全域設定
  AI_USAGE_LOG: "AiUsageLog", // AI 使用日誌
  LOGIN_LOG: "LoginLog", // 登入日誌
  SYSTEM_LOG: "SystemLog", // 系統日誌
  LINE_BOT: "LineBot", // LINE 機器人
  LINE_MESSAGE_LOG: "LineMessageLog", // LINE 訊息日誌
  LINE_LOGIN_CONFIG: "LineLoginConfig", // LINE 登入設定

  // 協作功能相關
  SHARED_FILE: "SharedFile", // 共享檔案
  FILE_PERMISSION: "FilePermission", // 檔案權限
  FILE_SHARE: "FileShare", // 檔案分享
  COMMENT: "Comment", // 評論
  COMMENT_REACTION: "CommentReaction", // 評論反應
  NOTIFICATION: "Notification", // 通知
  COLLABORATION_SESSION: "CollaborationSession", // 協作會話
} as const;

/**
 * 便捷的權限組合常數 (用於減少 Backend 程式碼中的硬編碼字串)
 * 格式: {ACTION}_{SUBJECT}
 */
export const PERMISSION_KEYS = {
  // Dashboard
  READ_DASHBOARD: `${Actions.READ}:${Subjects.DASHBOARD}`,

  // Permission
  READ_PERMISSION: `${Actions.READ}:${Subjects.PERMISSION}`,
  CREATE_PERMISSION: `${Actions.CREATE}:${Subjects.PERMISSION}`,
  UPDATE_PERMISSION: `${Actions.UPDATE}:${Subjects.PERMISSION}`,
  DELETE_PERMISSION: `${Actions.DELETE}:${Subjects.PERMISSION}`,
  MANAGE_PERMISSION: `${Actions.MANAGE}:${Subjects.PERMISSION}`,

  // Role
  READ_ROLE: `${Actions.READ}:${Subjects.ROLE}`,
  CREATE_ROLE: `${Actions.CREATE}:${Subjects.ROLE}`,
  UPDATE_ROLE: `${Actions.UPDATE}:${Subjects.ROLE}`,
  DELETE_ROLE: `${Actions.DELETE}:${Subjects.ROLE}`,
  MANAGE_ROLE: `${Actions.MANAGE}:${Subjects.ROLE}`,

  // Workspace
  READ_WORKSPACE: `${Actions.READ}:${Subjects.WORKSPACE}`,
  CREATE_WORKSPACE: `${Actions.CREATE}:${Subjects.WORKSPACE}`,
  UPDATE_WORKSPACE: `${Actions.UPDATE}:${Subjects.WORKSPACE}`,
  DELETE_WORKSPACE: `${Actions.DELETE}:${Subjects.WORKSPACE}`,
  MANAGE_WORKSPACE: `${Actions.MANAGE}:${Subjects.WORKSPACE}`,

  // Tenant
  READ_TENANT: `${Actions.READ}:${Subjects.TENANT}`,
  CREATE_TENANT: `${Actions.CREATE}:${Subjects.TENANT}`,
  UPDATE_TENANT: `${Actions.UPDATE}:${Subjects.TENANT}`,
  DELETE_TENANT: `${Actions.DELETE}:${Subjects.TENANT}`,
  MANAGE_TENANT: `${Actions.MANAGE}:${Subjects.TENANT}`,

  // User
  READ_USER: `${Actions.READ}:${Subjects.USER}`,
  CREATE_USER: `${Actions.CREATE}:${Subjects.USER}`,
  UPDATE_USER: `${Actions.UPDATE}:${Subjects.USER}`,
  DELETE_USER: `${Actions.DELETE}:${Subjects.USER}`,
  MANAGE_USER: `${Actions.MANAGE}:${Subjects.USER}`,

  // AI Model
  READ_AI_MODEL: `${Actions.READ}:${Subjects.AI_MODEL}`,
  CREATE_AI_MODEL: `${Actions.CREATE}:${Subjects.AI_MODEL}`,
  UPDATE_AI_MODEL: `${Actions.UPDATE}:${Subjects.AI_MODEL}`,
  DELETE_AI_MODEL: `${Actions.DELETE}:${Subjects.AI_MODEL}`,
  MANAGE_AI_MODEL: `${Actions.MANAGE}:${Subjects.AI_MODEL}`,

  // Line Bot
  READ_LINE_BOT: `${Actions.READ}:${Subjects.LINE_BOT}`,
  CREATE_LINE_BOT: `${Actions.CREATE}:${Subjects.LINE_BOT}`,
  UPDATE_LINE_BOT: `${Actions.UPDATE}:${Subjects.LINE_BOT}`,
  DELETE_LINE_BOT: `${Actions.DELETE}:${Subjects.LINE_BOT}`,
  MANAGE_LINE_BOT: `${Actions.MANAGE}:${Subjects.LINE_BOT}`,

  // System Log
  READ_SYSTEM_LOG: `${Actions.READ}:${Subjects.SYSTEM_LOG}`,

  // Line Message Log
  READ_LINE_MESSAGE_LOG: `${Actions.READ}:${Subjects.LINE_MESSAGE_LOG}`,

  // Collaboration Features
  // Shared Files
  READ_SHARED_FILE: `${Actions.READ}:${Subjects.SHARED_FILE}`,
  CREATE_SHARED_FILE: `${Actions.CREATE}:${Subjects.SHARED_FILE}`,
  UPDATE_SHARED_FILE: `${Actions.UPDATE}:${Subjects.SHARED_FILE}`,
  DELETE_SHARED_FILE: `${Actions.DELETE}:${Subjects.SHARED_FILE}`,
  MANAGE_SHARED_FILE: `${Actions.MANAGE}:${Subjects.SHARED_FILE}`,
  SHARE_SHARED_FILE: `${Actions.SHARE}:${Subjects.SHARED_FILE}`,

  // File Permissions
  READ_FILE_PERMISSION: `${Actions.READ}:${Subjects.FILE_PERMISSION}`,
  CREATE_FILE_PERMISSION: `${Actions.CREATE}:${Subjects.FILE_PERMISSION}`,
  UPDATE_FILE_PERMISSION: `${Actions.UPDATE}:${Subjects.FILE_PERMISSION}`,
  DELETE_FILE_PERMISSION: `${Actions.DELETE}:${Subjects.FILE_PERMISSION}`,
  MANAGE_FILE_PERMISSION: `${Actions.MANAGE}:${Subjects.FILE_PERMISSION}`,

  // File Shares
  READ_FILE_SHARE: `${Actions.READ}:${Subjects.FILE_SHARE}`,
  CREATE_FILE_SHARE: `${Actions.CREATE}:${Subjects.FILE_SHARE}`,
  UPDATE_FILE_SHARE: `${Actions.UPDATE}:${Subjects.FILE_SHARE}`,
  DELETE_FILE_SHARE: `${Actions.DELETE}:${Subjects.FILE_SHARE}`,
  MANAGE_FILE_SHARE: `${Actions.MANAGE}:${Subjects.FILE_SHARE}`,

  // Comments
  READ_COMMENT: `${Actions.READ}:${Subjects.COMMENT}`,
  CREATE_COMMENT: `${Actions.CREATE}:${Subjects.COMMENT}`,
  UPDATE_COMMENT: `${Actions.UPDATE}:${Subjects.COMMENT}`,
  DELETE_COMMENT: `${Actions.DELETE}:${Subjects.COMMENT}`,
  MANAGE_COMMENT: `${Actions.MANAGE}:${Subjects.COMMENT}`,

  // Comment Reactions
  READ_COMMENT_REACTION: `${Actions.READ}:${Subjects.COMMENT_REACTION}`,
  CREATE_COMMENT_REACTION: `${Actions.CREATE}:${Subjects.COMMENT_REACTION}`,
  DELETE_COMMENT_REACTION: `${Actions.DELETE}:${Subjects.COMMENT_REACTION}`,

  // Notifications
  READ_NOTIFICATION: `${Actions.READ}:${Subjects.NOTIFICATION}`,
  CREATE_NOTIFICATION: `${Actions.CREATE}:${Subjects.NOTIFICATION}`,
  UPDATE_NOTIFICATION: `${Actions.UPDATE}:${Subjects.NOTIFICATION}`,
  DELETE_NOTIFICATION: `${Actions.DELETE}:${Subjects.NOTIFICATION}`,
  MANAGE_NOTIFICATION: `${Actions.MANAGE}:${Subjects.NOTIFICATION}`,

  // Collaboration Sessions
  READ_COLLABORATION_SESSION: `${Actions.READ}:${Subjects.COLLABORATION_SESSION}`,
  CREATE_COLLABORATION_SESSION: `${Actions.CREATE}:${Subjects.COLLABORATION_SESSION}`,
  UPDATE_COLLABORATION_SESSION: `${Actions.UPDATE}:${Subjects.COLLABORATION_SESSION}`,
  DELETE_COLLABORATION_SESSION: `${Actions.DELETE}:${Subjects.COLLABORATION_SESSION}`,
  MANAGE_COLLABORATION_SESSION: `${Actions.MANAGE}:${Subjects.COLLABORATION_SESSION}`,
} as const;

// 匯出所有常數以便於引用
export const PERMISSIONS_CONSTANTS = {
  Actions,
  Subjects,
};

// 擴展匯出
export const PERMISSIONS_CONSTANTS_EXTENDED = {
  Actions,
  Subjects,
  PERMISSION_KEYS,
};

/**
 * 從常數物件動態生成 TypeScript 類型
 */
export type Action = typeof Actions[keyof typeof Actions];
export type Subject = typeof Subjects[keyof typeof Subjects];
